/**
 * 系统商户分类管理相关常量
 */

/**
 * 分页查询默认参数
 */
export const SysMerchantCategoryPageConstants = {
  // 默认页码
  DEFAULT_PAGE: 1,
  // 默认每页大小
  DEFAULT_PAGE_SIZE: 10,
  // 最大每页大小
  MAX_PAGE_SIZE: 100,
  // 最小每页大小
  MIN_PAGE_SIZE: 1,
} as const

/**
 * 商户分类状态枚举及映射
 */
export enum SysMerchantCategoryStatus {
  // 启用
  ENABLED = 1,
  // 禁用
  DISABLED = 2,
}

export const SysMerchantCategoryStatusMap = {
  [SysMerchantCategoryStatus.ENABLED]: '启用',
  [SysMerchantCategoryStatus.DISABLED]: '禁用',
} as const

/**
 * 商户分类验证规则常量
 */
export const SysMerchantCategoryValidation = {
  // 分类名称
  CATEGORY_NAME_MIN_LENGTH: 2,
  CATEGORY_NAME_MAX_LENGTH: 20,
  CATEGORY_NAME_PATTERN: /^[\u4e00-\u9fa5a-zA-Z0-9_\s-]{2,20}$/,

  // 分类编码
  CATEGORY_CODE_MIN_LENGTH: 3,
  CATEGORY_CODE_MAX_LENGTH: 30,
  CATEGORY_CODE_PATTERN: /^[A-Z0-9_]{3,30}$/,

  // 描述
  DESCRIPTION_MAX_LENGTH: 100,

  // 备注
  REMARK_MAX_LENGTH: 200,
} as const 