/**
 * 系统用户管理相关常量
 */

/**
 * 分页查询默认参数
 */
export const SysUserPageConstants = {
  // 默认页码
  DEFAULT_PAGE: 1,
  // 默认每页大小
  DEFAULT_PAGE_SIZE: 10,
  // 最大每页大小
  MAX_PAGE_SIZE: 100,
  // 最小每页大小
  MIN_PAGE_SIZE: 1,
} as const

/**
 * 用户状态枚举及映射
 */
export enum SysUserStatus {
  // 启用
  ENABLED = 1,
  // 禁用
  DISABLED = 2,
  // 锁定
  LOCKED = 3,
}

export const SysUserStatusMap = {
  [SysUserStatus.ENABLED]: '启用',
  [SysUserStatus.DISABLED]: '禁用',
  [SysUserStatus.LOCKED]: '锁定',
} as const

/**
 * 用户性别枚举及映射
 */
export enum SysUserGender {
  // 男
  MALE = 1,
  // 女
  FEMALE = 2,
  // 未知
  UNKNOWN = 3,
}

export const SysUserGenderMap = {
  [SysUserGender.MALE]: '男',
  [SysUserGender.FEMALE]: '女',
  [SysUserGender.UNKNOWN]: '未知',
} as const

/**
 * 用户验证规则常量
 */
export const SysUserValidation = {
  // 用户名
  USERNAME_MIN_LENGTH: 3,
  USERNAME_MAX_LENGTH: 20,
  USERNAME_PATTERN: /^[a-zA-Z0-9_]{3,20}$/,

  // 密码
  PASSWORD_MIN_LENGTH: 6,
  PASSWORD_MAX_LENGTH: 20,
  PASSWORD_PATTERN: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,20}$/,

  // 手机号
  PHONE_PATTERN: /^1[3-9]\d{9}$/,

  // 邮箱
  EMAIL_PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,

  // 真实姓名
  REAL_NAME_MAX_LENGTH: 10,
  REAL_NAME_PATTERN: /^[\u4e00-\u9fa5a-zA-Z\s]{1,10}$/,
} as const
