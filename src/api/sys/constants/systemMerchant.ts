/**
 * 系统商户管理相关常量
 */

/**
 * 分页查询默认参数
 */
export const SysMerchantPageConstants = {
  // 默认页码
  DEFAULT_PAGE: 1,
  // 默认每页大小
  DEFAULT_PAGE_SIZE: 10,
  // 最大每页大小
  MAX_PAGE_SIZE: 100,
  // 最小每页大小
  MIN_PAGE_SIZE: 1,
} as const

/**
 * 商户状态枚举及映射
 */
export enum SysMerchantStatus {
  // 正常营业
  NORMAL = 1,
  // 临时关闭
  TEMP_CLOSED = 2,
  // 永久关闭
  PERM_CLOSED = 3,
}

export const SysMerchantStatusMap = {
  [SysMerchantStatus.NORMAL]: '正常营业',
  [SysMerchantStatus.TEMP_CLOSED]: '临时关闭',
  [SysMerchantStatus.PERM_CLOSED]: '永久关闭',
} as const

/**
 * 商户验证规则常量
 */
export const SysMerchantValidation = {
  // 商户名称
  MERCHANT_NAME_MIN_LENGTH: 2,
  MERCHANT_NAME_MAX_LENGTH: 50,
  MERCHANT_NAME_PATTERN: /^[\u4e00-\u9fa5a-zA-Z0-9_\s-]{2,50}$/,

  // 商户编码
  MERCHANT_CODE_MIN_LENGTH: 3,
  MERCHANT_CODE_MAX_LENGTH: 30,
  MERCHANT_CODE_PATTERN: /^[A-Z0-9_]{3,30}$/,

  // 联系电话
  PHONE_PATTERN: /^1[3-9]\d{9}$/,

  // 邮箱
  EMAIL_PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,

  // 营业执照号
  BUSINESS_LICENSE_MAX_LENGTH: 50,

  // 描述
  DESCRIPTION_MAX_LENGTH: 255,

  // 备注
  REMARK_MAX_LENGTH: 255,

  // 平台佣金比例
  COMMISSION_RATE_MIN: 0,
  COMMISSION_RATE_MAX: 1,
  COMMISSION_RATE_PATTERN: /^0(\.\d{1,4})?$|^1(\.0{1,4})?$/,
} as const 