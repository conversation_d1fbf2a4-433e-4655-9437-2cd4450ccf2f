import { http } from '@/utils/http'
import type {
  ISysMerchantRolePageRequest,
  ISysMerchantRoleListResponse,
  ISysMerchantRoleDetailResponse,
  ISysMerchantRoleCreateRequest,
  ISysMerchantRoleUpdateRequest,
  ISysMerchantRoleStatusRequest,
  ISysMerchantRoleBatchDeleteRequest,
  ISysMerchantRolePermissionRequest,
  ISysMerchantRoleBasicResponse,
  ISysMerchantRoleSelectItem,
  ISysMerchantRoleStatsResponse,
  IRolePermissionAssignmentResponse,
  ISysMerchantRoleAvailablePermissionsResponse,
  ISysMerchantRolePermissionsResponse,
  IMerchantRoleAvailablePermissionNode,
  IMerchantRolePermissionTreeNode,
  IMerchantRolePermissionNode,
} from './types'
import type { ISysMerchantAuthorizedPermissionSelectItem } from './types/merchantAuthorizedPermission'

/**
 * 分页查询商户角色列表
 * @param params 查询参数
 * @returns 返回分页商户角色列表
 */
export const getSystemMerchantRolesApi = (params?: ISysMerchantRolePageRequest) => {
  return http.get<IPageData<ISysMerchantRoleListResponse>>('/business/merchant-roles', params)
}

/**
 * 查询商户角色详情
 * @param roleId 角色ID
 * @returns 返回商户角色详情信息
 */
export const getSystemMerchantRoleDetailApi = (roleId: string) => {
  return http.get<ISysMerchantRoleDetailResponse>(`/business/merchant-roles/${roleId}`)
}

/**
 * 创建新商户角色
 * @param createRequest 创建请求参数
 * @returns 返回创建结果
 */
export const createSystemMerchantRoleApi = (createRequest: ISysMerchantRoleCreateRequest) => {
  return http.post<string>('/business/merchant-roles', createRequest)
}

/**
 * 更新商户角色信息
 * @param roleId 角色ID
 * @param updateRequest 更新请求参数
 * @returns 返回更新结果
 */
export const updateSystemMerchantRoleApi = (
  roleId: string,
  updateRequest: ISysMerchantRoleUpdateRequest,
) => {
  return http.put<string>(`/business/merchant-roles/${roleId}`, updateRequest)
}

/**
 * 删除商户角色
 * @param roleId 角色ID
 * @returns 返回删除结果
 */
export const deleteSystemMerchantRoleApi = (roleId: string) => {
  return http.delete<string>(`/business/merchant-roles/${roleId}`)
}

/**
 * 批量删除商户角色
 * @param batchDeleteRequest 批量删除请求
 * @returns 返回批量删除结果
 */
export const batchDeleteSystemMerchantRolesApi = (
  batchDeleteRequest: ISysMerchantRoleBatchDeleteRequest,
) => {
  return http.post<string>('/business/merchant-roles/batch-delete', batchDeleteRequest)
}

/**
 * 切换商户角色状态
 * @param roleId 角色ID
 * @param statusRequest 状态切换请求
 * @returns 返回切换结果
 */
export const changeMerchantRoleStatusApi = (
  roleId: string,
  statusRequest: ISysMerchantRoleStatusRequest,
) => {
  return http.put<string>(`/business/merchant-roles/${roleId}/status`, statusRequest)
}

/**
 * 获取商户角色权限列表
 * @param roleId 角色ID
 * @returns 返回角色权限列表
 */
export const getMerchantRolePermissionsApi = (roleId: string) => {
  return http.get<ISysMerchantRolePermissionsResponse>(`/business/merchant-roles/${roleId}/permissions`)
}

/**
 * 获取可分配给角色的权限列表
 * @param roleId 角色ID
 * @returns 返回可分配的权限列表
 */
export const getAvailablePermissionsForRoleApi = (roleId: string) => {
  return http.get<ISysMerchantRoleAvailablePermissionsResponse>(
    `/business/merchant-roles/${roleId}/available-permissions`
  )
}

/**
 * 为角色分配权限
 * @param roleId 角色ID
 * @param permissionRequest 权限分配请求
 * @returns 返回分配结果
 */
export const assignPermissionsToRoleApi = (
  roleId: string,
  permissionRequest: ISysMerchantRolePermissionRequest,
) => {
  return http.post<string>(`/business/merchant-roles/${roleId}/permissions/assign`, permissionRequest)
}

/**
 * 移除角色权限
 * @param roleId 角色ID
 * @param permissionRequest 权限移除请求
 * @returns 返回移除结果
 */
export const removePermissionsFromRoleApi = (
  roleId: string,
  permissionRequest: ISysMerchantRolePermissionRequest,
) => {
  return http.post<string>(`/business/merchant-roles/${roleId}/permissions/remove`, permissionRequest)
}

/**
 * 获取商户角色基础信息列表
 * @param merchantId 商户ID
 * @returns 返回商户角色基础信息列表
 */
export const getMerchantRoleBasicListApi = (merchantId: number) => {
  return http.get<ISysMerchantRoleBasicResponse[]>(`/business/merchant-roles/basic/${merchantId}`)
}

/**
 * 获取商户角色选择项
 * @param merchantId 商户ID
 * @returns 返回用于下拉选择的商户角色列表
 */
export const getMerchantRoleSelectItemsApi = (merchantId: number) => {
  return http.get<ISysMerchantRoleSelectItem[]>(`/business/merchant-roles/select/${merchantId}`)
}

/**
 * 获取商户角色统计信息
 * @param merchantId 商户ID
 * @returns 返回商户角色统计数据
 */
export const getMerchantRoleStatsApi = (merchantId: number) => {
  return http.get<ISysMerchantRoleStatsResponse>(`/business/merchant-roles/stats/${merchantId}`)
}

// 同时导出接口类型，方便外部使用
export type {
  ISysMerchantRolePageRequest,
  ISysMerchantRoleListResponse,
  ISysMerchantRoleDetailResponse,
  ISysMerchantRoleCreateRequest,
  ISysMerchantRoleUpdateRequest,
  ISysMerchantRoleStatusRequest,
  ISysMerchantRoleBatchDeleteRequest,
  ISysMerchantRolePermissionRequest,
  ISysMerchantRoleBasicResponse,
  ISysMerchantRoleSelectItem,
  ISysMerchantRoleStatsResponse,
  IRolePermissionAssignmentResponse,
  ISysMerchantRoleAvailablePermissionsResponse,
  ISysMerchantRolePermissionsResponse,
  IMerchantRoleAvailablePermissionNode,
  IMerchantRolePermissionTreeNode,
  IMerchantRolePermissionNode,
} 