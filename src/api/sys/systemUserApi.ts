import { http } from '@/utils/http'
import type {
  ISysUserPageRequest,
  ISysUserListResponse,
  ISysUserDetailResponse,
  ISysUserCreateRequest,
  ISysUserUpdateRequest,
  ISysUserStatusRequest,
  ISysUserResetPasswordRequest,
  ISysUserBatchDeleteRequest,
  ISysUserCheckUsernameRequest,
  ISysUserCheckEmailRequest,
  ISysUserCheckPhoneRequest,
  ISysUnassignedUserCountResponse,
} from './types'

/**
 * 分页查询用户列表
 * @param params 查询参数
 * @returns 返回分页用户列表
 */
export const getSystemUsersApi = (params?: ISysUserPageRequest) => {
  return http.get<IPageData<ISysUserListResponse>>('/system/users', params)
}
/**
 * 分页查询未分配角色的用户列表
 * @param params 查询参数
 * @returns 返回分页用户列表
 */
export const getSystemUnassignedRoleUsersApi = (params?: ISysUserPageRequest) => {
  return http.get<IPageData<ISysUserListResponse>>('/system/users/unassigned', params)
}

/**
 * 查询未分配角色的用户数量
 * @param params 查询参数
 * @returns 返回未分配角色的用户数量
 */
export const getUnassignedUserCountApi = (params?: ISysUserPageRequest) => {
  return http.get<ISysUnassignedUserCountResponse>(
    '/system/users/unassigned/count',
    params,
  )
}

/**
 * 查询用户详情
 * @param userId 用户ID
 * @returns 返回用户详情信息
 */
export const getSystemUserDetailApi = (userId: string) => {
  return http.get<ISysUserDetailResponse>(`/system/users/${userId}`)
}

/**
 * 创建新用户
 * @param createRequest 创建请求参数
 * @returns 返回创建结果
 */
export const createSystemUserApi = (createRequest: ISysUserCreateRequest) => {
  return http.post<string>('/system/users', createRequest)
}

/**
 * 更新用户信息
 * @param userId 用户ID
 * @param updateRequest 更新请求参数
 * @returns 返回更新结果
 */
export const updateSystemUserApi = (userId: string, updateRequest: ISysUserUpdateRequest) => {
  return http.put<string>(`/system/users/${userId}`, updateRequest)
}

/**
 * 删除用户
 * @param userId 用户ID
 * @returns 返回删除结果
 */
export const deleteSystemUserApi = (userId: string) => {
  return http.delete<string>(`/system/users/${userId}`)
}

/**
 * 批量删除用户
 * @param batchDeleteRequest 批量删除请求
 * @returns 返回批量删除结果
 */
export const batchDeleteSystemUsersApi = (batchDeleteRequest: ISysUserBatchDeleteRequest) => {
  return http.delete<string>('/system/users/batch', batchDeleteRequest)
}

/**
 * 切换用户状态
 * @param userId 用户ID
 * @param statusRequest 状态切换请求
 * @returns 返回切换结果
 */
export const changeUserStatusApi = (userId: string, statusRequest: ISysUserStatusRequest) => {
  return http.put<string>(`/system/users/${userId}/status`, statusRequest)
}

/**
 * 重置用户密码
 * @param userId 用户ID
 * @param resetPasswordRequest 重置密码请求
 * @returns 返回重置结果
 */
export const resetUserPasswordApi = (
  userId: string,
  resetPasswordRequest: ISysUserResetPasswordRequest,
) => {
  return http.put<string>(`/system/users/${userId}/reset-password`, resetPasswordRequest)
}

/**
 * 检查用户名是否存在
 * @param params 检查参数
 * @returns 返回是否存在
 */
export const checkUsernameExistsApi = (params: ISysUserCheckUsernameRequest) => {
  return http.get<boolean>('/system/users/check-username', params)
}

/**
 * 检查邮箱是否存在
 * @param params 检查参数
 * @returns 返回是否存在
 */
export const checkEmailExistsApi = (params: ISysUserCheckEmailRequest) => {
  return http.get<boolean>('/system/users/check-email', params)
}

/**
 * 检查手机号是否存在
 * @param params 检查参数
 * @returns 返回是否存在
 */
export const checkPhoneExistsApi = (params: ISysUserCheckPhoneRequest) => {
  return http.get<boolean>('/system/users/check-phone', params)
}

// 同时导出接口类型，方便外部使用
export type {
  ISysUserPageRequest,
  ISysUserListResponse,
  ISysUserDetailResponse,
  ISysUserCreateRequest,
  ISysUserUpdateRequest,
  ISysUserStatusRequest,
  ISysUserResetPasswordRequest,
  ISysUserBatchDeleteRequest,
  ISysUserCheckUsernameRequest,
  ISysUserCheckEmailRequest,
  ISysUserCheckPhoneRequest,
}
