import { http } from '@/utils/http'

// ==================== 类型定义 ====================

// 商户授权权限列表响应
export interface IMerchantAuthorizedPermissionListResponse {
  id: string
  merchant_id: number
  merchant_name?: string
  permission_template_id: string
  permission_name?: string
  permission_code?: string
  permission_type?: number
  permission_type_desc: string
  status: number
  status_desc: string
  authorized_date: string
  authorized_by_name?: string
  remark?: string
}

// 商户授权权限详情响应
export interface IMerchantAuthorizedPermissionDetailResponse {
  id: string
  merchant_id: number
  merchant_name?: string
  permission_template_id: string
  permission_name?: string
  permission_code?: string
  permission_type?: number
  permission_type_desc: string
  permission_description?: string
  status: number
  status_desc: string
  authorized_date: string
  created_by: string
  authorized_by_name?: string
  updated_by?: string
  updated_by_name?: string
  remark?: string
}

// 商户授权权限树形节点
export interface IMerchantAuthorizedPermissionTreeNode {
  id: string
  permission_name: string
  permission_code: string
  permission_type: number
  permission_type_desc: string
  parent_id?: string
  order_num: number
  is_authorized: boolean
  authorized_permission_id?: string
  authorized_date?: string
  status?: number
  status_desc?: string
  children: IMerchantAuthorizedPermissionTreeNode[]
}

// 商户授权权限树形结构响应
export interface IMerchantAuthorizedPermissionTreeResponse {
  merchant_id: number
  merchant_name: string
  tree: IMerchantAuthorizedPermissionTreeNode[]
}

// 商户权限统计响应
export interface IMerchantPermissionStatsResponse {
  merchant_id: number
  merchant_name: string
  total_permissions: number
  authorized_permissions: number
  active_permissions: number
  suspended_permissions: number
}

// 商户授权权限选择项
export interface IMerchantAuthorizedPermissionSelectItem {
  id: string
  permission_template_id: string
  permission_name?: string
  permission_code?: string
  status: number
}

// 系统权限模板选择项
export interface ISystemPermissionTemplateSelectItem {
  id: string
  permission_name: string
  permission_code: string
  permission_type: number
  parent_id?: string
  visible: number
}

// 系统权限模板树形节点
export interface ISystemPermissionTemplateTreeNode {
  id: string
  label: string
  permission_code: string
  parent_id?: string
  permission_type: number
  visible: number
  icon?: string
  order_num: number
  children: ISystemPermissionTemplateTreeNode[]
}

// 商户授权权限分页查询请求
export interface IMerchantAuthorizedPermissionPageRequest {
  merchant_id?: number
  permission_template_id?: string
  status?: number
  page?: number
  page_size?: number
  authorized_start?: string
  authorized_end?: string
  created_start?: string
  created_end?: string
}

// 商户授权权限创建请求
export interface IMerchantAuthorizedPermissionCreateRequest {
  merchant_id: number
  permission_template_ids: string[]
  status: number
  authorized_date?: string
  remark?: string
}

// 商户授权权限更新请求
export interface IMerchantAuthorizedPermissionUpdateRequest {
  id: string
  status: number
  authorized_date?: string
  remark?: string
}

// 商户授权权限状态切换请求
export interface IMerchantAuthorizedPermissionStatusRequest {
  status: number
}

// 商户权限批量授权请求
export interface IMerchantPermissionBatchAuthorizeRequest {
  merchant_id: number
  permission_template_ids: string[]
  override_existing: boolean
  remark?: string
}

// 商户权限撤销请求
export interface IMerchantPermissionRevokeRequest {
  merchant_id: number
  permission_template_ids: string[]
  revoke_reason?: string
}

// 商户权限复制请求
export interface IMerchantPermissionCopyRequest {
  source_merchant_id: number
  target_merchant_ids: number[]
  override_existing: boolean
  remark?: string
}

// 商户授权权限批量删除请求
export interface IMerchantAuthorizedPermissionBatchDeleteRequest {
  authorized_permission_ids: string[]
}

// ==================== API 函数 ====================

/**
 * 分页查询商户授权权限列表
 */
export const getMerchantAuthorizedPermissionsApi = (params?: IMerchantAuthorizedPermissionPageRequest) => {
  return http.get<{
    items: IMerchantAuthorizedPermissionListResponse[]
    total: number
    page: number
    page_size: number
    total_pages: number
  }>('/business/merchant-authorized-permissions', { params })
}

/**
 * 查询商户授权权限详情
 */
export const getMerchantAuthorizedPermissionByIdApi = (authorizedPermissionId: string) => {
  return http.get<IMerchantAuthorizedPermissionDetailResponse>(
    `/business/merchant-authorized-permissions/${authorizedPermissionId}`
  )
}

/**
 * 获取商户授权权限树形结构
 */
export const getMerchantAuthorizedPermissionTreeApi = (merchantId: number) => {
  return http.get<IMerchantAuthorizedPermissionTreeResponse>(
    `/business/merchant-authorized-permissions/tree/${merchantId}`
  )
}

/**
 * 获取商户权限统计信息
 */
export const getMerchantPermissionStatsApi = (merchantId: number) => {
  return http.get<IMerchantPermissionStatsResponse>(
    `/business/merchant-authorized-permissions/stats/${merchantId}`
  )
}

/**
 * 获取商户授权权限选择项
 */
export const getMerchantAuthorizedPermissionSelectItemsApi = (merchantId: number) => {
  return http.get<IMerchantAuthorizedPermissionSelectItem[]>(
    `/business/merchant-authorized-permissions/select/${merchantId}`
  )
}

/**
 * 获取系统权限模板选择列表
 */
export const getPermissionTemplateSelectForAuthorizeApi = () => {
  return http.get<ISystemPermissionTemplateSelectItem[]>(
    '/business/merchant-authorized-permissions/permission-templates/select'
  )
}

/**
 * 获取系统权限模板树形选择列表
 */
export const getPermissionTemplateTreeForAuthorizeApi = () => {
  return http.get<ISystemPermissionTemplateTreeNode[]>(
    '/system/permission-templates/tree'
  )
}

/**
 * 创建商户授权权限
 */
export const createMerchantAuthorizedPermissionApi = (data: IMerchantAuthorizedPermissionCreateRequest) => {
  return http.post<string>('/business/merchant-authorized-permissions', data)
}

/**
 * 更新商户授权权限
 */
export const updateMerchantAuthorizedPermissionApi = (
  authorizedPermissionId: string,
  data: IMerchantAuthorizedPermissionUpdateRequest
) => {
  return http.put<string>(`/business/merchant-authorized-permissions/${authorizedPermissionId}`, data)
}

/**
 * 删除商户授权权限
 */
export const deleteMerchantAuthorizedPermissionApi = (authorizedPermissionId: string) => {
  return http.delete<string>(`/business/merchant-authorized-permissions/${authorizedPermissionId}`)
}

/**
 * 切换商户授权权限状态
 */
export const changeMerchantAuthorizedPermissionStatusApi = (
  authorizedPermissionId: string,
  data: IMerchantAuthorizedPermissionStatusRequest
) => {
  return http.put<string>(
    `/business/merchant-authorized-permissions/${authorizedPermissionId}/status`,
    data
  )
}

/**
 * 商户权限批量授权
 */
export const batchAuthorizeMerchantPermissionsApi = (data: IMerchantPermissionBatchAuthorizeRequest) => {
  return http.post<string>('/business/merchant-authorized-permissions/batch-authorize', data)
}

/**
 * 商户权限撤销
 */
export const revokeMerchantPermissionsApi = (data: IMerchantPermissionRevokeRequest) => {
  return http.post<string>('/business/merchant-authorized-permissions/revoke', data)
}

/**
 * 商户权限复制
 */
export const copyMerchantPermissionsApi = (data: IMerchantPermissionCopyRequest) => {
  return http.post<string>('/business/merchant-authorized-permissions/copy', data)
}

/**
 * 批量删除商户授权权限
 */
export const batchDeleteMerchantAuthorizedPermissionsApi = (data: IMerchantAuthorizedPermissionBatchDeleteRequest) => {
  return http.post<string>('/business/merchant-authorized-permissions/batch-delete', data)
} 