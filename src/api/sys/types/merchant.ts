/**
 * 地理位置数据结构
 */
export interface ILocation {
  // 经度
  longitude: number
  // 纬度
  latitude: number
}

/**
 * 商户分页查询请求参数
 */
export interface ISysMerchantPageRequest {
  // 商户名称
  merchant_name?: string | null
  // 商户编码
  merchant_code?: string | null
  // 商户分类ID
  category_id?: string | null
  // 联系电话
  phone?: string | null
  // 邮箱地址
  email?: string | null
  // 商户状态（1正常营业 2临时关闭 3永久关闭）
  status?: number | null
  // 当前页码
  page?: number | null
  // 每页大小
  page_size?: number | null
  // 创建开始时间
  created_start?: string | null
  // 创建结束时间
  created_end?: string | null
}

/**
 * 商户列表响应VO
 */
export interface ISysMerchantListResponse {
  // 商户ID
  id: number
  // 商户名称
  merchant_name: string
  // 商户头像
  avatar: string
  // 商户编码
  merchant_code: string
  // 分类ID
  category_id?: string | null
  // 分类名称
  category_name?: string | null
  // 联系电话
  phone?: string | null
  // 邮箱地址
  email?: string | null
  // 详细地址
  address?: string | null
  // 平台佣金比例
  platform_commission_rate?: string | null
  // 状态（1正常营业 2临时关闭 3永久关闭）
  status: number
  // 状态描述
  status_desc: string
  // 创建时间
  created_date: string
  // 更新时间
  updated_date: string
}

/**
 * 商户详情响应VO
 */
export interface ISysMerchantDetailResponse {
  // 商户ID
  id: number
  // 商户名称
  merchant_name: string
  // 商户编码
  merchant_code: string
  // 分类ID
  category_id?: string | null
  // 分类名称
  category_name?: string | null
  // 联系电话
  phone?: string | null
  // 邮箱地址
  email?: string | null
  // 详细地址
  address?: string | null
  // 经纬度坐标
  location?: ILocation | null
  // 营业执照号
  business_license?: string | null
  // 营业执照照片URL
  license_photo?: string | null
  // 商户头像URL
  avatar?: string | null
  // 商户描述
  description?: string | null
  // 平台佣金比例
  platform_commission_rate?: string | null
  // 状态（1正常营业 2临时关闭 3永久关闭）
  status: number
  // 状态描述
  status_desc: string
  // 自动结算日期
  auto_clear_date?: number | null
  // 排序字段
  sort_order?: number | null
  // 创建时间
  created_date: string
  // 更新时间
  updated_date: string
  // 创建人ID
  created_by?: string | null
  // 更新人ID
  updated_by?: string | null
  // 备注
  remark?: string | null
}

/**
 * 商户创建请求DTO
 */
export interface ISysMerchantCreateRequest {
  // 商户名称
  merchant_name: string
  // 商户编码
  merchant_code: string
  // 商户分类ID
  category_id?: string | null
  // 联系电话
  phone?: string | null
  // 邮箱地址
  email?: string | null
  // 经营地址
  address?: string | null
  // 地理位置
  location?: ILocation | null
  // 营业执照号
  business_license?: string | null
  // 营业执照照片URL
  license_photo?: string | null
  // 商户头像URL
  avatar?: string | null
  // 商户描述
  description?: string | null
  // 平台抽成比例(字符串格式，如"0.03")
  platform_commission_rate?: string | null
  // 商户状态（1正常营业 2临时关闭 3永久关闭）
  status: number
  // 账单自动清零日期(YYYY-MM-DD格式)
  auto_clear_date?: string | null
  // 排序字段
  sort_order?: number | null
  // 备注
  remark?: string | null
}

/**
 * 商户更新请求DTO
 */
export interface ISysMerchantUpdateRequest {
  // 商户名称
  merchant_name: string
  // 商户编码
  merchant_code: string
  // 商户分类ID
  category_id?: string | null
  // 联系电话
  phone?: string | null
  // 邮箱地址
  email?: string | null
  // 经营地址
  address?: string | null
  // 地理位置
  location?: ILocation | null
  // 营业执照号
  business_license?: string | null
  // 营业执照照片URL
  license_photo?: string | null
  // 商户头像URL
  avatar?: string | null
  // 商户描述
  description?: string | null
  // 平台抽成比例(字符串格式，如"0.03")
  platform_commission_rate?: string | null
  // 商户状态（1正常营业 2临时关闭 3永久关闭）
  status: number
  // 账单自动清零日期(MM-DD格式)
  auto_clear_date?: string | null
  // 排序字段
  sort_order?: number | null
  // 备注
  remark?: string | null
}

/**
 * 商户批量删除请求DTO
 */
export interface ISysMerchantBatchDeleteRequest {
  // 商户ID列表
  merchant_ids: number[]
}

/**
 * 商户状态切换请求DTO
 */
export interface ISysMerchantStatusRequest {
  // 状态（1正常营业 2临时关闭 3永久关闭）
  status: number
}

/**
 * 商户佣金设置请求DTO
 */
export interface ISysMerchantCommissionRequest {
  // 平台佣金比例（0-1之间的小数）
  platform_commission_rate?: string | null
  // 跟进人佣金比例（0-1之间的小数）
  follower_commission_rate?: string | null
}

/**
 * 商户基础信息响应VO (用于简单查询)
 */
export interface ISysMerchantBasicResponse {
  // 商户ID
  id: number
  // 商户名称
  merchant_name: string
  // 商户编码
  merchant_code: string
  // 联系人
  contact_person?: string | null
  // 联系电话
  contact_phone?: string | null
  // 商户等级
  merchant_level?: number | null
  // 商户等级描述
  merchant_level_desc?: string | null
  // 状态
  status: number
  // 状态描述
  status_desc: string
  // 经营地址
  business_address?: string | null
}

/**
 * 商户选择项VO (用于下拉选择)
 */
export interface ISysMerchantSelectItem {
  // 商户ID
  id: number
  // 商户名称
  merchant_name: string
  // 商户编码
  merchant_code: string
  // 联系人
  contact_person?: string | null
  // 联系电话
  contact_phone?: string | null
  // 商户等级
  merchant_level?: number | null
  // 状态
  status: number
}

/**
 * 商户分类统计VO
 */
export interface ISysMerchantCategoryStats {
  // 分类ID
  category_id?: string | null
  // 分类名称
  category_name?: string | null
  // 商户数量
  merchant_count: number
}

/**
 * 商户统计信息响应VO
 */
export interface ISysMerchantStatsResponse {
  // 商户总数
  total_count: number
  // 启用商户数
  active_count: number
  // 禁用商户数
  disabled_count: number
  // 冻结商户数
  frozen_count: number
  // 按分类统计
  by_category: ISysMerchantCategoryStats[]
}

/**
 * 商户位置响应VO
 */
export interface ISysMerchantLocationResponse {
  // 商户ID
  merchant_id: number
  // 商户名称
  merchant_name: string
  // 详细地址
  address?: string | null
  // 经纬度坐标
  location?: ILocation | null
}

/// 获取商户基础信息列表请求参数
export interface ISysMerchantListRequest {
  /// 商户名称
  merchant_name: string
}
