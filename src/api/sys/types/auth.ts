import { ISysRoleSimpleResponse } from './role'

/**
 * 登录请求参数
 */
export interface ILoginForm {
  // 用户名
  username: string
  // 密码
  password: string
}

/**
 * 登录接口返回数据
 */
export interface ILoginData {
  // 短token，用于请求资源
  access_token: string
  // 长token，用于短token过期，使用长token交换token
  refresh_token: string
}

/**
 * 刷新token请求参数
 */
export interface IRefreshTokenForm {
  // 刷新token
  refresh_token: string
}

/**
 * 刷新token返回数据
 */
export interface IRefreshTokenData {
  // 新的访问token
  access_token: string
  // 新的刷新token
  refresh_token: string
  // 过期时间（秒）
  expires_in: number
}

/**
 * 修改密码请求参数
 */
export interface IChangePasswordForm {
  // 旧密码
  old_password: string
  // 新密码
  new_password: string
}

/**
 * 用户个人信息
 */
export interface IUserProfile {
  // 用户ID
  id: string
  // 用户名
  username: string
  // 真实姓名
  real_name?: string | null
  // 手机号
  phone?: string | null
  // 邮箱
  email?: string | null
  // 头像地址
  avatar?: string | null
  // 性别 1:男 2:女 3:未知
  gender?: number | null
  // 状态 1:启用 2:禁用 3:锁定
  status: number
  // 最后登录时间
  last_login_date?: string | null
  // 最后登录IP
  last_login_ip?: string | null
  // 权限代码集合
  permissions: string[]
  // 角色信息简单集合
  roles: ISysRoleSimpleResponse[]
}

/**
 * 更新个人信息请求参数
 */
export interface IUpdateProfileForm {
  // 真实姓名
  real_name?: string | null
  // 手机号
  phone?: string | null
  // 邮箱
  email?: string | null
  // 头像地址
  avatar?: string | null
  // 性别 1:男 2:女 3:未知
  gender?: number | null
}

/**
 * 微信登录请求参数
 */
export interface IWechatLoginForm {
  // 微信授权码
  code: string
}

/**
 * 微信登录返回数据
 */
export interface IWechatLoginData {
  // 短token，用于请求资源
  access_token: string
  // 长token，用于短token过期，使用长token交换token
  refresh_token: string
}
