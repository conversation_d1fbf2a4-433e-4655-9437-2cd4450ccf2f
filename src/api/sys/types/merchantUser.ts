/**
 * 商户用户分页查询请求参数
 */
export interface ISysMerchantUserPageRequest {
  // 当前页码 (1开始)
  page?: number | null
  // 每页大小
  page_size?: number | null
  // 用户名关键字搜索
  username?: string | null
  // 真实姓名关键字搜索
  real_name?: string | null
  // 邮箱关键字搜索
  email?: string | null
  // 手机号关键字搜索
  phone?: string | null
  // 状态过滤 1:启用 2:禁用 3:锁定
  status?: number | null
  // 性别过滤 1:男 2:女 3:未知
  gender?: number | null
  // 创建时间开始 (YYYY-MM-DD格式)
  created_start?: string | null
  // 创建时间结束 (YYYY-MM-DD格式)
  created_end?: string | null
  // 搜索关键词（用户名、真实姓名、手机号）
  keyword?: string | null
}

/**
 * 商户用户列表响应VO (用于分页查询)
 */
export interface ISysMerchantUserListResponse {
  // 用户ID
  id: string
  // 用户名
  username: string
  // 真实姓名
  real_name: string
  // 手机号 (脱敏)
  phone: string
  // 邮箱 (脱敏)
  email?: string | null
  // 头像地址
  avatar?: string | null
  // 性别 1:男 2:女 3:未知
  gender?: number | null
  // 性别描述
  gender_desc: string
  // 状态 1:启用 2:禁用 3:锁定
  status: number
  // 状态描述
  status_desc: string
  // 最后登录时间
  last_login_date?: string | null
  // 最后登录IP
  last_login_ip?: string | null
  // 创建时间
  created_date: string
  // 更新时间
  updated_date: string
}

/**
 * 商户用户角色信息VO
 */
export interface ISysMerchantUserRoleInfo {
  // 角色ID
  role_id: string
  // 角色名称
  role_name: string
  // 角色编码
  role_code?: string | null
  // 角色类型 1:管理员角色 2:自定义角色
  role_type: number
  // 角色类型描述
  role_type_desc: string
  // 佣金比例
  commission_rate?: string | null
  // 状态 0:禁用 1:启用
  status: number
  // 状态描述
  status_desc: string
}

/**
 * 商户用户详情响应VO (包含完整信息)
 */
export interface ISysMerchantUserDetailResponse {
  // 用户ID
  id: string
  // 用户名
  username: string
  // 真实姓名
  real_name: string
  // 手机号
  phone: string
  // 邮箱
  email?: string | null
  // 头像地址
  avatar?: string | null
  // 性别 1:男 2:女 3:未知
  gender?: number | null
  // 性别描述
  gender_desc: string
  // 身份证号（脱敏）
  id_card?: string | null
  // 状态 1:启用 2:禁用 3:锁定
  status: number
  // 状态描述
  status_desc: string
  // 最后登录时间
  last_login_date?: string | null
  // 最后登录IP
  last_login_ip?: string | null
  // 创建时间
  created_date: string
  // 更新时间
  updated_date: string
  // 创建人ID
  created_by?: string | null
  // 更新人ID
  updated_by?: string | null
  // 备注
  remark?: string | null
  // 用户角色列表
  roles: ISysMerchantUserRoleInfo[]
}

/**
 * 商户用户创建请求DTO
 */
export interface ISysMerchantUserCreateRequest {
  // 用户名
  username: string
  // 密码
  password: string
  // 真实姓名
  real_name: string
  // 手机号
  phone: string
  // 邮箱
  email?: string | null
  // 头像地址
  avatar?: string | null
  // 性别 1:男 2:女 3:未知
  gender: number
  // 身份证号码
  id_card?: string | null
  // 状态 1:启用 2:禁用 3:锁定
  status: number
  // 备注
  remark?: string | null
}

/**
 * 商户用户更新请求DTO
 */
export interface ISysMerchantUserUpdateRequest {
  // 用户名
  username: string
  // 真实姓名
  real_name: string
  // 手机号
  phone: string
  // 邮箱
  email?: string | null
  // 头像地址
  avatar?: string | null
  // 性别 1:男 2:女 3:未知
  gender: number
  // 身份证号码
  id_card?: string | null
  // 状态 1:启用 2:禁用 3:锁定
  status: number
  // 备注
  remark?: string | null
}

/**
 * 商户用户状态切换请求DTO
 */
export interface ISysMerchantUserStatusRequest {
  // 状态 1:启用 2:禁用 3:锁定
  status: number
}

/**
 * 商户用户重置密码请求DTO
 */
export interface ISysMerchantUserResetPasswordRequest {
  // 新密码
  new_password: string
}

/**
 * 批量删除商户用户请求DTO
 */
export interface ISysMerchantUserBatchDeleteRequest {
  // 用户ID集合
  user_ids: string[]
}

/**
 * 商户用户角色分配请求DTO
 */
export interface ISysMerchantUserRoleAssignRequest {
  // 商户ID
  merchant_id: number
  // 用户ID
  user_id: string
  // 角色ID列表（重新分配，覆盖原有角色）
  role_ids: string[]
  // 佣金比例(字符串格式，如"0.01")
  commission_rate?: string | null
  // 备注
  remark?: string | null
}

/**
 * 检查用户名是否存在请求参数
 */
export interface ISysMerchantUserCheckUsernameRequest {
  // 要检查的用户名
  username: string
  // 排除的用户ID（用于更新时检查）
  exclude_id?: string
}

/**
 * 检查邮箱是否存在请求参数
 */
export interface ISysMerchantUserCheckEmailRequest {
  // 要检查的邮箱
  email: string
  // 排除的用户ID（用于更新时检查）
  exclude_id?: string
}

/**
 * 检查手机号是否存在请求参数
 */
export interface ISysMerchantUserCheckPhoneRequest {
  // 要检查的手机号
  phone: string
  // 排除的用户ID（用于更新时检查）
  exclude_id?: string
}

/**
 * 检查真实姓名是否存在请求参数
 */
export interface ISysMerchantUserCheckRealNameRequest {
  // 要检查的真实姓名
  real_name: string
  // 排除的用户ID（用于更新时检查）
  exclude_id?: string
}

/**
 * 商户用户基础信息响应VO (用于简单查询)
 */
export interface ISysMerchantUserBasicResponse {
  // 用户ID
  id: string
  // 用户名
  username: string
  // 真实姓名
  real_name: string
  // 手机号
  phone: string
  // 状态 1:启用 2:禁用 3:锁定
  status: number
  // 状态描述
  status_desc: string
}

/**
 * 商户用户选择项VO (用于下拉选择)
 */
export interface ISysMerchantUserSelectItem {
  // 用户ID
  id: string
  // 用户名
  username: string
  // 真实姓名
  real_name: string
  // 手机号
  phone: string
  // 状态 1:启用 2:禁用 3:锁定
  status: number
}

/**
 * 商户用户性别统计VO
 */
export interface ISysMerchantUserGenderStats {
  // 性别 1:男 2:女 3:未知
  gender?: number | null
  // 性别描述
  gender_desc: string
  // 用户数量
  user_count: number
}

/**
 * 商户用户统计信息响应VO
 */
export interface ISysMerchantUserStatsResponse {
  // 用户总数
  total_count: number
  // 启用用户数
  active_count: number
  // 禁用用户数
  disabled_count: number
  // 锁定用户数
  locked_count: number
  // 按性别统计
  by_gender: ISysMerchantUserGenderStats[]
} 