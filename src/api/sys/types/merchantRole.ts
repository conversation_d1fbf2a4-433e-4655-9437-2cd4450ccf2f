/**
 * 商户角色分页查询请求参数
 */
export interface ISysMerchantRolePageRequest {
  // 当前页码 (1开始)
  page?: number | null
  // 每页大小
  page_size?: number | null
  // 商户ID
  merchant_id?: number | null
  // 角色编码
  role_code?: string | null
  // 角色名称
  role_name?: string | null
  // 角色类型（1管理员角色 2自定义角色）
  role_type?: number | null
  // 是否默认角色
  is_default?: boolean | null
  // 数据范围（1商户全部数据 2个人数据）
  data_scope?: number | null
  // 角色状态（0禁用 1启用）
  status?: number | null
  // 创建时间开始 (YYYY-MM-DD格式)
  created_start?: string | null
  // 创建时间结束 (YYYY-MM-DD格式)
  created_end?: string | null
}

/**
 * 商户角色列表响应VO (用于分页查询)
 */
export interface ISysMerchantRoleListResponse {
  // 角色ID
  id: string
  // 商户ID
  merchant_id: number
  // 商户名称
  merchant_name?: string | null
  // 角色编码
  role_code?: string | null
  // 角色名称
  role_name: string
  // 角色类型
  role_type: number
  // 角色类型描述
  role_type_desc: string
  // 是否默认角色
  is_default: boolean
  // 数据范围
  data_scope: number
  // 数据范围描述
  data_scope_desc: string
  // 角色描述
  role_description?: string | null
  // 角色状态
  status: number
  // 角色状态描述
  status_desc: string
  // 关联用户数量
  user_count?: number | null
  // 关联权限数量
  permission_count?: number | null
  // 创建时间
  created_date: string
  // 更新时间
  updated_date: string
}

/**
 * 商户角色详情响应VO (包含完整信息)
 */
export interface ISysMerchantRoleDetailResponse {
  // 角色ID
  id: string
  // 商户ID
  merchant_id: number
  // 商户名称
  merchant_name?: string | null
  // 角色编码
  role_code?: string | null
  // 角色名称
  role_name: string
  // 角色类型
  role_type: number
  // 角色类型描述
  role_type_desc: string
  // 是否默认角色
  is_default: boolean
  // 数据范围
  data_scope: number
  // 数据范围描述
  data_scope_desc: string
  // 角色描述
  role_description?: string | null
  // 角色状态
  status: number
  // 角色状态描述
  status_desc: string
  // 创建时间
  created_date: string
  // 更新时间
  updated_date: string
  // 创建人ID
  created_by?: string | null
  // 更新人ID
  updated_by?: string | null
  // 创建人姓名
  created_by_name?: string | null
  // 更新人姓名
  updated_by_name?: string | null
  // 备注
  remark?: string | null
}

/**
 * 商户角色创建请求DTO
 */
export interface ISysMerchantRoleCreateRequest {
  // 商户ID
  merchant_id: number
  // 角色编码
  role_code?: string | null
  // 角色名称
  role_name: string
  // 角色类型（1管理员角色 2自定义角色）
  role_type: number
  // 是否默认角色
  is_default: boolean
  // 数据范围（1商户全部数据 2个人数据）
  data_scope: number
  // 角色描述
  role_description?: string | null
  // 角色状态（0禁用 1启用）
  status: number
  // 备注
  remark?: string | null
}

/**
 * 商户角色更新请求DTO
 */
export interface ISysMerchantRoleUpdateRequest {
  // 角色编码
  role_code?: string | null
  // 角色名称
  role_name: string
  // 角色类型（1管理员角色 2自定义角色）
  role_type: number
  // 是否默认角色
  is_default: boolean
  // 数据范围（1商户全部数据 2个人数据）
  data_scope: number
  // 角色描述
  role_description?: string | null
  // 角色状态（0禁用 1启用）
  status: number
  // 备注
  remark?: string | null
}

/**
 * 商户角色状态切换请求DTO
 */
export interface ISysMerchantRoleStatusRequest {
  // 角色状态（0禁用 1启用）
  status: number
}

/**
 * 批量删除商户角色请求DTO
 */
export interface ISysMerchantRoleBatchDeleteRequest {
  // 角色ID集合
  role_ids: string[]
}

/**
 * 商户角色权限分配请求DTO
 */
export interface ISysMerchantRolePermissionRequest {
  // 授权权限ID列表
  authorized_permission_ids: string[]
}

/**
 * 商户角色基础信息响应VO
 */
export interface ISysMerchantRoleBasicResponse {
  // 角色ID
  id: string
  // 角色名称
  role_name: string
  // 角色类型
  role_type: number
  // 角色类型描述
  role_type_desc: string
  // 是否默认角色
  is_default: boolean
  // 角色状态
  status: number
  // 角色状态描述
  status_desc: string
}

/**
 * 商户角色选择项VO (用于下拉选择)
 */
export interface ISysMerchantRoleSelectItem {
  // 角色ID
  id: string
  // 角色编码
  role_code?: string | null
  // 角色名称
  role_name: string
  // 角色类型
  role_type: number
  // 角色类型描述
  role_type_desc: string
  // 角色状态
  status: number
}

/**
 * 角色权限信息
 */
export interface IRolePermissionInfo {
  // 权限ID（授权权限ID）
  permission_id: string
  // 权限名称
  permission_name: string
  // 权限编码
  permission_code: string
  // 权限类型
  permission_type: number
  // 权限类型描述
  permission_type_desc: string
}

/**
 * 角色权限分配信息响应
 */
export interface IRolePermissionAssignmentResponse {
  // 角色ID
  role_id: string
  // 角色名称
  role_name: string
  // 角色类型
  role_type: number
  // 角色类型描述
  role_type_desc: string
  // 分配的权限列表
  permissions: IRolePermissionInfo[]
}

/**
 * 商户角色统计响应
 */
export interface ISysMerchantRoleStatsResponse {
  // 商户ID
  merchant_id: number
  // 商户名称
  merchant_name: string
  // 角色总数
  total_roles: number
  // 管理员角色数
  admin_roles: number
  // 自定义角色数
  custom_roles: number
  // 启用角色数
  active_roles: number
  // 禁用角色数
  disabled_roles: number
}

/**
 * 权限树节点通用字段
 */
export interface IMerchantRolePermissionNode {
  // 权限ID
  id: string
  // 授权权限ID
  authorized_permission_id: string
  // 权限模板ID
  permission_template_id: string
  // 权限名称
  permission_name: string
  // 权限编码
  permission_code: string
  // 父权限ID
  parent_id: string
  // 排序号
  order_num: number
  // 路径
  path: string
  // 组件
  component: string
  // 权限类型
  permission_type: number
  // 权限类型描述
  permission_type_desc: string
  // 是否可见
  visible: number
  // 可见描述
  visible_desc: string
  // 图标
  icon: string
  // 描述
  description: string
  // 子权限
  children: IMerchantRolePermissionNode[]
}

/**
 * 可用权限树节点
 */
export interface IMerchantRoleAvailablePermissionNode extends IMerchantRolePermissionNode {
  // 是否已授权
  is_authorized: boolean
  // 是否已分配
  is_assigned: boolean
  // 授权状态
  authorized_status: number
  // 授权状态描述
  authorized_status_desc: string
  // 授权时间
  authorized_date: string
  // 子权限
  children: IMerchantRoleAvailablePermissionNode[]
}

/**
 * 角色权限树节点
 */
export interface IMerchantRolePermissionTreeNode extends IMerchantRolePermissionNode {
  // 是否拥有权限
  has_permission: boolean
  // 状态
  status: number
  // 状态描述
  status_desc: string
  // 子权限
  children: IMerchantRolePermissionTreeNode[]
}

/**
 * 商户角色可用权限响应
 */
export interface ISysMerchantRoleAvailablePermissionsResponse {
  // 角色ID
  role_id: string
  // 角色名称
  role_name: string
  // 商户ID
  merchant_id: number
  // 商户名称
  merchant_name: string
  // 总授权数
  total_authorized: number
  // 已分配数
  already_assigned: number
  // 可用数量
  available_count: number
  // 权限树
  permission_tree: IMerchantRoleAvailablePermissionNode[]
}

/**
 * 商户角色权限响应
 */
export interface ISysMerchantRolePermissionsResponse {
  // 角色ID
  role_id: string
  // 角色名称
  role_name: string
  // 角色类型
  role_type: number
  // 角色类型描述
  role_type_desc: string
  // 商户ID
  merchant_id: number
  // 商户名称
  merchant_name: string
  // 总权限数
  total_permissions: number
  // 已分配权限数
  assigned_permissions: number
  // 权限树
  permission_tree: IMerchantRolePermissionTreeNode[]
} 