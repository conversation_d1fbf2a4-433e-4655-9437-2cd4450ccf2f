/**
 * 商户授权权限分页查询请求参数
 */
export interface ISysMerchantAuthorizedPermissionPageRequest {
  merchant_id?: number | null
  permission_template_id?: string | null
  status?: number | null
  page?: number | null
  page_size?: number | null
  authorized_start?: string | null
  authorized_end?: string | null
  created_start?: string | null
  created_end?: string | null
}

/**
 * 商户授权权限列表响应VO
 */
export interface ISysMerchantAuthorizedPermissionListResponse {
  id: string
  merchant_id: number
  merchant_name?: string | null
  permission_template_id: string
  permission_name?: string | null
  permission_code?: string | null
  permission_type?: number | null
  permission_type_desc: string
  status: number
  status_desc: string
  authorized_date: string
  authorized_by_name?: string | null
  remark?: string | null
}

/**
 * 商户授权权限详情响应VO
 */
export interface ISysMerchantAuthorizedPermissionDetailResponse {
  id: string
  merchant_id: number
  merchant_name?: string | null
  permission_template_id: string
  permission_name?: string | null
  permission_code?: string | null
  permission_type?: number | null
  permission_type_desc: string
  permission_description?: string | null
  status: number
  status_desc: string
  authorized_date: string
  created_by: string
  authorized_by_name?: string | null
  updated_by?: string | null
  updated_by_name?: string | null
  remark?: string | null
}

/**
 * 商户授权权限创建请求DTO
 */
export interface ISysMerchantAuthorizedPermissionCreateRequest {
  merchant_id: number
  permission_template_ids: string[]
  status: number
  authorized_date?: string | null
  remark?: string | null
}

/**
 * 商户授权权限更新请求DTO
 */
export interface ISysMerchantAuthorizedPermissionUpdateRequest {
  id: string
  status: number
  authorized_date?: string | null
  remark?: string | null
}

/**
 * 商户授权权限批量删除请求DTO
 */
export interface ISysMerchantAuthorizedPermissionBatchDeleteRequest {
  authorized_permission_ids: string[]
}

/**
 * 商户授权权限状态切换请求DTO
 */
export interface ISysMerchantAuthorizedPermissionStatusRequest {
  status: number
}

/**
 * 商户权限批量授权请求DTO
 */
export interface ISysMerchantPermissionBatchAuthorizeRequest {
  merchant_id: number
  permission_template_ids: string[]
  override_existing: boolean
  remark?: string | null
}

/**
 * 商户权限模板复制请求DTO
 */
export interface ISysMerchantPermissionCopyRequest {
  source_merchant_id: number
  target_merchant_ids: number[]
  override_existing: boolean
  remark?: string | null
}

/**
 * 商户权限撤销请求DTO
 */
export interface ISysMerchantPermissionRevokeRequest {
  merchant_id: number
  permission_template_ids: string[]
  revoke_reason?: string | null
}

/**
 * 商户授权权限选择项
 */
export interface ISysMerchantAuthorizedPermissionSelectItem {
  id: string
  permission_template_id: string
  permission_name?: string | null
  permission_code?: string | null
  status: number
}

/**
 * 商户权限统计响应
 */
export interface ISysMerchantPermissionStatsResponse {
  merchant_id: number
  merchant_name: string
  total_permissions: number
  authorized_permissions: number
  active_permissions: number
  suspended_permissions: number
}

/**
 * 商户授权权限树形节点
 */
export interface ISysMerchantAuthorizedPermissionTreeNode {
  id: string
  permission_code: string
  permission_name: string
  permission_type: number
  permission_type_desc: string
  parent_id?: string | null
  order_num: number
  is_authorized: boolean
  authorized_permission_id?: string | null
  status?: number | null
  status_desc?: string | null
  authorized_date?: string | null
  children: ISysMerchantAuthorizedPermissionTreeNode[]
}

/**
 * 商户授权权限树形结构响应
 */
export interface ISysMerchantAuthorizedPermissionTreeResponse {
  merchant_id: number
  merchant_name: string
  tree: ISysMerchantAuthorizedPermissionTreeNode[]
} 