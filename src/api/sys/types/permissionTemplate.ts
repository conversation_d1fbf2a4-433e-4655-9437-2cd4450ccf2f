/**
 * 系统权限模板分页查询请求参数
 */
export interface ISysPermissionTemplatePageRequest {
  permission_name?: string | null
  permission_code?: string | null
  parent_id?: string | null
  permission_type?: number | null
  visible?: number | null
  page: number
  page_size: number
}

/**
 * 系统权限模板列表响应VO
 */
export interface ISystemPermissionTemplateListResponse {
  id: string
  permission_name: string
  permission_code: string
  parent_id?: string | null
  parent_name?: string | null
  order_num: number
  path?: string | null
  component?: string | null
  permission_type: number
  permission_type_desc: string
  visible: number
  visible_desc: string
  icon?: string | null
  description?: string | null
  created_date: string
  updated_date: string
}

/**
 * 系统权限模板详情响应VO
 */
export interface ISystemPermissionTemplateDetailResponse {
  id: string
  permission_name: string
  permission_code: string
  parent_id?: string | null
  parent_name?: string | null
  order_num: number
  path?: string | null
  component?: string | null
  query?: string | null
  is_frame: number
  is_frame_desc: string
  is_cache: number
  is_cache_desc: string
  permission_type: number
  permission_type_desc: string
  visible: number
  visible_desc: string
  icon?: string | null
  description?: string | null
  created_date: string
  updated_date: string
  created_by?: string | null
  updated_by?: string | null
  remark?: string | null
}

/**
 * 系统权限模板创建请求DTO
 */
export interface ISysPermissionTemplateCreateRequest {
  permission_name: string
  permission_code: string
  parent_id?: string | null
  order_num: number
  path?: string | null
  component?: string | null
  query?: string | null
  is_frame: number
  is_cache: number
  permission_type: number
  visible: number
  icon: string
  description?: string | null
  remark?: string | null
}

/**
 * 系统权限模板更新请求DTO
 */
export interface ISysPermissionTemplateUpdateRequest {
  permission_name: string
  permission_code: string
  parent_id?: string | null
  order_num: number
  path?: string | null
  component?: string | null
  query?: string | null
  is_frame: number
  is_cache: number
  permission_type: number
  visible: number
  icon: string
  description?: string | null
  remark?: string | null
}

/**
 * 系统权限模板批量删除请求DTO
 */
export interface ISysPermissionTemplateBatchDeleteRequest {
  template_ids: string[]
}

/**
 * 系统权限模板状态切换请求DTO
 */
export interface ISysPermissionTemplateStatusRequest {
  visible: number
}

/**
 * 系统权限模板选择项VO
 */
export interface ISystemPermissionTemplateSelectItem {
  id: string
  permission_name: string
  permission_code: string
  permission_type: number
  parent_id?: string | null
  visible: number
}

/**
 * 系统权限模板统计信息响应VO
 */
export interface ISystemPermissionTemplateStatsResponse {
  total_count: number
  directory_count: number
  menu_count: number
  button_count: number
  visible_count: number
  hidden_count: number
}

/**
 * 系统权限模板树形节点VO
 */
export interface ISystemPermissionTemplateTreeNode {
  id: string
  label: string
  permission_code: string
  parent_id?: string | null
  permission_type: number
  visible: number
  icon?: string | null
  order_num: number
  children: ISystemPermissionTemplateTreeNode[]
} 