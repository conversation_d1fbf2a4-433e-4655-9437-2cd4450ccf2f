import { http } from '@/utils/http'
import type {
  ILoginForm,
  ILoginData,
  IRefreshTokenForm,
  IRefreshTokenData,
  IChangePasswordForm,
  IUserProfile,
  IUpdateProfileForm,
  IWechatLoginForm,
  IWechatLoginData,
} from './types'

/**
 * 系统用户账号密码登录
 * @param loginForm 登录表单数据
 * @returns 返回access_token和refresh_token
 */
export const loginApi = (loginForm: ILoginForm) => {
  return http.post<ILoginData>('/sys_user/login', loginForm)
}

/**
 * 刷新访问令牌
 * @param refreshTokenForm 刷新token表单
 * @returns 返回新的access_token和refresh_token
 */
export const refreshTokenApi = (refreshTokenForm: IRefreshTokenForm) => {
  return http.post<IRefreshTokenData>('/sys_user/refresh', refreshTokenForm)
}

/**
 * 用户登出
 * @returns 返回登出结果
 */
export const logoutApi = () => {
  return http.post<string>('/sys_user/logout', undefined, undefined, {
    hideErrorToast: true, // 隐藏错误提示，因为logout失败也要清理本地数据
  })
}

/**
 * 修改密码
 * @param changePasswordForm 修改密码表单
 * @returns 返回修改结果
 */
export const changePasswordApi = (changePasswordForm: IChangePasswordForm) => {
  return http.put<string>('/sys_user/change_password', changePasswordForm)
}

/**
 * 获取个人信息
 * @returns 返回当前登录用户的个人信息
 */
export const getProfileApi = () => {
  return http.get<IUserProfile>('/sys_user/profile')
}

/**
 * 更新个人信息
 * @param updateProfileForm 更新个人信息表单
 * @returns 返回更新结果
 */
export const updateProfileApi = (updateProfileForm: IUpdateProfileForm) => {
  return http.put<string>('/sys_user/profile', updateProfileForm)
}

/**
 * 系统用户微信登录
 * @param wechatLoginForm 微信登录表单数据
 * @returns 返回access_token和refresh_token
 */
export const wechatLoginApi = (wechatLoginForm: IWechatLoginForm) => {
  return http.post<IWechatLoginData>('/sys_user/wechat_login', wechatLoginForm)
}

// 同时导出接口类型，方便外部使用
export type {
  ILoginForm,
  ILoginData,
  IRefreshTokenForm,
  IRefreshTokenData,
  IChangePasswordForm,
  IUserProfile,
  IUpdateProfileForm,
  IWechatLoginForm,
  IWechatLoginData,
}
