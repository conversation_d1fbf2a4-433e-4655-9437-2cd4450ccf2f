import { http } from '@/utils/http'
import type {
  IPermissionPageRequest,
  IPermissionListResponse,
  IPermissionDetailResponse,
  IPermissionTreeNode,
  IPermissionSelectItem,
  IPermissionCreateRequest,
  IPermissionUpdateRequest,
  IPermissionStatusRequest,
  IPermissionBatchDeleteRequest,
  IRouterVo,
} from './types'

/**
 * 分页查询权限菜单列表
 * @param params 查询参数
 * @returns 返回分页权限菜单列表
 */
export const getPermissionListApi = (params?: IPermissionPageRequest) => {
  return http.get<IPageData<IPermissionListResponse>>('/system/permissions',  params )
}

/**
 * 获取权限菜单详情
 * @param permissionId 权限菜单ID
 * @returns 返回权限菜单详情
 */
export const getPermissionDetailApi = (permissionId: string) => {
  return http.get<IPermissionDetailResponse>(`/system/permissions/${permissionId}`)
}

/**
 * 创建权限菜单
 * @param createRequest 创建请求参数
 * @returns 返回创建结果
 */
export const createPermissionApi = (createRequest: IPermissionCreateRequest) => {
  return http.post<string>('/system/permissions', createRequest)
}

/**
 * 更新权限菜单
 * @param permissionId 权限菜单ID
 * @param updateRequest 更新请求参数
 * @returns 返回更新结果
 */
export const updatePermissionApi = (
  permissionId: string,
  updateRequest: IPermissionUpdateRequest,
) => {
  return http.put<string>(`/system/permissions/${permissionId}`, updateRequest)
}

/**
 * 删除权限菜单
 * @param permissionId 权限菜单ID
 * @returns 返回删除结果
 */
export const deletePermissionApi = (permissionId: string) => {
  return http.delete<string>(`/system/permissions/${permissionId}`)
}

/**
 * 切换权限菜单状态
 * @param permissionId 权限菜单ID
 * @param statusRequest 状态切换请求
 * @returns 返回切换结果
 */
export const changePermissionStatusApi = (
  permissionId: string,
  statusRequest: IPermissionStatusRequest,
) => {
  return http.put<string>(`/system/permissions/${permissionId}/status`, statusRequest)
}

/**
 * 批量删除权限菜单
 * @param batchDeleteRequest 批量删除请求
 * @returns 返回批量删除结果
 */
export const batchDeletePermissionsApi = (batchDeleteRequest: IPermissionBatchDeleteRequest) => {
  return http.post<string>('/system/permissions/batch-delete', batchDeleteRequest)
}

/**
 * 获取权限菜单选择项
 * @returns 返回所有可选的权限菜单项
 */
export const getPermissionSelectItemsApi = () => {
  return http.get<IPermissionSelectItem[]>('/system/permissions/select')
}

/**
 * 获取权限菜单树
 * @returns 返回所有权限菜单的树形结构
 */
export const getPermissionTreeApi = () => {
  return http.get<IPermissionTreeNode[]>('/system/permissions/tree')
}

/**
 * 获取用户可访问的路由菜单
 * @returns 返回当前用户权限可访问的前端路由菜单
 */
export const getUserRoutersApi = () => {
  return http.get<IRouterVo[]>('/system/user/routers')
}

// 同时导出接口类型，方便外部使用
export type {
  IPermissionPageRequest,
  IPermissionListResponse,
  IPermissionDetailResponse,
  IPermissionTreeNode,
  IPermissionSelectItem,
  IPermissionCreateRequest,
  IPermissionUpdateRequest,
  IPermissionStatusRequest,
  IPermissionBatchDeleteRequest,
  IRouterVo,
}
