<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'
import { useGlobalRole, userTypeEnum } from '@/store/globalRole'
import { useMenuPermissionStore } from '@/store/menuPermission'

// 添加标志位，防止重复跳转
let hasInitialNavigation = false

onLaunch(async () => {
  console.log('App Launch')

  // 根据全局角色，决定跳转页面（仅在首次启动时执行）
  const globalRole = useGlobalRole()

  // 全局隐藏原生TabBar
  setTimeout(() => {
    uni.hideTabBar({
      animation: false,
      fail: () => {
        // 忽略非TabBar页面的错误
      },
    })
  }, 0)

  // 首次启动时根据用户类型跳转页面
  if (!hasInitialNavigation) {
    hasInitialNavigation = true

    // 根据用户类型跳转到对应页面
    if (globalRole.getRole === userTypeEnum.system) {
      // 这里获取系统用户菜单权限的首元素path  当做默认页面  这里不应写死 系统用户可能没有系统首页权限
      const path =  useMenuPermissionStore().tabBarList[0].path
      console.log('跳转到系统管理员首页：', path)
      setTimeout(() => {
        uni.redirectTo({
          url: path,
        })
      }, 100)
    }
    // 可以在这里添加其他用户类型的跳转逻辑
    // else if (globalRole.getRole === userTypeEnum.merchant) {
    //   setTimeout(() => {
    //     uni.navigateTo({
    //       url: '/pages-merchant/index',
    //     })
    //   }, 100)
    // }
    // else if (globalRole.getRole === userTypeEnum.normal) {
    //   setTimeout(() => {
    //     uni.navigateTo({
    //       url: '/pages/index',
    //     })
    //   }, 100)
    // }
  }
})

onShow(() => {
  console.log('App Show')

  // 只处理 TabBar 隐藏，不处理页面跳转
  setTimeout(() => {
    uni.hideTabBar({
      animation: false,
      fail: () => {
        // 忽略非TabBar页面的错误
      },
    })
  }, 0)

  // 强制隐藏，多次尝试
  const hideTabBarInterval = setInterval(() => {
    uni.hideTabBar({
      animation: false,
      fail: () => {
        // 忽略非TabBar页面的错误
      },
    })
  }, 100)

  // 3秒后停止尝试
  setTimeout(() => {
    clearInterval(hideTabBarInterval)
  }, 3000)
})

onHide(() => {
  console.log('App Hide')
})
</script>

<style lang="scss">
/* stylelint-disable selector-type-no-unknown */
button::after {
  border: none;
}

swiper,
scroll-view {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

// 单行省略，优先使用 unocss: text-ellipsis
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 两行省略
.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 三行省略
.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

// 全局隐藏原生TabBar
uni-tabbar {
  display: none !important;
}
</style>
