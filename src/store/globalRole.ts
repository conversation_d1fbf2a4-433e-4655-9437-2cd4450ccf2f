import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useGlobalRole = defineStore(
  'globalRoleStore',
  () => {
    const role = ref<userTypeEnum>(userTypeEnum.user)

    const getRole = computed(() => role.value)

    const setRole = (new_role: userTypeEnum) => {
      role.value = new_role
    }

    return {
      role,
      getRole,
      setRole,
    }
  },
  { persist: true },
)

/**
 * 用户类型枚举
 * - 默认为普通用户(user)
 * - 内有三种用户类型，系统用户(system) | 商户用户(merchants) | 普通用户(user)
 */
export const enum userTypeEnum {
  system = 'system',
  merchants = 'merchants',
  user = 'user',
}
