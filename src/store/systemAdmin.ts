import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import {
  loginApi,
  refreshTokenApi,
  logout<PERSON>pi,
  getProfileApi,
  wechatLoginApi,
  type ILoginForm,
  type IUserProfile,
  type IWechatLoginForm,
} from '@/api/sys/systemAuthApi'
import { useGlobalRole, userTypeEnum } from '@/store/globalRole'
import { useMenuPermissionStore } from '@/store/menuPermission'
import { ISysRoleSimpleResponse } from '@/api/sys/types/role'

/**
 * Token 信息接口
 */
export interface TokenInfo {
  accessToken: string // 短 token (访问令牌)
  refreshToken: string // 长 token (刷新令牌)
}

/**
 * 系统管理员状态管理
 * 处理登录、权限、Token等核心功能
 */
export const useSystemAdminStore = defineStore(
  'systemAdmin',
  () => {
    const globalRole = useGlobalRole()
    const menuPermissionStore = useMenuPermissionStore()

    // ==================== 状态定义 ====================
    const adminInfo = ref<IUserProfile | null>(null) // 管理员信息
    const tokenInfo = ref<TokenInfo | null>(null) // Token信息
    const roles = ref<ISysRoleSimpleResponse[]>([]) // 角色信息简单集合
    const permissions = ref<string[]>([]) // 权限列表
    const isRefreshing = ref(false) // 是否正在刷新token
    // ==================== 计算属性 ====================
    /**
     * 检查是否已登录
     * 需要同时有管理员信息和有效token
     */
    const isLoggedIn = computed(() => {
      return adminInfo.value !== null && tokenInfo.value !== null
    })

    /**
     * 检查是否有有效的token
     */
    const hasValidToken = computed(() => {
      return tokenInfo.value !== null
    })

    // ==================== Token 管理 ====================
    /**
     * 获取当前访问令牌
     * @returns 访问令牌或null
     */
    const getAccessToken = (): string | null => {
      return tokenInfo.value?.accessToken || null
    }

    /**
     * 获取当前刷新令牌
     * @returns 刷新令牌或null
     */
    const getRefreshToken = (): string | null => {
      return tokenInfo.value?.refreshToken || null
    }

    /**
     * 刷新访问令牌
     * @returns 是否刷新成功
     */
    const refreshAccessToken = async () => {
      // 防止重复刷新
      if (isRefreshing.value || !tokenInfo.value) {
        return false
      }

      isRefreshing.value = true

      try {
        const result = await refreshTokenApi({
          refresh_token: tokenInfo.value.refreshToken,
        })

        if (result.code === 200) {
          // 更新 token
          tokenInfo.value = {
            accessToken: result.data.access_token,
            refreshToken: result.data.refresh_token,
          }
          return true
        } else if (result.code === 20002) {
          uni.navigateTo({
            url: '/pages-sys/login/login',
          })
          clearAll()
        }
        return false
      } catch (error) {
        console.error('刷新访问令牌失败:', error)
        return false
      } finally {
        isRefreshing.value = false
      }
    }

    // ==================== 权限管理 ====================
    /**
     * 检查是否有指定权限
     * @param permissionCode 权限代码
     * @returns 是否有该权限
     */
    const hasPermission = (permissionCode: string): boolean => {
      return permissions.value.includes(permissionCode)
    }

    /**
     * 检查是否有指定角色
     * @param roleCode 角色代码
     * @returns 是否有该角色
     */
    const hasRole = (roleCode: string): boolean => {
      return roles.value.includes(roleCode)
    }

    /**
     * 检查是否有任一权限
     * @param permissionCodes 权限代码数组
     * @returns 是否有任一权限
     */
    const hasAnyPermission = (permissionCodes: string[]): boolean => {
      return permissionCodes.some((code) => hasPermission(code))
    }

    /**
     * 检查是否有所有权限
     * @param permissionCodes 权限代码数组
     * @returns 是否有所有权限
     */
    const hasAllPermissions = (permissionCodes: string[]): boolean => {
      return permissionCodes.every((code) => hasPermission(code))
    }

    // ==================== 登录/登出 ====================
    /**
     * 执行登录操作
     * @param loginForm 登录表单数据
     * @returns 登录结果
     */
    const login = async (loginForm: ILoginForm) => {
      try {
        // 1. 调用登录API获取token
        const loginResult = await loginApi(loginForm)
        console.log('loginResult', loginResult)

        if (loginResult.code === 200) {
          // 2. 设置Token
          tokenInfo.value = {
            accessToken: loginResult.data.access_token,
            refreshToken: loginResult.data.refresh_token,
          }

          // 3. 获取用户信息
          const profileResult = await getProfileApi()

          if (profileResult.code === 200) {
            const profile = profileResult.data
            // 4. 设置用户信息、角色和权限
            adminInfo.value = profile
            roles.value = profile.roles
            permissions.value = profile.permissions

            // 5. 设置全局角色
            globalRole.setRole(userTypeEnum.system)

            // 6. 获取菜单数据
            await menuPermissionStore.fetchUserMenus()

            console.log('登录成功:', profile.username)
            return { success: true, data: profile }
          } else {
            // 获取用户信息失败
            throw new Error(profileResult.message || '获取用户信息失败')
          }
        } else {
          // 登录失败，使用后端返回的错误信息
          throw new Error(loginResult.message || '登录失败')
        }
      } catch (error) {
        console.error('登录失败:', error)
        // 登录失败时清理所有数据
        clearAll()

        // 返回错误信息
        let errorMessage = '登录失败'
        if (error instanceof Error) {
          errorMessage = error.message
        } else if (typeof error === 'string') {
          errorMessage = error
        }

        return {
          success: false,
          error: errorMessage,
        }
      }
    }

    /**
     * 执行微信登录操作
     * @param wechatLoginForm 微信登录表单数据
     * @returns 登录结果
     */
    const wechatLogin = async (wechatLoginForm: IWechatLoginForm) => {
      try {
        // 1. 调用微信登录API获取token
        const loginResult = await wechatLoginApi(wechatLoginForm)
        console.log('wechatLoginResult', loginResult)

        if (loginResult.code === 200) {
          // 2. 设置Token
          tokenInfo.value = {
            accessToken: loginResult.data.access_token,
            refreshToken: loginResult.data.refresh_token,
          }

          // 3. 获取用户信息
          const profileResult = await getProfileApi()

          if (profileResult.code === 200) {
            const profile = profileResult.data
            // 4. 设置用户信息、角色和权限
            adminInfo.value = profile
            roles.value = profile.roles
            permissions.value = profile.permissions

            // 5. 设置全局角色
            globalRole.setRole(userTypeEnum.system)

            // 6. 获取菜单数据
            await menuPermissionStore.fetchUserMenus()

            console.log('微信登录成功:', profile.username)
            return { success: true, data: profile }
          } else {
            // 获取用户信息失败
            throw new Error(profileResult.message || '获取用户信息失败')
          }
        } else {
          // 登录失败，使用后端返回的错误信息
          throw new Error(loginResult.message || '微信登录失败')
        }
      } catch (error) {
        console.error('微信登录失败:', error)
        // 登录失败时清理所有数据
        clearAll()

        // 返回错误信息
        let errorMessage = '微信登录失败'
        if (error instanceof Error) {
          errorMessage = error.message
        } else if (typeof error === 'string') {
          errorMessage = error
        }

        return {
          success: false,
          error: errorMessage,
        }
      }
    }

    /**
     * 执行登出操作
     * 调用API并清理本地数据
     */
    const logout = async () => {
      try {
        // 调用登出API
        await logoutApi()
      } catch (error) {
        console.error('调用登出API失败:', error)
        // 即使API调用失败，也要清理本地数据
      } finally {
        clearAll()
      }
    }

    // ==================== 数据更新 ====================
    /**
     * 更新管理员信息
     * @param updates 要更新的字段
     */
    const updateAdminInfo = (updates: Partial<IUserProfile>) => {
      if (adminInfo.value) {
        adminInfo.value = { ...adminInfo.value, ...updates }
      }
    }

    /**
     * 清空所有数据
     * 用于登出或登录失败时的数据清理
     */
    const clearAll = () => {
      console.log('清空所有数据')
      adminInfo.value = null
      tokenInfo.value = null
      roles.value = []
      permissions.value = []
      isRefreshing.value = false
      menuPermissionStore.clearMenus()
    }

    return {
      // 状态
      adminInfo,
      tokenInfo,
      roles,
      permissions,
      isRefreshing,

      // 计算属性
      isLoggedIn,
      hasValidToken,

      // Token方法
      getAccessToken,
      getRefreshToken,
      refreshAccessToken,

      // 权限方法
      hasPermission,
      hasRole,
      hasAnyPermission,
      hasAllPermissions,

      // 核心功能
      login,
      wechatLogin,
      logout,
      updateAdminInfo,
      clearAll,
    }
  },
  {
    persist: true, // 启用持久化存储
  },
)
