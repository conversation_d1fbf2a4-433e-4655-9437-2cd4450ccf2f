import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

/**
 * 商户用户类型枚举
 * 定义用户在商户中的身份类型
 */
export enum MerchantUserType {
  ADMIN = 1, // 商户管理员 - 拥有商户的完全管理权限
  EMPLOYEE = 2, // 商户普通员工 - 拥有有限的操作权限
}

/**
 * 商户用户基础信息接口
 */
export interface MerchantUserInfo {
  id: string // 用户唯一标识
  username: string // 用户名
  realName: string // 真实姓名
  avatar: string // 头像URL
  phone: string // 手机号
  email: string // 邮箱
  status: number // 用户状态：0-禁用 1-启用
  createTime: number // 创建时间戳
  lastLoginTime: number // 最后登录时间戳
  lastLoginIp: string // 最后登录IP
}

export interface Token {
  token: string
}

// ==================== 基础信息接口 ====================

/**
 * 商户位置信息接口
 */
export interface MerchantLocation {
  longitude: number // 经度
  latitude: number // 纬度
}

/**
 * 商户基础信息接口
 */
export interface MerchantInfo {
  id: string // 商户ID
  merchantName: string // 商户名称
  merchantCode: string // 商户编码，全局唯一
  categoryId: string // 商户分类ID
  phone: string // 联系电话
  email: string // 邮箱
  address: string // 经营地址
  location: MerchantLocation | null // 经营位置坐标
  businessLicense: string // 营业执照号
  licensePhoto: string[] // 营业执照照片URL
  avatar: string // 商户头像URL
  description: string // 商户描述
  platformCommissionRate: number // 平台抽成比例(0-1)
  status: number // 商户状态：1-正常营业，2-临时关闭，3-永久关闭
  autoClearDate: string // 账单自动清零日期(YYYY-MM-DD格式)
  remark: string // 备注信息
}

// ==================== 权限角色接口 ====================

/**
 * 权限信息接口
 */
export interface PermissionInfo {
  code: string // 权限代码，唯一标识
  name: string // 权限名称，用于显示
  description: string // 权限描述，详细说明
}

/**
 * 角色信息接口
 */
export interface RoleInfo {
  code: string // 角色代码，唯一标识
  name: string // 角色名称，用于显示
  description: string // 角色描述，详细说明
}

// ==================== 用户商户关联接口 ====================

/**
 * 用户商户关联信息接口
 * 定义用户在特定商户下的身份、角色和权限
 */
export interface UserMerchantRelation {
  merchantId: string // 关联的商户ID
  merchantCode: string // 关联的商户编码
  userType: MerchantUserType // 用户在该商户下的类型（管理员/员工）
  roles: string[] // 用户在该商户下的角色代码列表
  permissions: string[] // 用户在该商户下的直接权限代码列表
  status: number // 用户在该商户下的状态：0-禁用 1-启用
}

/**
 * 用户在商户下的权限信息接口
 */
export interface UserMerchantPermissionInfo {
  merchantId: string // 商户ID
  userRoles: RoleInfo[] // 用户在该商户下的角色信息
  userPermissions: PermissionInfo[] // 用户在该商户下的直接权限信息
}

// ==================== 商户集合接口 ====================

/**
 * 用户商户项接口
 * 用户在单个商户中的详细信息
 */
export interface UserMerchantItem {
  merchantInfo: MerchantInfo // 商户基础信息
  userRelation: UserMerchantRelation // 用户与该商户的关联信息
  userPermissionInfo: UserMerchantPermissionInfo // 用户在该商户下的权限信息
  userTypeText: string // 用户类型文本
}

/**
 * 用户所属商户集合接口
 * 包含用户在所有关联商户中的完整信息
 */
export interface UserMerchantCollection {
  userId: string // 用户ID
  userName: string // 用户名
  totalMerchants: number // 用户关联的商户总数
  merchantList: UserMerchantItem[] // 用户所属的商户详细列表
}

// ==================== 当前选中商户接口 ====================

/**
 * 当前选中商户信息接口
 * 包含商户基础信息和用户在该商户下的完整权限信息
 */
export interface CurrentSelectedMerchant {
  merchantInfo: MerchantInfo // 商户基础信息
  userRelation: UserMerchantRelation // 用户与该商户的关联信息
  userPermissionInfo: UserMerchantPermissionInfo // 用户在该商户下的权限信息
}

// ==================== Pinia Store (组合式 API) ====================

/**
 * 用户商户管理 Store
 * 管理用户的商户集合和当前选中的商户信息
 */
export const useMerchantUserStore = defineStore('merchantUser', () => {
  // ==================== 状态定义 ====================

  // 用户基础信息
  const userInfo = ref<MerchantUserInfo | null>(null)

  const token = ref<Token | null>(null)

  // 用户所属的商户集合
  const userMerchantCollection = ref<UserMerchantCollection | null>(null)

  // 当前选中的商户
  const currentSelectedMerchant = ref<CurrentSelectedMerchant | null>(null)

  // 加载状态
  const isLoading = ref(false)
  const isCollectionLoaded = ref(false)

  // 错误信息
  const error = ref<string | null>(null)

  // ==================== 计算属性 ====================

  /**
   * 获取用户拥有的商户数量
   */
  const merchantCount = computed(() => {
    return userMerchantCollection.value?.totalMerchants || 0
  })

  /**
   * 获取商户列表
   */
  const merchantList = computed(() => {
    return userMerchantCollection.value?.merchantList || []
  })

  /**
   * 获取当前商户信息
   */
  const currentMerchant = computed(() => {
    return currentSelectedMerchant.value?.merchantInfo || null
  })

  /**
   * 获取当前商户的权限代码列表
   */
  const currentMerchantPermissions = computed(() => {
    const permissions = currentSelectedMerchant.value?.userPermissionInfo.userPermissions || []
    return permissions.map((p) => p.code)
  })

  /**
   * 获取当前商户的角色代码列表
   */
  const currentMerchantRoles = computed(() => {
    return currentSelectedMerchant.value?.userRelation.roles || []
  })

  /**
   * 获取当前商户的角色详细信息
   */
  const currentMerchantRoleInfos = computed(() => {
    return currentSelectedMerchant.value?.userPermissionInfo.userRoles || []
  })

  /**
   * 判断用户是否是当前商户的管理员
   */
  const isCurrentMerchantAdmin = computed(() => {
    return currentSelectedMerchant.value?.userRelation.userType === MerchantUserType.ADMIN
  })

  /**
   * 获取当前用户类型文本
   */
  const currentUserTypeText = computed(() => {
    const userType = currentSelectedMerchant.value?.userRelation.userType
    return userType === MerchantUserType.ADMIN ? '管理员' : '员工'
  })

  // ==================== 操作方法 ====================

  /**
   * 设置用户基础信息
   */
  const setUserInfo = (userInfoData: MerchantUserInfo) => {
    userInfo.value = userInfoData
  }

  /**
   * 加载用户商户集合
   */
  const loadUserMerchantCollection = async () => {
    isLoading.value = true
    error.value = null

    try {
      // TODO: 调用 API 获取用户商户集合
      // const response = await merchantApi.getUserMerchantCollection()
      // userMerchantCollection.value = response.data

      // 模拟数据加载
      console.log('加载用户商户集合...')

      isCollectionLoaded.value = true
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载商户列表失败'
      console.error('加载用户商户集合失败:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 设置用户商户集合
   */
  const setUserMerchantCollection = (collection: UserMerchantCollection) => {
    userMerchantCollection.value = collection
    isCollectionLoaded.value = true
  }

  /**
   * 选择商户
   */
  const selectMerchant = async (merchantId: string) => {
    if (!userMerchantCollection.value) {
      await loadUserMerchantCollection()
    }

    const merchantItem = userMerchantCollection.value?.merchantList.find(
      (item) => item.merchantInfo.id === merchantId,
    )

    if (!merchantItem) {
      throw new Error('未找到指定的商户')
    }

    // 构建当前选中商户信息
    currentSelectedMerchant.value = {
      merchantInfo: merchantItem.merchantInfo,
      userRelation: merchantItem.userRelation,
      userPermissionInfo: merchantItem.userPermissionInfo,
    }

    console.log('已选择商户:', merchantItem.merchantInfo.merchantName)
  }

  /**
   * 通过商户编码选择商户
   */
  const selectMerchantByCode = async (merchantCode: string) => {
    if (!userMerchantCollection.value) {
      await loadUserMerchantCollection()
    }

    const merchantItem = userMerchantCollection.value?.merchantList.find(
      (item) => item.merchantInfo.merchantCode === merchantCode,
    )

    if (!merchantItem) {
      throw new Error('未找到指定的商户')
    }

    await selectMerchant(merchantItem.merchantInfo.id)
  }

  /**
   * 清除当前选中的商户
   */
  const clearSelectedMerchant = () => {
    currentSelectedMerchant.value = null
  }

  /**
   * 检查当前商户的权限
   */
  const hasPermission = (permissionCode: string): boolean => {
    return currentMerchantPermissions.value.includes(permissionCode)
  }

  /**
   * 检查当前商户的多个权限（需要全部拥有）
   */
  const hasAllPermissions = (permissionCodes: string[]): boolean => {
    const currentPermissions = currentMerchantPermissions.value
    return permissionCodes.every((code) => currentPermissions.includes(code))
  }

  /**
   * 检查当前商户的多个权限（只需拥有其中一个）
   */
  const hasAnyPermission = (permissionCodes: string[]): boolean => {
    const currentPermissions = currentMerchantPermissions.value
    return permissionCodes.some((code) => currentPermissions.includes(code))
  }

  /**
   * 检查当前商户的角色
   */
  const hasRole = (roleCode: string): boolean => {
    return currentMerchantRoles.value.includes(roleCode)
  }

  /**
   * 检查当前商户的多个角色（需要全部拥有）
   */
  const hasAllRoles = (roleCodes: string[]): boolean => {
    const currentRoles = currentMerchantRoles.value
    return roleCodes.every((code) => currentRoles.includes(code))
  }

  /**
   * 检查当前商户的多个角色（只需拥有其中一个）
   */
  const hasAnyRole = (roleCodes: string[]): boolean => {
    const currentRoles = currentMerchantRoles.value
    return roleCodes.some((code) => currentRoles.includes(code))
  }

  /**
   * 刷新当前商户信息
   */
  const refreshCurrentMerchant = async () => {
    if (!currentSelectedMerchant.value) return

    const merchantId = currentSelectedMerchant.value.merchantInfo.id

    // 重新加载商户集合
    await loadUserMerchantCollection()

    // 重新选择当前商户
    await selectMerchant(merchantId)
  }

  const setToken = (tokenData: Token) => {
    token.value = tokenData
  }

  const getToken = computed(() => {
    return token.value
  })

  /**
   * 是否登录
   */
  const isLoggedIn = computed(() => {
    return userInfo.value !== null && token.value !== null
  })

  /**
   * 重置 store 状态
   */
  const reset = () => {
    userInfo.value = null
    userMerchantCollection.value = null
    currentSelectedMerchant.value = null
    isLoading.value = false
    isCollectionLoaded.value = false
    error.value = null
  }

  // ==================== 返回所有状态和方法 ====================
  return {
    // 状态
    userInfo,
    userMerchantCollection,
    currentSelectedMerchant,
    isLoading,
    isCollectionLoaded,
    error,

    // 计算属性
    merchantCount,
    merchantList,
    currentMerchant,
    currentMerchantPermissions,
    currentMerchantRoles,
    currentMerchantRoleInfos,
    isCurrentMerchantAdmin,
    currentUserTypeText,
    isLoggedIn,
    getToken,
    // 方法
    setUserInfo,
    loadUserMerchantCollection,
    setUserMerchantCollection,
    selectMerchant,
    selectMerchantByCode,
    clearSelectedMerchant,
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    hasRole,
    hasAllRoles,
    hasAnyRole,
    refreshCurrentMerchant,
    reset,
    setToken,
  }
})
