<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '主页',
  },
}
</route>
<template>
  <view class="home-container" :style="{ paddingTop: safeAreaInsets?.top + 'px' }">
    <!-- 页面内容 -->
    <view class="content">
      <text>首页内容</text>
    </view>

    <!-- 底部导航栏 -->
    <TabBar current-page="index" />
  </view>
</template>

<script lang="ts" setup>
import { useGlobalSafeArea } from '@/hooks/useSafeArea'

defineOptions({
  name: 'Index',
})

// 使用全局安全区域 hook
const { safeAreaInsetsTop } = useGlobalSafeArea()
// 为了兼容模板中的使用方式，创建 safeAreaInsets 对象
const safeAreaInsets = computed(() => ({
  top: safeAreaInsetsTop.value,
}))
</script>

<style scoped>
.home-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
