<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '分类',
  },
  access: {
    requireAuth: false,
  },
}
</route>

<template>
  <view class="category-container" :style="{ paddingTop: safeAreaInsetsTop + 'px' }">
    <!-- 角色切换区域（开发测试用） -->
    <view class="role-switch-section">
      <text class="section-title">开发测试 - 角色切换</text>
      <view class="button-group">
        <wd-button
          :type="globalRoleStore.getRole.valueOf() === 'user' ? 'primary' : 'default'"
          size="small"
          @click="switchRole(userTypeEnum.user)"
        >
          普通用户
        </wd-button>
        <wd-button
          :type="globalRoleStore.getRole.valueOf() === 'merchants' ? 'primary' : 'default'"
          size="small"
          @click="switchRole(userTypeEnum.merchants)"
        >
          商户用户
        </wd-button>
        <wd-button
          :type="globalRoleStore.getRole.valueOf() === 'system' ? 'primary' : 'default'"
          size="small"
          @click="switchRole(userTypeEnum.system)"
        >
          管理员
        </wd-button>
      </view>
      <text class="current-role">当前角色：{{ currentRole }}</text>
    </view>

    <!-- 顶部搜索栏 -->
    <view class="header" :style="{ paddingRight: rightSafeArea + 'px' }">
      <wd-search
        v-model="searchValue"
        placeholder="搜索商品、类目"
        hide-cancel
        @focus="handleSearchFocus"
        @search="handleSearch"
        @input="handleSearchInput"
        @change="handleSearchChange"
        @clear="handleSearchClear"
        custom-class="search-component"
      />
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 左侧分类导航 -->
      <view class="sidebar-wrapper">
        <wd-sidebar
          v-model="activeCategory"
          @change="handleSidebarChange"
          custom-class="sidebar-container"
        >
          <wd-sidebar-item
            v-for="(category, index) in categories"
            :key="index"
            :value="index"
            :label="category.name"
          />
        </wd-sidebar>
      </view>

      <!-- 右侧商品展示区域 -->
      <view class="right-content">
        <!-- 搜索结果为空时显示空状态 -->
        <view v-if="isSearching && filteredCategories.length === 0" class="empty-state">
          <wd-status-tip image="search" tip="当前搜索无结果" />
        </view>

        <!-- 有搜索结果或未搜索时显示商品列表 -->
        <scroll-view
          v-else
          scroll-y
          class="right-scroll"
          :scroll-into-view="scrollIntoView"
          :scroll-with-animation="true"
          @scroll="onScroll"
        >
          <view
            v-for="(category, categoryIndex) in displayCategories"
            :key="categoryIndex"
            :id="`category-${categoryIndex}`"
            :class="`category category-${categoryIndex}`"
          >
            <view class="category-card">
              <view class="card-header">
                <text class="card-title">{{ category.name }}</text>
                <view class="arrow-icon">›</view>
              </view>
              <view class="items-grid">
                <view
                  class="item-card"
                  v-for="(item, index) in category.items"
                  :key="index"
                  @click="handleItemClick(item)"
                >
                  <view class="item-image">
                    <wd-img :src="item.image" mode="aspectFill" :width="60" :height="60" />
                  </view>
                  <text class="item-name">{{ item.name }}</text>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
    <!-- 底部导航栏 -->
    <TabBar current-page="category" />
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { getRect, isArray } from 'wot-design-uni/components/common/util'
import { useGlobalSafeArea } from '@/hooks/useSafeArea'
import { userTypeEnum, useGlobalRole } from '@/store/globalRole'

const globalRoleStore = useGlobalRole()

defineOptions({
  name: 'UserCategory',
})

// 切换角色
const switchRole = (role: userTypeEnum) => {
  globalRoleStore.setRole(role)
  uni.showToast({
    title: `已切换到${getRoleText(role)}`,
    icon: 'success',
  })
  if (role == userTypeEnum.system) {
    uni.redirectTo({ url: '/pages-sys/dashboard/index' })
  }
}

// 获取角色显示文本
const getRoleText = (role: userTypeEnum) => {
  const roleMap = {
    user: '普通用户',
    merchants: '商户用户',
    system: '管理员',
  }
  return roleMap[role]
}

// 当前角色文本
const currentRole = computed(() => {
  return getRoleText(globalRoleStore.getRole.valueOf() as userTypeEnum)
})

// 获取屏幕边界到安全区域距离
const { safeAreaInsetsTop, rightSafeArea } = useGlobalSafeArea()

const activeCategory = ref(0)
const scrollIntoView = ref('')
const searchValue = ref('')
const itemScrollTop = ref([])
const isSearching = ref(false)

// 分类数据
const categories = ref([
  {
    id: 1,
    name: '水果',
    icon: '🍎',
    items: [
      { name: '苹果', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
      { name: '香蕉', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
      { name: '橙子', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
      { name: '葡萄', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
    ],
  },
  {
    id: 2,
    name: '蔬菜',
    icon: '🥬',
    items: [
      { name: '白菜', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
      { name: '萝卜', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
    ],
  },
  {
    id: 3,
    name: '肉类',
    icon: '🥩',
    items: [
      { name: '猪肉', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
      { name: '牛肉', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
      { name: '鸡肉', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
    ],
  },
  {
    id: 4,
    name: '海鲜',
    icon: '🦐',
    items: [
      { name: '虾', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
      { name: '蟹', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
    ],
  },
  {
    id: 5,
    name: '饮品',
    icon: '🥤',
    items: [
      { name: '可乐', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
      { name: '果汁', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
      { name: '咖啡', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
    ],
  },
  {
    id: 6,
    name: '零食',
    icon: '🍿',
    items: [
      { name: '薯片', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
      { name: '巧克力', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
    ],
  },
  {
    id: 7,
    name: '调料',
    icon: '🧂',
    items: [
      { name: '盐', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
      { name: '糖', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
      { name: '酱油', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
    ],
  },
  {
    id: 8,
    name: '粮油',
    icon: '🌾',
    items: [
      { name: '大米', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
      { name: '面粉', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
    ],
  },
  {
    id: 9,
    name: '乳制品',
    icon: '🥛',
    items: [
      { name: '牛奶', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
      { name: '酸奶', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
      { name: '奶酪', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
    ],
  },
  {
    id: 10,
    name: '冷冻食品',
    icon: '🧊',
    items: [
      { name: '冷冻饺子', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
      { name: '冰淇淋', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
    ],
  },
  {
    id: 11,
    name: '面包糕点',
    icon: '🍞',
    items: [
      { name: '吐司', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
      { name: '蛋糕', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
    ],
  },
  {
    id: 12,
    name: '茶叶',
    icon: '🍵',
    items: [
      { name: '绿茶', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
      { name: '红茶', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
      { name: '乌龙茶', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
    ],
  },
  {
    id: 13,
    name: '酒类',
    icon: '🍷',
    items: [
      { name: '白酒', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
      { name: '红酒', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
    ],
  },
  {
    id: 14,
    name: '保健品',
    icon: '💊',
    items: [
      { name: '维生素', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
      { name: '蛋白粉', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
      { name: '钙片', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
    ],
  },
  {
    id: 15,
    name: '日用品',
    icon: '🧴',
    items: [
      { name: '洗发水', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
      { name: '牙膏', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
    ],
  },
  {
    id: 16,
    name: '清洁用品',
    icon: '🧽',
    items: [
      { name: '洗衣液', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
      { name: '洗洁精', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
      { name: '消毒液', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
    ],
  },
  {
    id: 17,
    name: '厨具',
    icon: '🍳',
    items: [
      { name: '平底锅', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
      { name: '菜刀', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
    ],
  },
  {
    id: 18,
    name: '文具',
    icon: '✏️',
    items: [
      { name: '笔记本', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
      { name: '圆珠笔', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
      { name: '橡皮擦', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
    ],
  },
  {
    id: 19,
    name: '电子产品',
    icon: '📱',
    items: [
      { name: '充电器', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
      { name: '耳机', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
    ],
  },
  {
    id: 20,
    name: '服装',
    icon: '👕',
    items: [
      { name: 'T恤', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
      { name: '牛仔裤', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
      { name: '运动鞋', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
    ],
  },
  {
    id: 21,
    name: '玩具',
    icon: '🧸',
    items: [
      { name: '积木', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
      { name: '娃娃', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
    ],
  },
  {
    id: 22,
    name: '运动用品',
    icon: '⚽',
    items: [
      { name: '篮球', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
      { name: '跑步机', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
      { name: '瑜伽垫', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
    ],
  },
  {
    id: 23,
    name: '宠物用品',
    icon: '🐕',
    items: [
      { name: '狗粮', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
      { name: '猫砂', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
    ],
  },
  {
    id: 24,
    name: '花卉园艺',
    icon: '🌸',
    items: [
      { name: '玫瑰花', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
      { name: '花盆', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
      { name: '肥料', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
    ],
  },
  {
    id: 25,
    name: '汽车用品',
    icon: '🚗',
    items: [
      { name: '机油', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
      { name: '车载充电器', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
    ],
  },
])

// 页面加载时获取各分类的位置
onMounted(() => {
  // 延迟获取位置，确保DOM完全渲染
  setTimeout(() => {
    updateCategoryPositions()
  }, 500)
})

// 更新分类位置信息
function updateCategoryPositions() {
  getRect('.category', true).then((rects) => {
    if (isArray(rects)) {
      itemScrollTop.value = rects.map((item) => item.top || 0)
      console.log('分类位置信息:', itemScrollTop.value)
    }
  })
}

// 添加滚动防抖标志
let isScrollingByClick = false
let scrollTimer = null

// 处理侧边栏切换
function handleSidebarChange(event) {
  const index = event.value

  activeCategory.value = index

  // 设置点击滚动标志，防止滚动事件干扰
  isScrollingByClick = true

  // 使用 scroll-into-view 实现平滑滑动
  scrollIntoView.value = `category-${index}`

  // 延迟清空 scrollIntoView，避免影响后续滚动
  setTimeout(() => {
    scrollIntoView.value = ''
    // 重新获取位置信息，确保数据准确
    setTimeout(() => {
      updateCategoryPositions()
      isScrollingByClick = false
    }, 200)
  }, 300)
}

// 处理右侧区域滚动
function onScroll(event) {
  // 如果是点击触发的滚动，暂时忽略滚动事件
  if (isScrollingByClick) {
    return
  }

  const { scrollTop: currentScrollTop } = event.detail

  // 清除之前的定时器
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }

  // 使用防抖，避免频繁更新
  scrollTimer = setTimeout(() => {
    updateActiveCategory(currentScrollTop)
  }, 30)
}

// 更新当前激活的分类
function updateActiveCategory(scrollTop) {
  if (itemScrollTop.value.length === 0) {
    return
  }

  let targetIndex = 0
  const threshold = 150 // 增大阈值，让分类选中更提前

  // 从后往前遍历，找到当前滚动位置对应的分类
  for (let i = itemScrollTop.value.length - 1; i >= 0; i--) {
    const categoryTop = itemScrollTop.value[i]

    // 如果当前滚动位置大于等于某个分类的顶部位置（减去阈值），则该分类应该被激活
    if (scrollTop >= categoryTop - threshold) {
      targetIndex = i
      break
    }
  }

  // 更新选中状态
  if (activeCategory.value !== targetIndex) {
    activeCategory.value = targetIndex
    console.log(
      '切换到分类:',
      targetIndex,
      '滚动位置:',
      scrollTop,
      '分类顶部:',
      itemScrollTop.value[targetIndex],
    )
  }
}

// 处理搜索框聚焦
const handleSearchFocus = () => {
  console.log('搜索框聚焦')
}

// 处理搜索输入
const handleSearchInput = () => {
  // 当输入内容变化时，设置搜索状态
  const isEmpty = searchValue.value.trim() === ''
  isSearching.value = !isEmpty

  // 注意：清空操作由handleSearchClear处理
  // 这里只处理输入变化
}

// 处理搜索框内容变化事件（包括手动清空）
const handleSearchChange = (event) => {
  const value = event.value || ''

  // 如果内容为空，重置搜索状态并恢复原始分类显示
  if (value.trim() === '') {
    isSearching.value = false

    // 恢复原始分类显示
    nextTick(() => {
      setTimeout(() => {
        updateCategoryPositions()
      }, 200)
    })
  } else {
    // 如果有内容，设置搜索状态
    isSearching.value = true
  }
}

// 处理搜索事件
const handleSearch = () => {
  // 如果搜索内容为空，不执行搜索
  if (!searchValue.value.trim()) {
    isSearching.value = false
    return
  }

  console.log('搜索内容:', searchValue.value)
  isSearching.value = true

  // 搜索后更新位置信息
  if (filteredCategories.value.length > 0) {
    nextTick(() => {
      setTimeout(() => {
        updateCategoryPositions()
      }, 200)
    })
  }
}

// 处理商品项点击
const handleItemClick = (item: any) => {
  console.log('点击商品:', item.name)

  // 获取当前选中的分类信息
  const currentCategory = categories.value[activeCategory.value]

  // 跳转到搜索页面，传递分类和商品信息
  uni.navigateTo({
    url: `/pages/user/search?fromCategory=true&categoryName=${encodeURIComponent(currentCategory.name)}&itemName=${encodeURIComponent(item.name)}`,
  })
}

// 根据搜索关键词过滤分类和商品
const filteredCategories = computed(() => {
  if (!searchValue.value.trim()) {
    return []
  }

  const keyword = searchValue.value.toLowerCase().trim()

  return categories.value
    .map((category) => {
      // 过滤符合条件的商品项
      const filteredItems = category.items.filter((item) =>
        item.name.toLowerCase().includes(keyword),
      )

      // 如果该分类下有匹配的商品，返回包含这些商品的新分类对象
      if (filteredItems.length > 0) {
        return {
          ...category,
          items: filteredItems,
        }
      }

      // 如果分类名称匹配关键词，返回完整分类
      if (category.name.toLowerCase().includes(keyword)) {
        return category
      }

      // 否则返回null，表示该分类不匹配
      return null
    })
    .filter(Boolean) // 过滤掉null值
})

// 显示的分类数据（搜索时显示过滤结果，否则显示全部）
const displayCategories = computed(() => {
  return isSearching.value ? filteredCategories.value : categories.value
})

// 处理搜索框清空事件
const handleSearchClear = () => {
  // 重置搜索状态
  isSearching.value = false
  searchValue.value = ''

  // 恢复原始分类显示
  nextTick(() => {
    setTimeout(() => {
      updateCategoryPositions()
    }, 200)
  })
}
</script>

<style lang="scss" scoped>
.category-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  box-sizing: border-box;
}

// 角色切换区域样式
.role-switch-section {
  background-color: #fff;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;

  .section-title {
    font-size: 12px;
    color: #999;
    margin-bottom: 8px;
    display: block;
  }

  .button-group {
    display: flex;
    gap: 10px;
    margin-bottom: 8px;
  }

  .current-role {
    font-size: 12px;
    color: #666;
  }
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #eee;

  :deep(.search-component) {
    // 动态右边距通过 style 属性设置
  }
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
}

:deep(.sidebar-container) {
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
}

.right-content {
  flex: 1;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.right-scroll {
  flex: 1;
  height: 100%;
}

.category {
  margin-bottom: 15px;

  &:first-child {
    margin-top: 15px;
  }
}

.category-card {
  margin: 0 10px;
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px 4px 20px;
}

.card-title {
  font-size: 16px;
  font-weight: 400;
  color: #333;
}

.arrow-icon {
  font-size: 25px;
  color: #666;
  transform: rotate(0deg);
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5px;
  padding: 1px;
}

.item-card {
  background: #fff;
  padding: 8px 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: opacity 0.2s;
  min-height: 80px;
  justify-content: center;
  border-radius: 8px;

  &:active {
    opacity: 0.7;
  }
}

.item-image {
  margin-bottom: 4px;
}

.item-name {
  font-size: 12px;
  color: #333;
  text-align: center;
  line-height: 1.2;
}

.empty-state {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: #f8f9fa;
}
</style>
