<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '店铺分类',
  },
  access: {
    requireAuth: false,
  },
}
</route>

<template>
  <view class="merchant-category-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: safeAreaInsetsTop + 'px' }">
      <view class="navbar-content" :style="{ paddingRight: rightSafeArea + 'px' }">
        <view class="navbar-search">
          <wd-search
            v-model="searchValue"
            placeholder="搜索商品、类目"
            hide-cancel
            @focus="handleSearchFocus"
            @search="handleSearch"
            @input="handleSearchInput"
            @change="handleSearchChange"
            @clear="handleSearchClear"
          />
        </view>
      </view>
    </view>

    <!-- 分类布局内容 -->
    <view class="category-content" :style="{ marginTop: safeAreaInsetsTop + 64 + 'px' }">
      <view class="category-layout-container">
        <!-- 搜索结果为空时显示空状态 -->
        <view v-if="isSearching && filteredCategories.length === 0" class="empty-state">
          <wd-status-tip image="search" tip="当前搜索无结果" />
        </view>

        <!-- 有搜索结果或未搜索时显示分类列表 -->
        <view v-else class="main-content">
          <!-- 左侧分类导航 -->
          <view class="sidebar-wrapper">
            <wd-sidebar
              v-model="activeCategory"
              @change="handleSidebarChange"
              custom-class="sidebar-container"
            >
              <wd-sidebar-item
                v-for="(category, index) in displayCategories"
                :key="index"
                :value="index"
                :label="category.name"
              />
            </wd-sidebar>
          </view>

          <!-- 右侧商品展示区域 -->
          <view class="right-content">
            <scroll-view
              scroll-y
              class="right-scroll"
              :scroll-into-view="scrollIntoView"
              :scroll-with-animation="true"
              @scroll="onScroll"
            >
              <view
                v-for="(category, categoryIndex) in displayCategories"
                :key="categoryIndex"
                :id="`category-${categoryIndex}`"
                :class="`category category-${categoryIndex}`"
              >
                <view class="category-card">
                  <view class="card-header">
                    <text class="card-title">{{ category.name }}</text>
                    <view class="arrow-icon">›</view>
                  </view>
                  <view class="items-grid">
                    <view
                      class="item-card"
                      v-for="(item, index) in category.items"
                      :key="index"
                      @click="handleItemClick(item, category)"
                    >
                      <view class="item-image">
                        <wd-img :src="item.image" mode="aspectFill" :width="60" :height="60" />
                      </view>
                      <text class="item-name">{{ item.name }}</text>
                    </view>
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部导航栏 -->
    <MerchantBottomNav :current-nav="'category'" :merchant-id="merchantId" />
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useGlobalSafeArea } from '@/hooks/useSafeArea'
import MerchantBottomNav from '@/components/merchant-bottom-nav/index.vue'

defineOptions({
  name: 'MerchantCategory',
})

// 获取安全区域距离
const { safeAreaInsetsTop, rightSafeArea } = useGlobalSafeArea()

// 页面参数
const merchantId = ref('')

// 分类相关状态
const activeCategory = ref(0)
const scrollIntoView = ref('')
const itemScrollTop = ref([])

// 搜索相关状态
const searchValue = ref('')
const isSearching = ref(false)

// 页面加载时获取参数
onLoad((options: any) => {
  console.log('店铺分类页面参数:', options)
  if (options.id) {
    merchantId.value = options.id
  }
})

// 页面加载时获取各分类的位置
onMounted(() => {
  // 延迟获取位置，确保DOM完全渲染
  setTimeout(() => {
    updateCategoryPositions()
  }, 800)
})

// 根据搜索关键词过滤分类和商品
const filteredCategories = computed(() => {
  if (!searchValue.value.trim()) {
    return []
  }

  const keyword = searchValue.value.toLowerCase().trim()

  return categories.value
    .map((category) => {
      // 过滤符合条件的商品项
      const filteredItems = category.items.filter((item) =>
        item.name.toLowerCase().includes(keyword),
      )

      // 如果该分类下有匹配的商品，返回包含这些商品的新分类对象
      if (filteredItems.length > 0) {
        return {
          ...category,
          items: filteredItems,
        }
      }

      // 如果分类名称匹配关键词，返回完整分类
      if (category.name.toLowerCase().includes(keyword)) {
        return category
      }

      // 否则返回null，表示该分类不匹配
      return null
    })
    .filter(Boolean) // 过滤掉null值
})

// 显示的分类数据（搜索时显示过滤结果，否则显示全部）
const displayCategories = computed(() => {
  return isSearching.value ? filteredCategories.value : categories.value
})

// 搜索相关事件处理
const handleSearchFocus = () => {
  console.log('搜索框聚焦')
}

const handleSearchInput = () => {
  // 当输入内容变化时，设置搜索状态
  const isEmpty = searchValue.value.trim() === ''
  isSearching.value = !isEmpty
}

const handleSearchChange = (event) => {
  const value = event.value || ''

  // 如果内容为空，重置搜索状态并恢复原始分类显示
  if (value.trim() === '') {
    isSearching.value = false

    // 恢复原始分类显示
    nextTick(() => {
      setTimeout(() => {
        updateCategoryPositions()
      }, 200)
    })
  } else {
    // 如果有内容，设置搜索状态
    isSearching.value = true
  }
}

const handleSearch = () => {
  // 如果搜索内容为空，不执行搜索
  if (!searchValue.value.trim()) {
    isSearching.value = false
    return
  }

  console.log('搜索内容:', searchValue.value)
  isSearching.value = true

  // 搜索后更新位置信息
  if (filteredCategories.value.length > 0) {
    nextTick(() => {
      setTimeout(() => {
        updateCategoryPositions()
      }, 200)
    })
  }
}

const handleSearchClear = () => {
  // 重置搜索状态
  isSearching.value = false
  searchValue.value = ''

  // 恢复原始分类显示
  nextTick(() => {
    setTimeout(() => {
      updateCategoryPositions()
    }, 200)
  })
}

// 更新分类位置信息
function updateCategoryPositions() {
  const query = uni.createSelectorQuery()
  query
    .selectAll('.category')
    .boundingClientRect((rects) => {
      if (Array.isArray(rects) && rects.length > 0) {
        itemScrollTop.value = rects.map((item) => item.top || 0)
        console.log('分类位置信息:', itemScrollTop.value)
      }
    })
    .exec()
}

// 添加滚动防抖标志
let isScrollingByClick = false
let scrollTimer = null

// 处理侧边栏切换
function handleSidebarChange(event) {
  const index = event.value

  activeCategory.value = index

  // 设置点击滚动标志，防止滚动事件干扰
  isScrollingByClick = true

  // 使用 scroll-into-view 实现平滑滑动
  scrollIntoView.value = `category-${index}`

  // 延迟清空 scrollIntoView，避免影响后续滚动
  setTimeout(() => {
    scrollIntoView.value = ''
    // 重新获取位置信息，确保数据准确
    setTimeout(() => {
      updateCategoryPositions()
      isScrollingByClick = false
    }, 200)
  }, 300)
}

// 处理右侧区域滚动
function onScroll(event) {
  // 如果是点击触发的滚动，暂时忽略滚动事件
  if (isScrollingByClick) {
    return
  }

  const { scrollTop: currentScrollTop } = event.detail

  // 清除之前的定时器
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }

  // 使用防抖，避免频繁更新
  scrollTimer = setTimeout(() => {
    updateActiveCategory(currentScrollTop)
  }, 30)
}

// 更新当前激活的分类
function updateActiveCategory(scrollTop) {
  if (itemScrollTop.value.length === 0) {
    return
  }

  let targetIndex = 0
  const threshold = 150 // 增大阈值，让分类选中更提前

  // 从后往前遍历，找到当前滚动位置对应的分类
  for (let i = itemScrollTop.value.length - 1; i >= 0; i--) {
    const categoryTop = itemScrollTop.value[i]

    // 如果当前滚动位置大于等于某个分类的顶部位置（减去阈值），则该分类应该被激活
    if (scrollTop >= categoryTop - threshold) {
      targetIndex = i
      break
    }
  }

  // 更新选中状态
  if (activeCategory.value !== targetIndex) {
    activeCategory.value = targetIndex
    console.log(
      '切换到分类:',
      targetIndex,
      '滚动位置:',
      scrollTop,
      '分类顶部:',
      itemScrollTop.value[targetIndex],
    )
  }
}

// 分类数据
const categories = ref([
  {
    id: 1,
    name: '新鲜水果',
    icon: '🍎',
    items: [
      { name: '苹果', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
      { name: '香蕉', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
      { name: '橙子', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
      { name: '葡萄', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
      { name: '草莓', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
      { name: '芒果', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
    ],
  },
  {
    id: 2,
    name: '精选蔬菜',
    icon: '🥬',
    items: [
      { name: '白菜', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
      { name: '萝卜', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
      { name: '西红柿', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
      { name: '黄瓜', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
    ],
  },
  {
    id: 3,
    name: '优质肉类',
    icon: '🥩',
    items: [
      { name: '猪肉', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
      { name: '牛肉', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
      { name: '鸡肉', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
      { name: '羊肉', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
    ],
  },
  {
    id: 4,
    name: '新鲜海鲜',
    icon: '🦐',
    items: [
      { name: '虾', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
      { name: '蟹', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
      { name: '鱼', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
      { name: '贝类', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
    ],
  },
  {
    id: 5,
    name: '健康饮品',
    icon: '🥤',
    items: [
      { name: '果汁', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
      { name: '牛奶', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
      { name: '酸奶', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
      { name: '茶饮', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
    ],
  },
  {
    id: 6,
    name: '休闲零食',
    icon: '🍿',
    items: [
      { name: '薯片', image: 'https://img.yzcdn.cn/vant/apple-3.jpg' },
      { name: '巧克力', image: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
      { name: '坚果', image: 'https://img.yzcdn.cn/vant/apple-4.jpg' },
      { name: '饼干', image: 'https://img.yzcdn.cn/vant/apple-2.jpg' },
    ],
  },
])

// 处理商品项点击
const handleItemClick = (item: any, category: any) => {
  console.log('点击商品:', item.name, '分类:', category.name)

  // 跳转到商户全部宝贝页面，并搜索该商品，同时传递分类信息
  uni.redirectTo({
    url: `/pages/user/merchant-goods?id=${merchantId.value}&search=${encodeURIComponent(
      item.name,
    )}&category=${encodeURIComponent(category.name)}`,
  })
}
</script>

<style lang="scss" scoped>
.merchant-category-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
  padding: 5px 0px;
  box-sizing: border-box;
  border-bottom: 1px solid #eee;
}

.navbar-content {
  display: flex;
  align-items: center;
  height: 44px;
}

.navbar-search {
  flex: 1;
}

/* 分类内容区域 */
.category-content {
  flex: 1;
  overflow: hidden;
}

.category-layout-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  box-sizing: border-box;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
}

:deep(.sidebar-container) {
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
}

.right-content {
  flex: 1;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.right-scroll {
  flex: 1;
  height: 100%;
}

.category {
  margin-bottom: 15px;

  &:first-child {
    margin-top: 15px;
  }
}

.category-card {
  margin: 0 10px;
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px 4px 20px;
}

.card-title {
  font-size: 16px;
  font-weight: 400;
  color: #333;
}

.arrow-icon {
  font-size: 25px;
  color: #666;
  transform: rotate(0deg);
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5px;
  padding: 1px;
}

.item-card {
  background: #fff;
  padding: 8px 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: opacity 0.2s;
  min-height: 80px;
  justify-content: center;
  border-radius: 8px;

  &:active {
    opacity: 0.7;
  }
}

.item-image {
  margin-bottom: 4px;
}

.item-name {
  font-size: 12px;
  color: #333;
  text-align: center;
  line-height: 1.2;
}

.empty-state {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: #f8f9fa;
}
</style>
