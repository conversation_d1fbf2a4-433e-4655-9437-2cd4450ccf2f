<route lang="json5">
{
  access: {
    requireAuth: false,
  },
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '搜索',
  },
}
</route>

<template>
  <view class="search-container">
    <!-- 顶部红色背景区域 -->
    <view class="top-background" :style="{ paddingTop: safeAreaInsetsTop + 'px' }">
      <!-- 顶部搜索栏 -->
      <view class="header">
        <!-- 返回按钮 -->
        <wd-icon name="arrow-left" size="25px" color="#fff" @click="goBack" />

        <wd-search
          v-model="searchKeyword"
          :placeholder="`搜索${searchTarget}`"
          cancel-txt="搜索"
          :focus="shouldFocus"
          focusWhenClear
          placeholder-left
          @search="handleSearch"
          @focus="handleSearchFocus"
          @change="handleSearchInput"
          @cancel="handleSearch"
          custom-class="search-component"
          :custom-style="searchStyle"
          ref="searchRef"
        >
          <!-- 左侧搜索目标下拉框插槽 -->
          <template #prefix>
            <wd-popover mode="menu" :content="searchTargetMenu" @menuclick="changeSearchTarget">
              <view class="search-type">
                <text>{{ searchTarget }}</text>
                <wd-icon custom-class="icon-arrow" name="fill-arrow-down"></wd-icon>
              </view>
            </wd-popover>
            <wd-divider
              vertical
              :hairline="false"
              content-position="left"
              custom-class="search-divider"
            />
          </template>
        </wd-search>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 搜索建议 -->
      <view v-if="showSuggestions && suggestions.length > 0" class="suggestions-section">
        <view class="suggestions-list">
          <view
            v-for="(suggestion, index) in suggestions"
            :key="index"
            class="suggestion-item"
            @click="selectSuggestion(suggestion)"
          >
            <wd-icon name="search" size="16px" color="#999" />
            <text class="suggestion-text">{{ suggestion }}</text>
          </view>
        </view>
      </view>

      <!-- 搜索历史和热门搜索 -->
      <view v-if="!showResults && !showSuggestions" class="search-content">
        <!-- 搜索历史 -->
        <view v-if="searchHistory.length > 0" class="history-section">
          <view class="section-header">
            <text class="section-title">搜索历史</text>
            <wd-icon name="delete" size="16px" color="#999" @click="clearHistory" />
          </view>
          <view class="history-tags">
            <view
              v-for="(item, index) in searchHistory"
              :key="index"
              class="history-tag"
              @click="selectHistory(item)"
            >
              <text class="tag-text">{{ item }}</text>
            </view>
          </view>
        </view>

        <!-- 热门榜单 -->
        <view class="hot-section">
          <!-- Tab切换 -->
          <view class="hot-tabs">
            <view
              class="hot-tab"
              :class="{ active: activeHotTab === 'goods' }"
              @click="switchHotTab('goods')"
            >
              <text class="tab-text">商品榜</text>
            </view>
            <view
              class="hot-tab"
              :class="{ active: activeHotTab === 'merchant' }"
              @click="switchHotTab('merchant')"
            >
              <text class="tab-text">商户榜</text>
            </view>
          </view>

          <!-- 榜单列表 -->
          <view class="hot-ranking">
            <view
              v-for="(item, index) in currentHotList"
              :key="index"
              class="ranking-item"
              @click="selectHotItem(item)"
            >
              <view class="ranking-number">
                <text class="number-text">{{ index + 1 }}</text>
              </view>
              <view class="ranking-content">
                <text class="ranking-title">{{ item.title }}</text>
                <text class="ranking-desc">{{ item.desc }}</text>
              </view>
              <view class="ranking-trend">
                <wd-icon
                  :name="
                    item.trend === 'up'
                      ? 'arrow-up'
                      : item.trend === 'down'
                        ? 'arrow-down'
                        : 'minus'
                  "
                  :color="
                    item.trend === 'up' ? '#ff4d4f' : item.trend === 'down' ? '#52c41a' : '#999'
                  "
                  size="14px"
                />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 搜索结果 -->
      <view v-if="showResults" class="results-section">
        <!-- 排序筛选栏 - 包含所有控制按钮 -->
        <view class="sort-filter-bar">
          <!-- 排序选项 -->
          <view
            v-for="sort in sortOptions"
            :key="sort.value"
            class="sort-item"
            :class="{ active: activeSort === sort.value }"
            @click="handleSortChange(sort.value)"
          >
            <text class="sort-text">{{ sort.label }}</text>
            <wd-icon
              v-if="sort.value === 'price'"
              :name="priceOrder === 'asc' ? 'arrow-up' : 'arrow-down'"
              size="12px"
              :color="activeSort === 'price' ? '#f8293a' : '#999'"
            />
          </view>

          <!-- 右侧按钮组 -->
          <view class="right-actions">
            <!-- 筛选按钮 -->
            <view class="filter-button" @click="openFilterPopup">
              <text class="iconfont-sys iconsys-shaixuan"></text>
            </view>

            <!-- 显示模式切换 -->
            <view class="view-toggle-btn" @click="toggleViewMode">
              <text
                class="iconfont-sys"
                :class="viewMode === 'grid' ? 'iconsys-liebiao' : 'iconsys-more-grid-big'"
              ></text>
            </view>
          </view>
        </view>

        <!-- 商品列表容器 -->
        <scroll-view
          v-if="searchTarget === '商品'"
          class="results-scroll-container"
          scroll-y
          @scrolltolower="handleResultsReachBottom"
          :lower-threshold="200"
        >
          <view class="results-content">
            <!-- 网格模式 -->
            <WaterfallGoods
              v-if="viewMode === 'grid'"
              :goods-list="waterfallSearchResults"
              :loading="resultsLoading"
              :has-more="resultsHasMore"
              :column-count="2"
              :show-price="true"
              loading-text="加载中..."
              no-more-text="没有更多商品了"
              @goods-click="handleWaterfallGoodsClick"
              @like-click="handleWaterfallLikeClick"
            />

            <!-- 列表模式 -->
            <GoodsList
              v-else-if="viewMode === 'list'"
              :goods-list="listSearchResults"
              :loading="resultsLoading"
              :has-more="resultsHasMore"
              :show-price="true"
              loading-text="加载中..."
              no-more-text="没有更多商品了"
              @goods-click="handleListGoodsClick"
              @like-click="handleListLikeClick"
            />
          </view>
        </scroll-view>

        <!-- 商户列表容器 -->
        <scroll-view
          v-if="searchTarget === '店铺'"
          class="results-scroll-container"
          scroll-y
          @scrolltolower="handleMerchantResultsReachBottom"
          :lower-threshold="200"
        >
          <view class="results-content merchant-content">
            <MerchantList
              :merchant-list="merchantSearchResults"
              :loading="resultsLoading"
              :has-more="resultsHasMore"
              :show-follow="false"
              :show-distance="true"
              :show-rating="false"
              loading-text="加载中..."
              no-more-text="没有更多商户了"
              @merchant-click="handleMerchantClick"
              @follow-click="handleMerchantFollowClick"
            />
          </view>
        </scroll-view>

        <!-- 空状态 -->
        <view v-if="showEmptyState" class="empty-state">
          <wd-icon name="search" size="60px" color="#ccc" />
          <text class="empty-text">{{ emptyStateText }}</text>
          <text class="empty-tip">试试其他关键词吧</text>
        </view>
      </view>
    </view>

    <!-- 筛选弹窗 -->
    <FilterPopup
      :visible="showFilterPopup"
      :view-mode="viewMode"
      :filter-options="filterOptions"
      :current-filters="currentFilters"
      :show-view-mode-switch="false"
      @update:visible="showFilterPopup = $event"
      @update:view-mode="viewMode = $event"
      @update:current-filters="currentFilters = $event"
      @apply="applyFilters"
      @reset="resetFilters"
    />
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import WaterfallGoods from '@/components/waterfall-goods/index.vue'
import GoodsList from '@/components/goods-list/index.vue'
import MerchantList from '@/components/merchant-list/index.vue'
import FilterPopup from '@/components/filter-popup/index.vue'
import type { FilterOptions, FilterValues } from '@/components/filter-popup/types'
import type { MerchantItem } from '@/components/merchant-list/types'
import { useGlobalSafeArea } from '@/hooks/useSafeArea'
import { isMp } from '@/utils/platform'

// 定义瀑布流商品类型
interface WaterfallGoodsItem {
  id: number | string
  title: string
  price?: number
  originalPrice?: number | null
  color?: string
  sales: number
  isLiked: boolean
  imageHeight: number
  image?: string
}

// 获取屏幕边界到安全区域距离
const { safeAreaInsetsTop, safeAreaInsetsBottom, rightSafeArea } = useGlobalSafeArea()

// 获取系统信息，用于小程序胶囊适配
const systemInfo = uni.getSystemInfoSync()

// 小程序胶囊信息
const capsuleInfo = ref<any>({})
if (isMp) {
  // #ifdef MP
  capsuleInfo.value = uni.getMenuButtonBoundingClientRect()
  // #endif
}

// 搜索相关状态
const searchKeyword = ref('')
const showSuggestions = ref(false)
const showResults = ref(false)
const searchRef = ref()

// 分类相关状态
const fromCategory = ref(false)
const categoryName = ref('')
const originalItemName = ref('')

// 搜索目标相关状态
const searchTarget = ref('商品')
const searchTargets = ref(['商品', '店铺'])
const searchTargetMenu = ref([
  {
    content: '商品',
  },
  {
    content: '店铺',
  },
])

// 焦点控制状态
const shouldFocus = ref(false)

// 计算是否应该自动聚焦
const shouldAutoFocus = computed(() => {
  const result = !fromCategory.value
  console.log('计算是否应该聚焦:', result, 'fromCategory:', fromCategory.value)
  return result
})

// 计算搜索框样式，适配小程序胶囊
const searchStyle = computed(() => {
  if (isMp && capsuleInfo.value.width) {
    // 计算搜索框右边距，避免被胶囊遮挡
    const rightMargin = systemInfo.windowWidth - capsuleInfo.value.left - 5
    return `margin-right: ${rightMargin}px`
  }
  return ''
})

// 搜索建议
const suggestions = ref<string[]>([])

// 搜索历史
const searchHistory = ref<string[]>(['苹果', '牛奶', '面包', '鸡蛋'])

// 热门榜单
const activeHotTab = ref('goods')

// 热门商品榜单
const hotGoods = ref([
  { title: '新鲜苹果', desc: '搜索热度 99.8万', trend: 'up' },
  { title: '有机牛奶', desc: '搜索热度 88.6万', trend: 'up' },
  { title: '进口零食', desc: '搜索热度 76.3万', trend: 'down' },
  { title: '优质大米', desc: '搜索热度 65.2万', trend: 'up' },
  { title: '新鲜蔬菜', desc: '搜索热度 58.9万', trend: 'same' },
  { title: '健康饮品', desc: '搜索热度 52.1万', trend: 'down' },
  { title: '特价商品', desc: '搜索热度 48.7万', trend: 'up' },
  { title: '本地特产', desc: '搜索热度 42.3万', trend: 'same' },
])

// 热门商户榜单
const hotMerchants = ref([
  { title: '新鲜果园旗舰店', desc: '月销量 12.8万', trend: 'up' },
  { title: '优选生鲜超市', desc: '月销量 10.6万', trend: 'up' },
  { title: '有机生活馆', desc: '月销量 9.2万', trend: 'down' },
  { title: '品质食材店', desc: '月销量 8.5万', trend: 'up' },
  { title: '健康美食坊', desc: '月销量 7.8万', trend: 'same' },
  { title: '进口食品专营', desc: '月销量 6.9万', trend: 'down' },
  { title: '本地农产品', desc: '月销量 6.2万', trend: 'up' },
  { title: '精品零食铺', desc: '月销量 5.7万', trend: 'same' },
])

// 当前显示的榜单
const currentHotList = computed(() => {
  return activeHotTab.value === 'goods' ? hotGoods.value : hotMerchants.value
})

// 搜索结果
const searchResults = ref<any[]>([])

// 商户搜索结果
const merchantSearchResults = ref<MerchantItem[]>([])

// 显示模式和排序相关状态
const viewMode = ref<'list' | 'grid'>('grid') // 显示模式：'list' 列表视图, 'grid' 网格视图
const activeSort = ref('comprehensive') // 当前排序方式
const priceOrder = ref('desc') // 价格排序：'asc' 升序, 'desc' 降序
const resultsLoading = ref(false) // 搜索结果加载状态
const resultsHasMore = ref(true) // 是否还有更多搜索结果
const resultsCurrentPage = ref(1) // 搜索结果当前页码

// 排序选项
const sortOptions = computed(() => {
  const baseOptions = [
    { label: '销量', value: 'sales' },
    { label: '价格', value: 'price' },
    { label: '新上架', value: 'newest' },
  ]

  // 如果是从分类页面跳转，第一个选项显示分类名称
  if (fromCategory.value && originalItemName.value) {
    return [{ label: originalItemName.value, value: 'comprehensive' }, ...baseOptions]
  } else {
    return [{ label: '综合', value: 'comprehensive' }, ...baseOptions]
  }
})

// 瀑布流格式的搜索结果
const waterfallSearchResults = ref<WaterfallGoodsItem[]>([])

// 列表格式的搜索结果
const listSearchResults = computed(() => {
  return waterfallSearchResults.value.map((item) => ({
    id: item.id,
    title: item.title,
    price: item.price,
    originalPrice: item.originalPrice,
    sales: item.sales,
    isLiked: item.isLiked,
    image: item.image || '',
  }))
})

// 模拟搜索建议数据
const allSuggestions = [
  '苹果',
  '苹果汁',
  '苹果派',
  '红苹果',
  '青苹果',
  '牛奶',
  '纯牛奶',
  '酸奶',
  '面包',
  '全麦面包',
  '吐司面包',
]

// 筛选相关状态
const showFilterPopup = ref(false)
const filterOptions: FilterOptions = {
  distance: [
    { label: '5km以内', value: '5km' },
    { label: '10km以内', value: '10km' },
    { label: '20km以内', value: '20km' },
    { label: '50km以内', value: '50km' },
    { label: '不限距离', value: 'unlimited' },
  ],
  freshness: [
    { label: '不限', value: 'all' },
    { label: '当日新鲜', value: 'today' },
    { label: '3日内', value: 'three_days' },
    { label: '一周内', value: 'week' },
  ],
}

// 当前筛选条件
const currentFilters = ref<FilterValues>({
  priceMin: '',
  priceMax: '',
  distance: '5km',
  freshness: 'all',
})

// 空状态相关计算属性
const showEmptyState = computed(() => {
  if (resultsLoading.value) return false

  if (searchTarget.value === '店铺') {
    return merchantSearchResults.value.length === 0
  } else {
    return searchResults.value.length === 0
  }
})

const emptyStateText = computed(() => {
  return searchTarget.value === '店铺' ? '没有找到相关商户' : '没有找到相关商品'
})

// 返回上一页
const goBack = () => {
  const pages = getCurrentPages()
  if (pages.length > 1) {
    uni.navigateBack()
  } else {
    // 如果是第一页，跳转到首页
    uni.reLaunch({
      url: '/pages/user/index',
    })
  }
}

// 选择搜索目标
const changeSearchTarget = ({ item, index }: { item: any; index: number }) => {
  searchTarget.value = item.content
}

// 搜索框聚焦
const handleSearchFocus = () => {
  console.log('搜索框获得焦点')
  showResults.value = false
  if (searchKeyword.value.trim()) {
    updateSuggestions(searchKeyword.value)
    showSuggestions.value = true
  }
}

// 搜索输入
const handleSearchInput = ({ value }: { value: string }) => {
  searchKeyword.value = value
  if (value.trim()) {
    updateSuggestions(value)
    showSuggestions.value = true
    showResults.value = false
  } else {
    // 清除分类状态（但不清空搜索框，因为用户正在输入）
    fromCategory.value = false
    categoryName.value = ''
    originalItemName.value = ''

    // 清空搜索结果
    searchResults.value = []
    waterfallSearchResults.value = []
    merchantSearchResults.value = []

    // 当输入框被清空时，清空所有状态
    showSuggestions.value = false
    showResults.value = false
  }
}

// 更新搜索建议
const updateSuggestions = (keyword: string) => {
  suggestions.value = allSuggestions
    .filter((item) => item.toLowerCase().includes(keyword.toLowerCase()))
    .slice(0, 5)
}

// 执行搜索
const handleSearch = () => {
  // 处理搜索关键词，如果包含"分类："前缀则提取实际关键词
  let actualKeyword = searchKeyword.value.trim()

  if (actualKeyword.startsWith('分类：')) {
    actualKeyword = actualKeyword.replace('分类：', '').trim()
  }

  if (!actualKeyword) {
    // 当搜索关键词为空时，清空搜索结果并回到初始状态
    clearCategoryState()
    searchResults.value = []
    waterfallSearchResults.value = []
    console.log('搜索关键词为空，已清空搜索结果')
    return
  }

  // 添加到搜索历史（保存实际关键词，不包含前缀）
  addToHistory(actualKeyword)

  // 隐藏建议，显示结果
  showSuggestions.value = false
  showResults.value = true

  // 模拟搜索结果（使用实际关键词）
  performSearch(actualKeyword)
}

// 选择搜索建议
const selectSuggestion = (suggestion: string) => {
  searchKeyword.value = suggestion
  handleSearch()
}

// 选择搜索历史
const selectHistory = (item: string) => {
  searchKeyword.value = item

  // 如果历史记录包含"分类："前缀，需要设置分类状态
  if (item.startsWith('分类：')) {
    const itemName = item.replace('分类：', '').trim()
    fromCategory.value = true
    originalItemName.value = itemName
    // 可以设置一个默认的分类名，或者从历史记录中解析
    categoryName.value = '历史分类'
  }

  handleSearch()
}

// 切换热门榜单tab
const switchHotTab = (tab: string) => {
  activeHotTab.value = tab
}

// 选择榜单项
const selectHotItem = (item: any) => {
  searchKeyword.value = item.title
  handleSearch()
}

// 添加到搜索历史
const addToHistory = (keyword: string) => {
  const index = searchHistory.value.indexOf(keyword)
  if (index > -1) {
    searchHistory.value.splice(index, 1)
  }
  searchHistory.value.unshift(keyword)
  if (searchHistory.value.length > 10) {
    searchHistory.value = searchHistory.value.slice(0, 10)
  }
  // TODO: 保存到本地存储
}

// 清空搜索历史
const clearHistory = () => {
  uni.showModal({
    title: '提示',
    content: '确定要清空搜索历史吗？',
    success: (res) => {
      if (res.confirm) {
        searchHistory.value = []
      }
    },
  })
}

// 执行搜索
const performSearch = (keyword: string) => {
  resultsLoading.value = true
  resultsCurrentPage.value = 1

  if (searchTarget.value === '店铺') {
    // 商户搜索
    setTimeout(() => {
      const mockMerchants = generateMerchantResults(keyword, 1, 20)
      merchantSearchResults.value = mockMerchants
      resultsLoading.value = false
      resultsHasMore.value = mockMerchants.length >= 20
      console.log('商户搜索完成，设置resultsHasMore为true')
    }, 300)
  } else {
    // 商品搜索
    setTimeout(() => {
      const mockResults = generateSearchResults(keyword, 1, 20)
      searchResults.value = mockResults
      waterfallSearchResults.value = convertToWaterfallGoods(mockResults)
      resultsLoading.value = false
      resultsHasMore.value = mockResults.length >= 20
      console.log('商品搜索完成，设置resultsHasMore为true')
    }, 300)
  }
}

// 生成搜索结果数据
const generateSearchResults = (keyword: string, page: number, count: number) => {
  const titles = [
    `新鲜${keyword} - 优质产品`,
    `精选${keyword} - 品质保证`,
    `有机${keyword} - 健康之选`,
    `进口${keyword} - 高端品质`,
    `特价${keyword} - 超值优惠`,
    `本地${keyword} - 新鲜直供`,
    `无公害${keyword} - 安全可靠`,
    `绿色${keyword} - 天然健康`,
  ]

  const shops = [
    '新鲜果园旗舰店',
    '优选商城',
    '有机生活馆',
    '品质食材店',
    '进口食品专营',
    '本地农产品',
    '健康美食坊',
    '精品零食铺',
  ]

  return Array.from({ length: count }, (_, i) => {
    const index = (page - 1) * count + i
    return {
      id: `search_${index}`,
      title: titles[index % titles.length],
      image: `https://picsum.photos/300/300?random=${index}`,
      price: (Math.random() * 50 + 10).toFixed(2),
      originalPrice: Math.random() > 0.5 ? (Math.random() * 20 + 60).toFixed(2) : null,
      sales: Math.floor(Math.random() * 1000 + 50),
      shopName: shops[index % shops.length],
      isLiked: Math.random() > 0.7,
    }
  })
}

// 转换为瀑布流格式
const convertToWaterfallGoods = (results: any[]): WaterfallGoodsItem[] => {
  return results.map((item) => ({
    id: item.id,
    title: item.title,
    price: parseFloat(item.price),
    originalPrice: item.originalPrice ? parseFloat(item.originalPrice) : null,
    sales: item.sales,
    isLiked: item.isLiked,
    imageHeight: Math.floor(Math.random() * 100) + 150,
    image: item.image,
  }))
}

// 排序处理
const handleSortChange = (sortValue: string) => {
  if (sortValue === 'price' && activeSort.value === 'price') {
    // 如果点击的是当前价格排序，切换升降序
    priceOrder.value = priceOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    activeSort.value = sortValue
    if (sortValue === 'price') {
      priceOrder.value = 'desc' // 默认降序
    }
  }

  // 重新排序搜索结果
  sortSearchResults()
}

// 排序搜索结果
const sortSearchResults = () => {
  let sortedResults = [...searchResults.value]

  switch (activeSort.value) {
    case 'sales':
      sortedResults.sort((a, b) => b.sales - a.sales)
      break
    case 'price':
      if (priceOrder.value === 'asc') {
        sortedResults.sort((a, b) => parseFloat(a.price) - parseFloat(b.price))
      } else {
        sortedResults.sort((a, b) => parseFloat(b.price) - parseFloat(a.price))
      }
      break
    case 'newest':
      // 新上架排序，按ID倒序（假设ID越大越新）
      sortedResults.sort((a, b) => {
        const idA = parseInt(a.id.toString().replace('search_', ''))
        const idB = parseInt(b.id.toString().replace('search_', ''))
        return idB - idA
      })
      break
    case 'comprehensive':
    default:
      // 综合排序可以基于多个因子
      sortedResults.sort((a, b) => {
        const scoreA = a.sales * 0.3 + parseFloat(a.price) * 0.2 + (a.isLiked ? 100 : 0)
        const scoreB = b.sales * 0.3 + parseFloat(b.price) * 0.2 + (b.isLiked ? 100 : 0)
        return scoreB - scoreA
      })
      break
  }

  searchResults.value = sortedResults
  waterfallSearchResults.value = convertToWaterfallGoods(sortedResults)
}

// 瀑布流商品点击处理
const handleWaterfallGoodsClick = (item: WaterfallGoodsItem) => {
  console.log('瀑布流商品点击:', item)
  // 跳转到商品详情页面
  uni.navigateTo({
    url: `/pages/user/goods-detail?id=${item.id}&title=${encodeURIComponent(item.title)}`,
  })
}

// 瀑布流点赞处理
const handleWaterfallLikeClick = (item: WaterfallGoodsItem) => {
  const index = waterfallSearchResults.value.findIndex((goods) => goods.id === item.id)
  if (index !== -1) {
    waterfallSearchResults.value[index].isLiked = !waterfallSearchResults.value[index].isLiked
    // 同步更新原始搜索结果
    const originalIndex = searchResults.value.findIndex((goods) => goods.id === item.id)
    if (originalIndex !== -1) {
      searchResults.value[originalIndex].isLiked = waterfallSearchResults.value[index].isLiked
    }
    uni.showToast({
      title: waterfallSearchResults.value[index].isLiked ? '已收藏' : '已取消收藏',
      icon: 'success',
      duration: 1000,
    })
  }
}

// 列表商品点击处理
const handleListGoodsClick = (item: any) => {
  console.log('列表商品点击:', item)
  // 跳转到商品详情页面
  uni.navigateTo({
    url: `/pages/user/goods-detail?id=${item.id}&title=${encodeURIComponent(item.title)}`,
  })
}

// 列表点赞处理
const handleListLikeClick = (item: any) => {
  const index = waterfallSearchResults.value.findIndex((goods) => goods.id === item.id)
  if (index !== -1) {
    waterfallSearchResults.value[index].isLiked = !waterfallSearchResults.value[index].isLiked
    // 同步更新原始搜索结果
    const originalIndex = searchResults.value.findIndex((goods) => goods.id === item.id)
    if (originalIndex !== -1) {
      searchResults.value[originalIndex].isLiked = waterfallSearchResults.value[index].isLiked
    }
    uni.showToast({
      title: waterfallSearchResults.value[index].isLiked ? '已收藏' : '已取消收藏',
      icon: 'success',
      duration: 1000,
    })
  }
}

// 搜索结果触底加载更多
const handleResultsReachBottom = () => {
  if (!resultsLoading.value && resultsHasMore.value) {
    loadMoreSearchResults()
  }
}

// 加载更多搜索结果
const loadMoreSearchResults = () => {
  resultsLoading.value = true
  resultsCurrentPage.value += 1

  if (searchTarget.value === '店铺') {
    // 加载更多商户
    setTimeout(() => {
      const moreMerchants = generateMerchantResults(
        searchKeyword.value,
        resultsCurrentPage.value,
        10,
      )
      merchantSearchResults.value.push(...moreMerchants)
      resultsLoading.value = false
      resultsHasMore.value = moreMerchants.length >= 10
    }, 500)
  } else {
    // 加载更多商品
    setTimeout(() => {
      const moreResults = generateSearchResults(searchKeyword.value, resultsCurrentPage.value, 10)
      searchResults.value.push(...moreResults)
      waterfallSearchResults.value.push(...convertToWaterfallGoods(moreResults))
      resultsLoading.value = false
      resultsHasMore.value = moreResults.length >= 10
    }, 500)
  }
}

// 加载更多商户结果
const loadMoreMerchantResults = () => {
  resultsLoading.value = true
  resultsCurrentPage.value += 1

  setTimeout(() => {
    const moreMerchants = generateMerchantResults(searchKeyword.value, resultsCurrentPage.value, 10)
    merchantSearchResults.value.push(...moreMerchants)
    resultsLoading.value = false
    resultsHasMore.value = moreMerchants.length >= 10
  }, 500)
}

// 生成商户搜索结果数据
const generateMerchantResults = (keyword: string, page: number, count: number) => {
  const merchantNames = [
    `${keyword}专营店`,
    `精选${keyword}商城`,
    `${keyword}品质馆`,
    `优质${keyword}超市`,
    `${keyword}旗舰店`,
    `本地${keyword}商户`,
    `${keyword}直营店`,
    `特色${keyword}小店`,
  ]

  const descriptions = [
    '品质保证，服务至上',
    '新鲜直供，价格优惠',
    '老字号，值得信赖',
    '专业服务，贴心体验',
    '精选商品，品质生活',
    '本地特色，新鲜直达',
    '优质服务，放心购买',
    '特色精品，独家供应',
  ]

  const tags = [
    ['品质保证', '快速配送', '售后无忧'],
    ['新鲜直供', '当日达', '优质服务'],
    ['老字号', '口碑好', '信誉佳'],
    ['专业团队', '贴心服务', '品质优'],
    ['精选商品', '品质生活', '值得信赖'],
    ['本地特色', '新鲜直达', '价格实惠'],
    ['优质服务', '放心购买', '快速配送'],
    ['特色精品', '独家供应', '限量版'],
  ]

  // 生成热销商品的函数
  const generateHotGoods = (merchantIndex: number) => {
    const goodsCount = Math.floor(Math.random() * 4) + 3 // 每个商户3-6个热销商品
    return Array.from({ length: goodsCount }, (_, i) => ({
      id: `goods_${merchantIndex}_${i}`,
      image: `https://picsum.photos/200/200?random=${merchantIndex * 10 + i}`,
      price: parseFloat((Math.random() * 50 + 10).toFixed(2)),
    }))
  }

  return Array.from({ length: count }, (_, i) => {
    const index = (page - 1) * count + i
    return {
      id: `merchant_${index}`,
      name: merchantNames[index % merchantNames.length],
      logo: `https://picsum.photos/90/90?random=${index}`,
      description: descriptions[index % descriptions.length],
      address: `${keyword}路${Math.floor(Math.random() * 999) + 1}号`,
      distance: `${(Math.random() * 5 + 0.1).toFixed(1)}km`,
      followers: Math.floor(Math.random() * 5000 + 100),
      goodsCount: Math.floor(Math.random() * 500 + 50),
      rating: parseFloat((Math.random() * 2 + 3).toFixed(1)),
      isFollowed: Math.random() > 0.7,
      tags: tags[index % tags.length],
      status: (Math.random() > 0.8 ? 'closed' : 'open') as 'open' | 'closed' | 'break',
      hotGoods: generateHotGoods(index), // 添加热销商品数据
    }
  })
}

// 商户点击处理
const handleMerchantClick = (merchant: MerchantItem) => {
  console.log('商户点击:', merchant)
  // 跳转到商户主页
  uni.navigateTo({
    url: `/pages/user/merchant-home?id=${merchant.id}&name=${encodeURIComponent(merchant.name)}`,
  })
}

// 商户关注处理
const handleMerchantFollowClick = (merchant: MerchantItem) => {
  const index = merchantSearchResults.value.findIndex((item) => item.id === merchant.id)
  if (index !== -1) {
    merchantSearchResults.value[index].isFollowed = !merchantSearchResults.value[index].isFollowed
    uni.showToast({
      title: merchantSearchResults.value[index].isFollowed ? '已关注' : '已取消关注',
      icon: 'success',
      duration: 1000,
    })
  }
}

// 商户搜索结果触底加载更多
const handleMerchantResultsReachBottom = () => {
  if (!resultsLoading.value && resultsHasMore.value) {
    loadMoreMerchantResults()
  }
}

// 筛选弹窗处理
const openFilterPopup = () => {
  showFilterPopup.value = true
}

const applyFilters = (filters: FilterValues) => {
  console.log('应用筛选条件:', filters)
  showFilterPopup.value = false

  // 重新执行搜索并应用筛选条件
  if (searchKeyword.value.trim()) {
    performSearch(searchKeyword.value.trim())
  }
}

const resetFilters = () => {
  currentFilters.value = {
    priceMin: '',
    priceMax: '',
    distance: '5km',
    freshness: 'all',
  }
}

// 页面加载完成
onMounted(() => {
  console.log('搜索页面 onMounted 开始')

  // 尝试多种方式获取页面参数
  let options: any = null
  let isFromCategory = false

  // 方法1: 使用 getCurrentPages
  try {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    options = (currentPage as any).options
    console.log('方法1 - getCurrentPages 获取参数:', options)

    if (options && options.fromCategory === 'true') {
      isFromCategory = true
      processPageParams(options)
    }
  } catch (e) {
    console.log('方法1 失败:', e)
  }

  // 方法2: 如果方法1没有获取到分类参数，延迟重试
  if (!isFromCategory) {
    setTimeout(() => {
      try {
        const pages = getCurrentPages()
        const currentPage = pages[pages.length - 1]
        options = (currentPage as any).options
        console.log('方法2 - 延迟获取参数:', options)

        if (options && options.fromCategory === 'true') {
          isFromCategory = true
          processPageParams(options)
        } else {
          // 确认不是从分类页面跳转，手动聚焦
          console.log('确认直接访问，准备手动聚焦')

          // 方案1: 设置focus属性
          shouldFocus.value = true
          console.log('设置shouldFocus为true')

          // 方案2: 手动调用focus方法
          setTimeout(() => {
            if (searchRef.value) {
              try {
                if (typeof searchRef.value.focus === 'function') {
                  searchRef.value.focus()
                  console.log('直接访问，手动聚焦搜索框成功')
                } else {
                  console.log('searchRef.value.focus 不是函数:', typeof searchRef.value.focus)
                }
              } catch (focusError) {
                console.log('手动聚焦失败:', focusError)
              }
            } else {
              console.log('searchRef.value 为空')
            }
          }, 200)
        }
      } catch (e) {
        console.log('方法2 也失败:', e)
        // 如果都失败了，默认手动聚焦
        setTimeout(() => {
          shouldFocus.value = true
          console.log('兜底方案：设置shouldFocus为true')

          if (searchRef.value && typeof searchRef.value.focus === 'function') {
            searchRef.value.focus()
            console.log('兜底方案：手动聚焦搜索框')
          }
        }, 300)
      }
    }, 100)
  }

  console.log('搜索页面 onMounted 结束，isFromCategory:', isFromCategory)
})

// 处理页面参数的函数
const processPageParams = (options: any) => {
  console.log('开始处理页面参数:', options)

  fromCategory.value = true
  categoryName.value = decodeURIComponent(options.categoryName || '')
  originalItemName.value = decodeURIComponent(options.itemName || '')

  // 确保从分类跳转时不聚焦
  shouldFocus.value = false

  console.log('设置分类状态:', {
    fromCategory: fromCategory.value,
    categoryName: categoryName.value,
    itemName: originalItemName.value,
    shouldFocus: shouldFocus.value,
  })

  // 使用 nextTick 确保状态更新后再处理后续逻辑
  nextTick(() => {
    console.log('nextTick 中的 shouldAutoFocus:', shouldAutoFocus.value)

    // 手动失焦，防止自动弹出输入法
    if (searchRef.value && searchRef.value.blur) {
      searchRef.value.blur()
      console.log('手动失焦搜索框')
    }

    // 设置搜索关键词
    if (originalItemName.value) {
      searchKeyword.value = `分类：${originalItemName.value}`
      console.log('设置搜索关键词:', searchKeyword.value)

      // 执行搜索
      setTimeout(() => {
        const actualSearchKeyword = originalItemName.value
        if (actualSearchKeyword.trim()) {
          addToHistory(actualSearchKeyword.trim())
          showSuggestions.value = false
          showResults.value = true
          console.log('开始执行搜索，实际搜索关键词:', actualSearchKeyword)
          performSearch(actualSearchKeyword.trim())
        }
      }, 50)
    }
  })
}

// 切换视图模式
const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'grid' ? 'list' : 'grid'
}

// 清除分类状态
const clearCategoryState = () => {
  fromCategory.value = false
  categoryName.value = ''
  originalItemName.value = ''
  // 清空搜索框
  searchKeyword.value = ''
  // 隐藏搜索结果
  showResults.value = false
  showSuggestions.value = false
  // 清空所有搜索结果
  searchResults.value = []
  waterfallSearchResults.value = []
  merchantSearchResults.value = []
}
</script>

<style lang="scss" scoped>
.search-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
}

/* 顶部红色背景区域 */
.top-background {
  background: linear-gradient(to right, #ff4757, #ff3742);
}

/* 自定义导航栏 */
.header {
  background-color: transparent;
  padding: 8px 10px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  gap: 1px;
  position: relative;

  :deep(.search-component) {
    background-color: #f5f5f5;
    border-radius: 16px;
    flex: 1;
    height: 22px;
    margin-left: 4px;

    // 隐藏搜索图标
    .wd-icon-search {
      display: none !important;
    }

    // 调整输入框左边距，因为隐藏了图标
    .wd-search__input {
      padding-left: 0px !important;
    }
  }

  :deep(.wd-icon-arrow-left) {
    font-size: 25px !important;
    width: 25px;
    height: 25px;
    margin-left: 0px !important;
    margin-right: 0px !important;
    color: #fff !important;
  }
}

.search-type {
  height: 30px;
  line-height: 30px;
  font-size: 13px;
  color: #666;

  :deep(.icon-arrow) {
    display: inline-block;
    font-size: 16px;
    height: 100%;
    vertical-align: middle;
    line-height: 26px;
  }
}

.search-divider {
  margin: 10px 10px 10px 0px;
}

.main-content {
  flex: 1;
  overflow-y: auto;
}

// 搜索建议
.suggestions-section {
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

.suggestions-list {
  padding: 0;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  gap: 12px;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f5f5f5;
  }
}

.suggestion-text {
  font-size: 15px;
  color: #333;
}

// 搜索内容区域
.search-content {
  padding: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

// 搜索历史
.history-section {
  margin-bottom: 30px;
}

.history-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.history-tag {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 16px;
  background-color: transparent;

  &:active {
    background-color: #f5f5f5;
  }
}

.tag-text {
  font-size: 14px;
  color: #666;
}

// 热门榜单
.hot-section {
  margin-bottom: 20px;
}

.hot-tabs {
  display: flex;
  margin-bottom: 15px;
  background-color: #f8f8f8;
  border-radius: 6px;
  padding: 2px;
  width: 160px;
}

.hot-tab {
  flex: 1;
  text-align: left;
}

.tab-text {
  font-size: 13px;
  color: #666;

  .hot-tab.active & {
    color: #1890ff;
    font-weight: 500;
  }
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 8px;
  gap: 12px;

  // 前三名渐变背景
  &:nth-child(1) {
    background: linear-gradient(
      90deg,
      rgba(255, 107, 107, 0.15) 0%,
      rgba(255, 107, 107, 0.05) 70%,
      transparent 100%
    );
  }

  &:nth-child(2) {
    background: linear-gradient(
      90deg,
      rgba(255, 140, 0, 0.15) 0%,
      rgba(255, 140, 0, 0.05) 70%,
      transparent 100%
    );
  }

  &:nth-child(3) {
    background: linear-gradient(
      90deg,
      rgba(255, 165, 0, 0.15) 0%,
      rgba(255, 165, 0, 0.05) 70%,
      transparent 100%
    );
  }

  // 其他排名灰色背景
  &:nth-child(n + 4) {
    background-color: #f5f5f5;
  }

  &:active {
    opacity: 0.8;
  }
}

.ranking-number {
  width: auto;
  min-width: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.number-text {
  font-size: 16px;
  font-weight: 600;

  // 第一名红色
  .ranking-item:nth-child(1) & {
    color: #ff4d4f;
  }

  // 第二名橙红色
  .ranking-item:nth-child(2) & {
    color: #ff7a45;
  }

  // 第三名橙色
  .ranking-item:nth-child(3) & {
    color: #ffa940;
  }

  // 其他排名淡色
  .ranking-item:nth-child(n + 4) & {
    color: #999;
    font-weight: 500;
  }
}

.ranking-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.ranking-title {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  line-height: 1.3;
}

.ranking-desc {
  font-size: 12px;
  color: #999;
  line-height: 1.2;
}

.ranking-trend {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

// 搜索结果
.results-section {
  background-color: #fff;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.sort-filter-bar {
  display: flex;
  background: #fff;
  padding: 8px 20px;
  border-bottom: 1px solid #f0f0f0;
  justify-content: space-between;
  align-items: center;
  min-height: 36px;
}

.sort-item {
  position: relative;
  padding: 6px 0;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-right: 24px;

  &:last-of-type {
    margin-right: 0;
  }

  &.active {
    .sort-text {
      color: #f8293a;
      font-weight: 600;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 20px;
      height: 2px;
      background: #f8293a;
      border-radius: 1px;
    }
  }

  &:active {
    transform: scale(0.95);
  }
}

.sort-text {
  font-size: 14px;
  color: #666;
  transition: all 0.2s;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-button {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;

  .iconfont-sys {
    font-size: 16px;
    color: #666;
  }

  &:active {
    transform: scale(0.95);
  }
}

.view-toggle-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;

  .iconfont-sys {
    font-size: 16px;
    color: #666;
  }

  &:active {
    transform: scale(0.95);
  }
}

.results-scroll-container {
  flex: 1;
  overflow: auto;
}

.results-content {
  padding: 8px;

  // 商户列表内容使用更小的padding
  &.merchant-content {
    padding: 4px 8px;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-text {
  font-size: 16px;
  color: #999;
  margin: 20px 0 8px;
}

.empty-tip {
  font-size: 14px;
  color: #ccc;
}
</style>
