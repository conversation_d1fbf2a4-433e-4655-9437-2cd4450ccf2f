<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '购物车',
  },
  access: {
    requireAuth: false,
  },
}
</route>

<template>
  <view class="cart-page" @click="closeOutside">
    <!-- 自定义导航栏 -->
    <Navbar title="购物车" :show-back="false" :fixed="true" :placeholder="true" />

    <!-- 购物车内容 -->
    <view class="cart-content">
      <!-- 空购物车状态 -->
      <view v-if="cartItems.length === 0" class="empty-cart">
        <wd-icon name="cart" size="80px" color="#ccc" />
        <text class="empty-text">购物车是空的</text>
        <text class="empty-tip">快去挑选心仪的商品吧~</text>
        <wd-button
          type="primary"
          size="medium"
          @click="goShopping"
          custom-style="background-color: #ff4142; border-color: #ff4142; border-radius: 8px;"
        >
          去购物
        </wd-button>
      </view>

      <!-- 购物车列表 -->
      <view v-else class="cart-list">
        <!-- 商户分组 -->
        <view
          v-for="(merchant, merchantIndex) in groupedCartItems"
          :key="merchant.merchantId"
          class="merchant-group"
        >
          <!-- 商户头部 -->
          <view class="merchant-header">
            <wd-checkbox
              v-model="merchant.checked"
              @change="handleMerchantCheck(merchantIndex)"
              :checked-color="checkedColor"
            >
              <view class="merchant-info">
                <text class="merchant-name">{{ merchant.merchantName }}</text>
              </view>
            </wd-checkbox>
          </view>

          <!-- 商品列表 -->
          <view v-for="(item, itemIndex) in merchant.items" :key="item.id" class="cart-item">
            <wd-swipe-action>
              <view class="cart-item-content">
                <wd-checkbox
                  v-model="item.checked"
                  @change="handleItemCheck(merchantIndex, itemIndex)"
                  :checked-color="checkedColor"
                />

                <view class="item-content">
                  <image class="item-image" :src="item.image" mode="aspectFill" />

                  <view class="item-info">
                    <text class="item-name">{{ item.name }}</text>
                    <text class="item-specs">{{ item.specs }}</text>

                    <view class="item-bottom">
                      <text class="item-price">¥{{ item.price }}</text>

                      <!-- 数量选择器 -->
                      <wd-input-number
                        v-model="item.quantity"
                        :min="1"
                        :max="item.stock"
                        @change="handleQuantityChange(merchantIndex, itemIndex)"
                      />
                    </view>
                  </view>
                </view>
              </view>


              <template #right>
                <view class="swipe-action-wrapper">
                  <view class="swipe-action-btn" @click="handleDelete(merchantIndex, itemIndex)">
                    <text class="swipe-action-text">删除</text>
                  </view>
                </view>
              </template>
            </wd-swipe-action>
          </view>
        </view>
        <text>仅供演示！</text>

      </view>
    </view>

    <!-- 底部结算栏 -->
    <view v-if="cartItems.length > 0" class="cart-footer">
      <view class="footer-left">
        <wd-checkbox v-model="allChecked" @change="handleAllCheck" :checked-color="checkedColor">
          <text class="select-all-text">全选</text>
        </wd-checkbox>

        <view class="total-info">
          <text class="total-label">合计：</text>
          <text class="total-price">¥{{ totalPrice }}</text>
        </view>
      </view>

      <wd-button
        type="primary"
        size="medium"
        :disabled="selectedCount === 0"
        @click="handleCheckout"
        custom-style="border-radius: 8px; background-color: #ff4142; border-color: #ff4142; height: 36px; line-height: 36px;"
      >
        结算({{ selectedCount }})
      </wd-button>
    </view>

    <!-- TabBar -->
    <TabBar current-page="cart" />
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useGlobalSafeArea } from '../../hooks/useSafeArea'
// @ts-ignore
import { useQueue } from 'wot-design-uni/components/composables/useQueue'

// 使用全局安全区域 hook
const { safeAreaInsetsTop } = useGlobalSafeArea()

// 使用队列管理
const { closeOutside } = useQueue()

// 定义选中颜色
const checkedColor = ref('#ff4142')

// 计算导航栏总高度
const navbarTotalHeight = computed(() => {
  // #ifdef MP-WEIXIN
  // 微信小程序需要更多高度
  return safeAreaInsetsTop.value + 44 + 20 // 额外20px为了适配胶囊按钮
  // #endif
  // #ifndef MP-WEIXIN
  return safeAreaInsetsTop.value + 44
  // #endif
})

// 购物车商品接口
interface CartItem {
  id: string
  name: string
  image: string
  price: number
  quantity: number
  stock: number
  specs: string
  merchantId: string
  merchantName: string
  checked: boolean
}

// 商户分组接口
interface MerchantGroup {
  merchantId: string
  merchantName: string
  checked: boolean
  items: CartItem[]
}

// 购物车数据
const cartItems = ref<CartItem[]>([])

// 按商户分组的购物车数据
const groupedCartItems = computed(() => {
  const groups: MerchantGroup[] = []

  cartItems.value.forEach((item) => {
    let group = groups.find((g) => g.merchantId === item.merchantId)

    if (!group) {
      group = {
        merchantId: item.merchantId,
        merchantName: item.merchantName,
        checked: false,
        items: [],
      }
      groups.push(group)
    }

    group.items.push(item)
    // 更新商户选中状态
    group.checked = group.items.every((item) => item.checked)
  })

  return groups
})

// 全选状态
const allChecked = computed({
  get: () => cartItems.value.length > 0 && cartItems.value.every((item) => item.checked),
  set: (value) => {
    cartItems.value.forEach((item) => {
      item.checked = value
    })
  },
})

// 选中数量
const selectedCount = computed(() => {
  return cartItems.value.filter((item) => item.checked).length
})

// 总价
const totalPrice = computed(() => {
  return cartItems.value
    .filter((item) => item.checked)
    .reduce((total, item) => total + item.price * item.quantity, 0)
    .toFixed(2)
})

// 处理商户选择
const handleMerchantCheck = (merchantIndex: number) => {
  const merchant = groupedCartItems.value[merchantIndex]
  const checked = merchant.checked

  // 更新该商户下所有商品的选中状态
  merchant.items.forEach((item) => {
    item.checked = checked
  })
}

// 处理单个商品选择
const handleItemCheck = (merchantIndex: number, itemIndex: number) => {
  // 选中状态变化会自动触发 groupedCartItems 的重新计算
  // 商户的选中状态会自动更新
}

// 处理全选
const handleAllCheck = () => {
  // allChecked 的 setter 会处理
}

// 处理数量变化
const handleQuantityChange = (merchantIndex: number, itemIndex: number) => {
  const item = groupedCartItems.value[merchantIndex].items[itemIndex]
  console.log('数量变化:', item.name, item.quantity)

  // TODO: 调用接口更新购物车数量
  updateCartQuantity(item.id, item.quantity)
}

// 删除商品
const handleDelete = (merchantIndex: number, itemIndex: number) => {
  const item = groupedCartItems.value[merchantIndex].items[itemIndex]

  uni.showModal({
    title: '提示',
    content: '确定要删除这个商品吗？',
    success: (res) => {
      if (res.confirm) {
        // 从购物车中移除
        const index = cartItems.value.findIndex((i) => i.id === item.id)
        if (index > -1) {
          cartItems.value.splice(index, 1)
        }

        // TODO: 调用接口删除购物车商品
        deleteCartItem(item.id)

        uni.showToast({
          title: '删除成功',
          icon: 'success',
        })
      }
    },
  })
}

// 去购物
const goShopping = () => {
  uni.switchTab({
    url: '/pages/user/index',
  })
}

// 结算
const handleCheckout = () => {
  const selectedItems = cartItems.value.filter((item) => item.checked)

  if (selectedItems.length === 0) {
    uni.showToast({
      title: '请选择要结算的商品',
      icon: 'none',
    })
    return
  }

  // TODO: 跳转到确认订单页面
  console.log('结算商品:', selectedItems)
  uni.showToast({
    title: '功能开发中',
    icon: 'none',
  })
}

// 加载购物车数据
const loadCartData = async () => {
  try {
    // TODO: 调用接口获取购物车数据
    // 模拟数据
    cartItems.value = [
      {
        id: '1',
        name: '新鲜水果礼盒装 精选当季时令水果',
        image: 'https://img.yzcdn.cn/vant/apple-1.jpg',
        price: 98.0,
        quantity: 2,
        stock: 100,
        specs: '规格：5斤装',
        merchantId: 'm1',
        merchantName: '鲜果优选',
        checked: true,
      },
      {
        id: '2',
        name: '进口车厘子 智利直采 J级大果',
        image: 'https://img.yzcdn.cn/vant/apple-2.jpg',
        price: 168.0,
        quantity: 1,
        stock: 50,
        specs: '规格：2斤装',
        merchantId: 'm1',
        merchantName: '鲜果优选',
        checked: true,
      },
      {
        id: '3',
        name: '有机蔬菜套餐 新鲜直达',
        image: 'https://img.yzcdn.cn/vant/apple-3.jpg',
        price: 58.0,
        quantity: 3,
        stock: 200,
        specs: '规格：时令搭配',
        merchantId: 'm2',
        merchantName: '绿色农场',
        checked: false,
      },
    ]
  } catch (error) {
    console.error('加载购物车失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  }
}

// 更新购物车数量（API调用）
const updateCartQuantity = async (itemId: string, quantity: number) => {
  try {
    // TODO: 调用接口
    console.log('更新数量:', itemId, quantity)
  } catch (error) {
    console.error('更新数量失败:', error)
  }
}

// 删除购物车商品（API调用）
const deleteCartItem = async (itemId: string) => {
  try {
    // TODO: 调用接口
    console.log('删除商品:', itemId)
  } catch (error) {
    console.error('删除商品失败:', error)
  }
}

// 页面加载
onMounted(() => {
  loadCartData()
})
</script>

<style lang="scss" scoped>
.cart-page {
  min-height: 100vh;
  background-color: #fafafa;
  display: flex;
  flex-direction: column;
  position: relative;

  // 设置 Wot Design 的主题色变量
  --wd-color-theme: #ff4142;
  --wd-checkbox-checked-color: #ff4142;
}

.cart-content {
  flex: 1;
  padding-bottom: calc(50px + env(safe-area-inset-bottom) + 60px); // 50px TabBar + 60px 结算栏高度
  overflow-y: auto;
}

// 空购物车
.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 20px;

  .empty-text {
    margin-top: 20px;
    font-size: 16px;
    color: #333;
  }

  .empty-tip {
    margin-top: 8px;
    margin-bottom: 30px;
    font-size: 14px;
    color: #999;
  }
}

// 购物车列表
.cart-list {
  padding: 12px;
}

// 商户分组
.merchant-group {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
}

.merchant-header {
  padding: 12px 16px;

  .merchant-info {
    display: flex;
    align-items: center;

    .merchant-name {
      font-size: 15px;
      font-weight: 500;
      color: #333;
    }
  }
}

// 购物车商品
.cart-item {
  margin-bottom: 0;

  &:last-child {
    margin-bottom: 0;
  }

  .cart-item-content {
    display: flex;
    align-items: center;
    padding: 16px;
    background-color: #fff;
  }

  :deep(.wd-checkbox) {
    margin-right: 12px;
  }

  .item-content {
    flex: 1;
    display: flex;
    gap: 12px;
  }

  .item-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    flex-shrink: 0;
    background-color: #f5f5f5;
  }

  .item-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .item-name {
    font-size: 14px;
    color: #333;
    line-height: 20px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }

  .item-specs {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
  }

  .item-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .item-price {
    font-size: 16px;
    color: #ff4142;
    font-weight: 600;
  }
}

// 滑动操作样式
.swipe-action-wrapper {
  height: 100%;
  display: flex;
  align-items: center;
}

.swipe-action-btn {
  background-color: #ff4142;
  height: 100%;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  .swipe-action-text {
    color: #fff;
    font-size: 14px;
  }
}

// 底部结算栏
.cart-footer {
  position: fixed;
  bottom: calc(50px + env(safe-area-inset-bottom)); // TabBar 的高度
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 100; // 确保在内容之上，但在 TabBar 之下
  height: 60px; // 固定高度

  .footer-left {
    display: flex;
    align-items: center;
    gap: 20px;
    height: 100%;

    .select-all-text {
      font-size: 14px;
      color: #333;
      line-height: 1;
    }
  }

  .total-info {
    display: flex;
    align-items: center; // 改为 center 对齐
    gap: 4px; // 添加间距

    .total-label {
      font-size: 14px;
      color: #666;
      line-height: 1;
    }

    .total-price {
      font-size: 24px;
      color: #ff4142;
      font-weight: 600;
      line-height: 1;
    }
  }

  // 底部结算栏复选框样式
  :deep(.wd-checkbox) {
    display: flex;
    align-items: center;
    height: 36px;
  }
}

// 数量选择器样式调整
:deep(.wd-input-number) {
  // 修改整体大小
  transform: scale(0.9);

  // 修改按钮样式
  .wd-input-number__increase,
  .wd-input-number__decrease {
    background-color: transparent !important;
    border: none !important;
    color: #666 !important;
    width: 28px;
    height: 28px;

    // 图标样式
    &::before {
      font-size: 20px !important;
      font-weight: bold;
    }

    &:active {
      background-color: #f5f5f5 !important;
      color: #ff4142 !important;
      border-radius: 50%;
    }

    // 禁用状态
    &.is-disabled {
      color: #ccc !important;
      opacity: 0.5;
    }
  }

  // 输入框样式
  .wd-input-number__input {
    background-color: #f5f5f5 !important;
    border: none !important;
    border-radius: 4px;
    margin: 0 4px;
    min-width: 50px;
    height: 28px;
    font-size: 14px;
    color: #333;
    text-align: center;

    // 聚焦状态
    &:focus {
      background-color: #fff !important;
      box-shadow: 0 0 0 1px #ff4142 !important;
    }
  }
}
</style>
