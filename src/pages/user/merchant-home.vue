<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '商户主页',
  },
  access: {
    requireAuth: false,
  },
}
</route>

<template>
  <view class="merchant-home-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: safeAreaInsetsTop + 'px' }">
      <view class="navbar-content" :style="{ paddingRight: rightSafeArea + 'px' }">
        <view class="navbar-left" @click="goBack">
          <view class="back-icon">
            <wd-icon name="arrow-left" size="20px" color="#333" />
          </view>
        </view>
        <view class="navbar-center" :style="{ maxWidth: `calc(100% - 60px - ${rightSafeArea}px)` }">
          <view class="merchant-info">
            <image class="merchant-logo" :src="merchantInfo.logo" mode="aspectFill" />
            <text class="merchant-name">{{ merchantInfo.name }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <scroll-view
      class="main-content"
      scroll-y
      :style="{ marginTop: safeAreaInsetsTop + 44 + 'px' }"
      @scrolltolower="handleReachBottom"
      :lower-threshold="200"
    >
      <!-- 商户信息卡片 -->
      <view class="merchant-card">
        <view class="merchant-header">
          <image class="merchant-avatar" :src="merchantInfo.logo" mode="aspectFill" />
          <view class="merchant-detail">
            <text class="merchant-title">{{ merchantInfo.name }}</text>
            <text class="merchant-desc">{{ merchantInfo.description }}</text>
            <view class="merchant-location">
              <wd-icon name="location" size="12px" color="#999" />
              <text class="location-text">{{ merchantInfo.address }}</text>
              <text class="distance-text">{{ merchantInfo.distance }}</text>
            </view>

            <!-- 联系方式和资质信息行 -->
            <view class="merchant-contact-row">
              <!-- 联系电话 -->
              <view class="merchant-phone" @click="callPhone">
                <wd-icon name="phone" size="11px" color="#999" />
                <text class="phone-text">{{ merchantInfo.phone }}</text>
              </view>

              <text class="separator">|</text>

              <!-- 资质信息 -->
              <view class="merchant-qualifications" @click="goToQualification">
                <text class="iconfont-sys iconsys-dianpu qualification-icon"></text>
                <text class="qualification-text">商户资质</text>
                <wd-icon name="arrow-right" size="12px" color="#999" />
              </view>
            </view>

            <view class="merchant-stats">
              <text class="stat-item">粉丝 {{ merchantInfo.followers }}</text>
              <text class="stat-item">商品 {{ merchantInfo.goodsCount }}</text>
            </view>
          </view>
          <view class="button-group">
            <view class="follow-btn" :class="{ followed: isFollowed }" @click="toggleFollow">
              <text class="follow-text">{{ isFollowed ? '已关注' : '关注' }}</text>
            </view>
            <view class="share-btn" @click="shareStore">
              <wd-icon name="share" size="16px" color="#666" />
            </view>
          </view>
        </view>
      </view>

      <!-- 分类筛选栏 -->
      <view class="filter-bar">
        <wd-tabs
          v-model="activeTab"
          @change="handleTabChange"
          line-width="30px"
          line-height="3px"
          auto-line-width
          swipeable
          animated
        >
          <wd-tab v-for="tab in tabs" :key="tab.value" :name="tab.value" :title="tab.label">
            <!-- 商品列表容器 -->
            <view class="goods-scroll-container">
              <view class="goods-content">
                <!-- 网格模式 -->
                <WaterfallGoods
                  v-if="viewMode === 'grid'"
                  :goods-list="goodsList"
                  :loading="loading"
                  :has-more="hasMore"
                  :column-count="2"
                  :show-price="true"
                  loading-text="加载中..."
                  no-more-text="没有更多商品了"
                  @goods-click="handleGoodsClick"
                  @like-click="handleLikeClick"
                />

                <!-- 列表模式 -->
                <GoodsList
                  v-else-if="viewMode === 'list'"
                  :goods-list="listGoods"
                  :loading="loading"
                  :has-more="hasMore"
                  :show-price="true"
                  loading-text="加载中..."
                  no-more-text="没有更多商品了"
                  @goods-click="handleListGoodsClick"
                  @like-click="handleListLikeClick"
                />
              </view>
            </view>
          </wd-tab>
        </wd-tabs>
      </view>
    </scroll-view>

    <!-- 底部导航栏 -->
    <MerchantBottomNav :current-nav="'home'" :merchant-id="merchantId" />
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useGlobalSafeArea } from '@/hooks/useSafeArea'
import WaterfallGoods from '@/components/waterfall-goods/index.vue'
import GoodsList from '@/components/goods-list/index.vue'
import MerchantBottomNav from '@/components/merchant-bottom-nav/index.vue'
import type { GoodsItem } from '@/components/waterfall-goods/types'

defineOptions({
  name: 'MerchantHome',
})

// 使用全局安全区域 hook
const { safeAreaInsetsTop, safeAreaInsetsBottom, rightSafeArea } = useGlobalSafeArea()

// 页面参数
const merchantId = ref('')
const merchantName = ref('')

// 页面加载时获取参数
onLoad((options: any) => {
  console.log('商户主页参数:', options)
  if (options.id) {
    merchantId.value = options.id
    console.log('设置商户ID:', options.id)
  }
  if (options.name) {
    merchantName.value = decodeURIComponent(options.name)
    console.log('设置商户名称:', merchantName.value)
    // 更新商户信息中的名称
    merchantInfo.value.name = merchantName.value
    merchantInfo.value.id = merchantId.value
  }
})

// 商户信息
const merchantInfo = ref({
  id: '1',
  name: '阳光果园旗舰店',
  logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop&crop=center',
  description: '专营新鲜水果，品质保证，新鲜直达',
  address: '北京市朝阳区建国门外大街1号',
  distance: '1.2km',
  followers: 2580,
  goodsCount: 156,
  phone: '************',
  qualifications: ['营业执照', '食品经营许可证', '质量认证'],
})

// 关注状态
const isFollowed = ref(false)

// 当前标签页
const activeTab = ref('recommend')

// 标签页配置
const tabs = ref([
  { label: '推荐', value: 'recommend' },
  { label: '新品', value: 'new' },
])

// 显示模式
const viewMode = ref<'list' | 'grid'>('grid')

// 商品数据
const goodsList = ref<GoodsItem[]>([])
const loading = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)

// 列表格式的商品数据
const listGoods = computed(() => {
  return goodsList.value.map((item) => ({
    id: item.id,
    title: item.title,
    price: item.price,
    originalPrice: item.originalPrice,
    sales: item.sales,
    isLiked: item.isLiked,
    image: item.image || '',
  }))
})

// 生成商品数据
const generateGoods = (category: string, startIndex: number, count: number): GoodsItem[] => {
  const titles = [
    '新疆阿克苏冰糖心苹果 5斤装',
    '山东烟台红富士苹果 10斤',
    '陕西洛川苹果 脆甜可口',
    '进口蛇果 新鲜甜脆',
    '新疆香梨 5斤装 清甜多汁',
    '四川丑橘 不知火 酸甜可口',
    '海南金煌芒果 香甜软糯',
    '云南石榴 籽粒饱满',
  ]

  const colors = [
    '#FF6B6B',
    '#4ECDC4',
    '#45B7D1',
    '#96CEB4',
    '#FFEAA7',
    '#DDA0DD',
    '#98D8C8',
    '#F7DC6F',
  ]

  return Array.from({ length: count }, (_, i) => {
    const index = startIndex + i
    const randomPrice = Math.floor(Math.random() * 50) + 10
    const randomOriginalPrice =
      Math.random() > 0.3 ? randomPrice + Math.floor(Math.random() * 20) + 5 : null
    const randomSales = Math.floor(Math.random() * 2000) + 50
    const randomHeight = Math.floor(Math.random() * 100) + 150

    return {
      id: `${category}_${index}`,
      title: titles[index % titles.length],
      price: randomPrice,
      originalPrice: randomOriginalPrice,
      color: colors[index % colors.length],
      sales: randomSales,
      isLiked: Math.random() > 0.7,
      imageHeight: randomHeight,
    }
  })
}

// 加载商品数据
const loadGoods = async (category: string, page: number = 1, append: boolean = false) => {
  if (loading.value) return

  loading.value = true
  try {
    // 模拟网络请求延迟
    await new Promise((resolve) => setTimeout(resolve, 500))

    const newGoods = generateGoods(category, (page - 1) * 10, 10)

    if (append) {
      goodsList.value.push(...newGoods)
    } else {
      goodsList.value = newGoods
      currentPage.value = 1
    }

    currentPage.value = page
    // 模拟数据有限，加载到50个商品后停止
    hasMore.value = goodsList.value.length < 50
  } catch (error) {
    console.error('加载商品数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 标签页切换
const handleTabChange = ({ name }: { name: string }) => {
  console.log('标签页切换:', name)
  loadGoods(name)
}

// 触底加载更多
const handleReachBottom = () => {
  if (!loading.value && hasMore.value) {
    loadGoods(activeTab.value, currentPage.value + 1, true)
  }
}

// 商品点击处理
const handleGoodsClick = (item: GoodsItem) => {
  uni.navigateTo({
    url: `/pages/user/goods-detail?id=${item.id}&title=${encodeURIComponent(item.title)}`,
  })
}

// 列表商品点击处理
const handleListGoodsClick = (item: any) => {
  uni.navigateTo({
    url: `/pages/user/goods-detail?id=${item.id}&title=${encodeURIComponent(item.title)}`,
  })
}

// 点赞处理
const handleLikeClick = (item: GoodsItem) => {
  const index = goodsList.value.findIndex((goods) => goods.id === item.id)
  if (index !== -1) {
    goodsList.value[index].isLiked = !goodsList.value[index].isLiked
    uni.showToast({
      title: goodsList.value[index].isLiked ? '已收藏' : '已取消收藏',
      icon: 'success',
      duration: 1000,
    })
  }
}

// 列表点赞处理
const handleListLikeClick = (item: any) => {
  const index = goodsList.value.findIndex((goods) => goods.id === item.id)
  if (index !== -1) {
    goodsList.value[index].isLiked = !goodsList.value[index].isLiked
    uni.showToast({
      title: goodsList.value[index].isLiked ? '已收藏' : '已取消收藏',
      icon: 'success',
      duration: 1000,
    })
  }
}

// 返回上一页
const goBack = () => {
  const pages = getCurrentPages()
  if (pages.length > 1) {
    uni.navigateBack()
  } else {
    uni.reLaunch({
      url: '/pages/user/index',
    })
  }
}

// 切换关注状态
const toggleFollow = () => {
  isFollowed.value = !isFollowed.value
  uni.showToast({
    title: isFollowed.value ? '已关注店铺' : '已取消关注',
    icon: 'success',
  })
}

// 分享店铺
const shareStore = () => {
  uni.showToast({
    title: '分享店铺',
    icon: 'none',
  })
}

// 拨打电话
const callPhone = () => {
  if (merchantInfo.value.phone) {
    uni.makePhoneCall({
      phoneNumber: merchantInfo.value.phone,
      fail: () => {
        uni.showToast({
          title: '拨打电话失败',
          icon: 'none',
        })
      },
    })
  }
}

// 跳转到资质页面
const goToQualification = () => {
  uni.navigateTo({
    url: `/pages/user/merchant-qualification?merchantId=${merchantId.value}&merchantName=${encodeURIComponent(merchantInfo.value.name)}`,
  })
}

// 页面初始化
onMounted(() => {
  loadGoods('recommend')
})
</script>

<style lang="scss" scoped>
@import '@/style/iconfont.scss';

.merchant-home-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 16px;
  position: relative;
}

.navbar-left {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 16px;
  top: 0;
  z-index: 1001;

  /* 通过CSS放大返回按钮图标 */
  :deep(.wd-icon) {
    font-size: 20px !important;
    width: 28px !important;
    height: 28px !important;
    transform: scale(1.1);
  }
}

.back-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  /* 移除背景色 */
  background: transparent;
  transition: all 0.2s;

  &:active {
    /* 点击时也不显示背景，只有缩放效果 */
    background: transparent;
    transform: scale(0.95);
  }
}

.navbar-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  justify-content: center;
  align-items: center;
  height: 44px;
  /* 最大宽度现在通过模板动态设置，确保不与左侧按钮和右侧胶囊冲突 */
}

.merchant-info {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.merchant-logo {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  flex-shrink: 0;
}

.merchant-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 商户信息卡片 */
.merchant-card {
  background: #fff;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.merchant-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  width: 100%;
}

.merchant-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  flex-shrink: 0;
}

.merchant-detail {
  flex: 1;
  min-width: 0;
}

.merchant-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.merchant-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.merchant-location {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
}

.location-text {
  font-size: 12px;
  color: #999;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.distance-text {
  font-size: 12px;
  color: #ff9500;
  font-weight: 400;

  &::before {
    content: '·';
    color: #ddd;
  }
}

// 联系方式和资质信息行
.merchant-contact-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;

  .separator {
    color: #ddd;
    font-size: 12px;
    margin: 0 4px;
  }
}

.merchant-phone {
  display: flex;
  align-items: center;
  transition: opacity 0.2s;
  gap: 4px;

  &:active {
    opacity: 0.7;
  }

  // 精确控制电话图标大小
  :deep(.wd-icon) {
    font-size: 11px !important;
  }
}

.phone-text {
  font-size: 13px;
  color: #1890ff;
}

.merchant-qualifications {
  display: flex;
  align-items: center;
  gap: 4px;
  transition: opacity 0.2s;

  &:active {
    opacity: 0.7;
  }

  .qualification-icon {
    font-size: 14px;
    color: #52c41a;
  }

  // 控制右箭头图标大小
  :deep(.wd-icon) {
    font-size: 12px !important;
  }
}

.qualification-content {
  display: flex;
  align-items: center;
  gap: 4px;
}

.qualification-text {
  font-size: 13px;
  color: #52c41a;
  font-weight: 500;
}

.merchant-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  font-size: 12px;
  color: #999;
}

/* 按钮组容器 */
.button-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 6px;
  flex-shrink: 0;
  margin-top: 0;
  width: 40px;
}

.follow-btn {
  padding: 6px 12px;
  border: 1px solid #ff9500;
  border-radius: 16px;
  background: transparent;
  transition: all 0.2s;
  white-space: nowrap;
  min-width: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  &.followed {
    background: #ff9500;

    .follow-text {
      color: #fff;
    }
  }

  &:active {
    transform: scale(0.95);
  }
}

.follow-text {
  font-size: 12px;
  color: #ff9500;
  font-weight: 500;
  line-height: 1;
}

.share-btn {
  padding: 6px;
  background: transparent;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  align-self: center;

  &:active {
    transform: scale(0.9);
  }
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  background-color: #f5f5f5;

  ::-webkit-scrollbar {
    display: none;
  }
}

.filter-bar {
  background-color: #fff;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  z-index: 10;
}

.goods-scroll-container {
  height: auto;
  overflow: visible;
}

.goods-content {
  padding: 8px;
  min-height: calc(100vh - 300px);
}

/* 隐藏 wot-design-uni tabs 横向滚动条 */
:deep(.wd-tabs__nav-wrap) {
  ::-webkit-scrollbar {
    display: none;
  }
}
</style>
