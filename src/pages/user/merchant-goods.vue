<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '全部商品',
  },
  access: {
    requireAuth: false,
  },
}
</route>

<template>
  <view class="merchant-goods-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: safeAreaInsetsTop + 'px' }">
      <view class="navbar-content" :style="{ paddingRight: rightSafeArea + 'px' }">
        <view class="navbar-center">
          <wd-search
            v-model="searchKeyword"
            :placeholder="searchPlaceholder"
            hide-cancel
            @search="handleSearch"
            @input="handleSearchInput"
            @clear="handleSearchClear"
            custom-class="search-component"
          />
        </view>
      </view>
    </view>

    <!-- 排序筛选栏 -->
    <view class="sort-filter-bar" :style="{ marginTop: safeAreaInsetsTop + 44 + 'px' }">
      <!-- 排序选项 -->
      <view
        v-for="sort in sortOptions"
        :key="sort.value"
        class="sort-item"
        :class="{ active: activeSort === sort.value }"
        @click="handleSortChange(sort.value)"
      >
        <text class="sort-text">{{ sort.label }}</text>
        <wd-icon
          v-if="sort.value === 'price'"
          :name="priceOrder === 'asc' ? 'arrow-up' : 'arrow-down'"
          size="12px"
          :color="activeSort === 'price' ? '#1890ff' : '#999'"
        />
      </view>

      <!-- 右侧按钮组 -->
      <view class="right-actions">
        <!-- 筛选按钮 -->
        <view class="filter-button" @click="openFilterPopup">
          <text class="iconfont-sys iconsys-shaixuan"></text>
        </view>

        <!-- 显示模式切换 -->
        <view class="view-toggle-btn" @click="toggleViewMode">
          <text
            class="iconfont-sys"
            :class="viewMode === 'grid' ? 'iconsys-liebiao' : 'iconsys-more-grid-big'"
          ></text>
        </view>
      </view>
    </view>

    <!-- 商品列表容器 -->
    <scroll-view
      class="goods-scroll-container"
      scroll-y
      @scrolltolower="handleReachBottom"
      :lower-threshold="200"
    >
      <view class="goods-content">
        <!-- 网格模式 -->
        <WaterfallGoods
          v-if="viewMode === 'grid'"
          :goods-list="goodsList"
          :loading="loading"
          :has-more="hasMore"
          :column-count="2"
          :show-price="true"
          loading-text="加载中..."
          no-more-text="没有更多商品了"
          @goods-click="handleGoodsClick"
          @like-click="handleLikeClick"
        />

        <!-- 列表模式 -->
        <GoodsList
          v-else-if="viewMode === 'list'"
          :goods-list="listGoods"
          :loading="loading"
          :has-more="hasMore"
          :show-price="true"
          loading-text="加载中..."
          no-more-text="没有更多商品了"
          @goods-click="handleListGoodsClick"
          @like-click="handleListLikeClick"
        />
      </view>
    </scroll-view>

    <!-- 空状态 -->
    <view v-if="goodsList.length === 0 && !loading" class="empty-state">
      <wd-icon name="search" size="60px" color="#ccc" />
      <text class="empty-text">没有找到相关商品</text>
      <text class="empty-tip">试试其他关键词吧</text>
    </view>

    <!-- 底部导航栏 -->
    <MerchantBottomNav :current-nav="'goods'" :merchant-id="merchantId" />

    <!-- 筛选弹窗 -->
    <FilterPopup
      :visible="showFilterPopup"
      :view-mode="viewMode"
      :filter-options="filterOptions"
      :current-filters="currentFilters"
      :show-view-mode-switch="false"
      @update:visible="showFilterPopup = $event"
      @update:view-mode="viewMode = $event"
      @update:current-filters="currentFilters = $event"
      @apply="applyFilters"
      @reset="resetFilters"
    />
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useGlobalSafeArea } from '@/hooks/useSafeArea'
import WaterfallGoods from '@/components/waterfall-goods/index.vue'
import GoodsList from '@/components/goods-list/index.vue'
import MerchantBottomNav from '@/components/merchant-bottom-nav/index.vue'
import FilterPopup from '@/components/filter-popup/index.vue'
import type { GoodsItem } from '@/components/waterfall-goods/types'

defineOptions({
  name: 'MerchantGoods',
})

// 使用全局安全区域 hook
const { safeAreaInsetsTop, safeAreaInsetsBottom, rightSafeArea } = useGlobalSafeArea()

// 页面参数
const merchantId = ref('')
const categoryName = ref('')

// 页面加载时获取参数
onLoad((options: any) => {
  console.log('全部商品页面参数:', options)
  if (options.id) {
    merchantId.value = options.id
  }

  // 获取分类名称参数
  if (options.category) {
    categoryName.value = decodeURIComponent(options.category)
  }

  // 如果有搜索参数，自动设置搜索关键词并搜索
  if (options.search) {
    searchKeyword.value = decodeURIComponent(options.search)
    // 延迟执行搜索，确保页面初始化完成
    setTimeout(() => {
      handleSearch()
    }, 100)
  }
})

// 搜索相关
const searchKeyword = ref('')

// 搜索框占位符文本
const searchPlaceholder = computed(() => {
  return categoryName.value ? `分类：${categoryName.value}` : '搜索店内商品'
})

// 排序相关
const activeSort = ref('comprehensive')
const priceOrder = ref('desc')

// 排序选项 - 动态计算，当有分类时替换"综合"
const sortOptions = computed(() => {
  const comprehensiveLabel = categoryName.value || '综合'
  return [
    { label: comprehensiveLabel, value: 'comprehensive' },
    { label: '销量', value: 'sales' },
    { label: '新上架', value: 'newest' },
    { label: '价格', value: 'price' },
  ]
})

// 显示模式
const viewMode = ref<'list' | 'grid'>('grid')

// 筛选相关状态
const showFilterPopup = ref(false)

// 筛选选项配置
const filterOptions = {
  distance: [
    { label: '5km以内', value: '5km' },
    { label: '10km以内', value: '10km' },
    { label: '20km以内', value: '20km' },
    { label: '50km以内', value: '50km' },
    { label: '不限距离', value: 'unlimited' },
  ],
  freshness: [
    { label: '不限', value: 'all' },
    { label: '当日新鲜', value: 'today' },
    { label: '3日内', value: 'three_days' },
    { label: '一周内', value: 'week' },
  ],
}

// 当前筛选条件
const currentFilters = ref({
  priceMin: '',
  priceMax: '',
  distance: '5km',
  freshness: 'all',
})

// 商品数据
const goodsList = ref<GoodsItem[]>([])
const loading = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)

// 列表格式的商品数据
const listGoods = computed(() => {
  return goodsList.value.map((item) => ({
    id: item.id,
    title: item.title,
    price: item.price,
    originalPrice: item.originalPrice,
    sales: item.sales,
    isLiked: item.isLiked,
    image: item.image || '',
    color: item.color,
  }))
})

// 生成商品数据
const generateGoods = (keyword: string, startIndex: number, count: number): GoodsItem[] => {
  const titles = [
    '新疆阿克苏冰糖心苹果 5斤装',
    '山东烟台红富士苹果 10斤',
    '陕西洛川苹果 脆甜可口',
    '进口蛇果 新鲜甜脆',
    '新疆香梨 5斤装 清甜多汁',
    '四川丑橘 不知火 酸甜可口',
    '海南金煌芒果 香甜软糯',
    '云南石榴 籽粒饱满',
    '广西沙糖桔 甜蜜多汁',
    '福建蜜柚 清香甘甜',
  ]

  const colors = [
    '#FF6B6B',
    '#4ECDC4',
    '#45B7D1',
    '#96CEB4',
    '#FFEAA7',
    '#DDA0DD',
    '#98D8C8',
    '#F7DC6F',
  ]

  // 如果有搜索关键词，过滤标题
  const filteredTitles = keyword
    ? titles.filter((title) => title.toLowerCase().includes(keyword.toLowerCase()))
    : titles

  if (filteredTitles.length === 0) {
    return []
  }

  return Array.from({ length: count }, (_, i) => {
    const index = startIndex + i
    const randomPrice = Math.floor(Math.random() * 50) + 10
    const randomOriginalPrice =
      Math.random() > 0.3 ? randomPrice + Math.floor(Math.random() * 20) + 5 : null
    const randomSales = Math.floor(Math.random() * 2000) + 50
    const randomHeight = Math.floor(Math.random() * 100) + 150

    return {
      id: `goods_${index}`,
      title: filteredTitles[index % filteredTitles.length],
      price: randomPrice,
      originalPrice: randomOriginalPrice,
      color: colors[index % colors.length],
      sales: randomSales,
      isLiked: Math.random() > 0.7,
      imageHeight: randomHeight,
      // 暂时不使用真实图片，全部使用纯色背景进行模拟
      image: '',
    }
  })
}

// 加载商品数据
const loadGoods = async (keyword: string = '', page: number = 1, append: boolean = false) => {
  if (loading.value) return

  loading.value = true
  try {
    // 模拟网络请求延迟
    await new Promise((resolve) => setTimeout(resolve, 500))

    const newGoods = generateGoods(keyword, (page - 1) * 10, 10)

    if (append) {
      goodsList.value.push(...newGoods)
    } else {
      goodsList.value = newGoods
      currentPage.value = 1
    }

    currentPage.value = page
    // 模拟数据有限，加载到50个商品后停止
    hasMore.value = goodsList.value.length < 50
  } catch (error) {
    console.error('加载商品数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 排序商品
const sortGoods = () => {
  let sortedGoods = [...goodsList.value]

  switch (activeSort.value) {
    case 'sales':
      sortedGoods.sort((a, b) => b.sales - a.sales)
      break
    case 'price':
      if (priceOrder.value === 'asc') {
        sortedGoods.sort((a, b) => a.price - b.price)
      } else {
        sortedGoods.sort((a, b) => b.price - a.price)
      }
      break
    case 'newest':
      // 新上架排序，按ID倒序
      sortedGoods.sort((a, b) => {
        const idA = parseInt(a.id.toString().replace('goods_', ''))
        const idB = parseInt(b.id.toString().replace('goods_', ''))
        return idB - idA
      })
      break
    case 'comprehensive':
    default:
      // 综合排序
      sortedGoods.sort((a, b) => {
        const scoreA = a.sales * 0.3 + a.price * 0.2 + (a.isLiked ? 100 : 0)
        const scoreB = b.sales * 0.3 + b.price * 0.2 + (b.isLiked ? 100 : 0)
        return scoreB - scoreA
      })
      break
  }

  goodsList.value = sortedGoods
}

// 搜索处理
const handleSearch = () => {
  console.log('搜索商品:', searchKeyword.value)
  loadGoods(searchKeyword.value)
}

// 搜索输入处理
const handleSearchInput = () => {
  // 检测输入框是否为空
  if (!searchKeyword.value.trim()) {
    // 清除分类状态
    categoryName.value = ''
    // 重置排序为综合
    activeSort.value = 'comprehensive'
    // 重新加载商品数据
    loadGoods()
  }
  // 实时搜索可以在这里实现
}

// 清空搜索
const handleSearchClear = () => {
  searchKeyword.value = ''
  // 清除分类状态
  categoryName.value = ''
  // 重置排序为综合
  activeSort.value = 'comprehensive'
  // 重新加载商品数据
  loadGoods()
}

// 排序处理
const handleSortChange = (sortValue: string) => {
  if (sortValue === 'price' && activeSort.value === 'price') {
    // 如果点击的是当前价格排序，切换升降序
    priceOrder.value = priceOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    activeSort.value = sortValue
    if (sortValue === 'price') {
      priceOrder.value = 'desc' // 默认降序
    }
  }

  // 重新排序商品
  sortGoods()
}

// 切换显示模式
const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'grid' ? 'list' : 'grid'
}

// 筛选弹窗处理
const openFilterPopup = () => {
  showFilterPopup.value = true
}

const applyFilters = (filters: any) => {
  console.log('应用筛选条件:', filters)
  currentFilters.value = { ...filters }
  showFilterPopup.value = false

  // 重新加载商品数据并应用筛选条件
  loadGoods(searchKeyword.value)
}

const resetFilters = () => {
  currentFilters.value = {
    priceMin: '',
    priceMax: '',
    distance: '5km',
    freshness: 'all',
  }
}

// 触底加载更多
const handleReachBottom = () => {
  if (!loading.value && hasMore.value) {
    loadGoods(searchKeyword.value, currentPage.value + 1, true)
  }
}

// 商品点击处理
const handleGoodsClick = (item: GoodsItem) => {
  uni.navigateTo({
    url: `/pages/user/goods-detail?id=${item.id}&title=${encodeURIComponent(item.title)}`,
  })
}

// 列表商品点击处理
const handleListGoodsClick = (item: any) => {
  uni.navigateTo({
    url: `/pages/user/goods-detail?id=${item.id}&title=${encodeURIComponent(item.title)}`,
  })
}

// 点赞处理
const handleLikeClick = (item: GoodsItem) => {
  const index = goodsList.value.findIndex((goods) => goods.id === item.id)
  if (index !== -1) {
    goodsList.value[index].isLiked = !goodsList.value[index].isLiked
    uni.showToast({
      title: goodsList.value[index].isLiked ? '已收藏' : '已取消收藏',
      icon: 'success',
      duration: 1000,
    })
  }
}

// 列表点赞处理
const handleListLikeClick = (item: any) => {
  const index = goodsList.value.findIndex((goods) => goods.id === item.id)
  if (index !== -1) {
    goodsList.value[index].isLiked = !goodsList.value[index].isLiked
    uni.showToast({
      title: goodsList.value[index].isLiked ? '已收藏' : '已取消收藏',
      icon: 'success',
      duration: 1000,
    })
  }
}

// 页面初始化
onMounted(() => {
  loadGoods()
})
</script>

<style lang="scss" scoped>
.merchant-goods-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
}

.navbar-content {
  display: flex;
  align-items: center;
  height: 44px;
  position: relative;
}

.navbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 44px;
}

:deep(.search-component) {
  width: 100%;
  background-color: transparent;
}

/* 排序筛选栏 */
.sort-filter-bar {
  display: flex;
  background: #fff;
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
  justify-content: space-between;
  align-items: center;
}

.sort-item {
  position: relative;
  padding: 6px 0;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-right: 24px;

  &:last-of-type {
    margin-right: 0;
  }

  &.active {
    .sort-text {
      color: #1890ff;
      font-weight: 600;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 20px;
      height: 2px;
      background: #1890ff;
      border-radius: 1px;
    }
  }

  &:active {
    transform: scale(0.95);
  }
}

.sort-text {
  font-size: 14px;
  color: #666;
  transition: all 0.2s;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-button {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;

  .iconfont-sys {
    font-size: 16px;
    color: #666;
  }

  &:active {
    transform: scale(0.95);
  }
}

.view-toggle-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;

  .iconfont-sys {
    font-size: 16px;
    color: #666;
  }

  &:active {
    transform: scale(0.95);
  }
}

/* 商品列表容器 */
.goods-scroll-container {
  flex: 1;
  overflow: auto;
}

.goods-content {
  padding: 8px;
  padding-bottom: 68px; /* 为底部导航栏留出空间，避免最后一个商品被遮挡 */
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  flex: 1;
}

.empty-text {
  font-size: 16px;
  color: #999;
  margin: 20px 0 8px;
}

.empty-tip {
  font-size: 14px;
  color: #ccc;
}
</style>
