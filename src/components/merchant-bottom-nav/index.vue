<template>
  <wd-tabbar
    :model-value="currentNav"
    @change="handleTabChange"
    fixed
    placeholder
    safeAreaInsetBottom
    bordered
    active-color="#1890ff"
    inactive-color="#666666"
  >
    <wd-tabbar-item v-for="item in navItems" :key="item.value" :name="item.value" :title="item.label">
      <template #icon>
        <view
          class="iconfont-sys"
          :class="item.icon"
          :style="{
            fontSize: '20px',
            color: currentNav === item.value ? '#1890ff' : '#666666',
          }"
        ></view>
      </template>
    </wd-tabbar-item>
  </wd-tabbar>
</template>

<script lang="ts" setup>
import { computed, onUnmounted } from 'vue'
import { useGlobalSafeArea } from '@/hooks/useSafeArea'

// 导航项接口
interface NavItem {
  label: string
  value: string
  icon: string
  route: string
}

// 定义组件属性
interface Props {
  currentNav: string
  merchantId: string
}

const props = withDefaults(defineProps<Props>(), {
  currentNav: 'home',
  merchantId: '1'
})

// 使用全局安全区域 hook
const { safeAreaInsetsBottom } = useGlobalSafeArea()

// 导航配置
const navItems: NavItem[] = [
  { 
    label: '首页', 
    value: 'home', 
    icon: 'iconsys-shouye',
    route: `/pages/user/merchant-home?id=${props.merchantId}`
  },
  { 
    label: '全部商品', 
    value: 'goods', 
    icon: 'iconsys-gouwuche',
    route: `/pages/user/merchant-goods?id=${props.merchantId}`
  },
  { 
    label: '店铺分类', 
    value: 'category', 
    icon: 'iconsys-fenlei',
    route: `/pages/user/merchant-category?id=${props.merchantId}`
  },
]

// 路由映射
const routeMap = computed(() => {
  const map: Record<string, string> = {}
  navItems.forEach((item) => {
    map[item.value] = item.route
  })
  return map
})

// 页面跳转防抖
let isJumping = false

// 处理标签切换
const handleTabChange = ({ value }: { value: string }) => {
  // 如果点击的是当前页面，不进行跳转
  if (props.currentNav === value) {
    return
  }

  // 防止快速连续点击
  if (isJumping) {
    return
  }

  const targetRoute = routeMap.value[value]
  if (targetRoute) {
    isJumping = true
    console.log('商户导航切换到:', targetRoute)

    // 统一使用 redirectTo，这是最适合 tab 切换的方式
    uni.redirectTo({
      url: targetRoute,
      success: () => {
        console.log('redirectTo 跳转成功:', targetRoute)
        // 重置跳转状态
        setTimeout(() => {
          isJumping = false
        }, 1)
      },
      fail: (err) => {
        console.log('redirectTo 跳转失败，尝试 reLaunch:', err)
        // 如果 redirectTo 失败，使用 reLaunch
        uni.reLaunch({
          url: targetRoute,
          success: () => {
            console.log('reLaunch 跳转成功:', targetRoute)
            // 重置跳转状态
            setTimeout(() => {
              isJumping = false
            }, 10)
          },
          fail: (launchErr) => {
            console.error('页面跳转失败:', launchErr)
            // 即使失败也要重置状态
            isJumping = false
          }
        })
      },
    })
  }
}

// 组件卸载时清除状态
onUnmounted(() => {
  isJumping = false
})
</script>

<style lang="scss" scoped>
// 如果需要自定义wd-tabbar的样式，可以在这里添加
</style> 