// 商品数据类型
export interface GoodsItem {
  id: number | string
  title: string
  price?: number
  originalPrice?: number | null
  color?: string
  sales: number
  isLiked: boolean
  image?: string
}

// 组件属性类型
export interface GoodsListProps {
  // 商品列表
  goodsList: GoodsItem[]
  // 是否显示价格
  showPrice?: boolean
  // 是否正在加载
  loading?: boolean
  // 是否还有更多数据
  hasMore?: boolean
  // 加载文本
  loadingText?: string
  // 没有更多数据文本
  noMoreText?: string
}

// 组件事件类型
export interface GoodsListEmits {
  // 商品点击事件
  (e: 'goods-click', item: GoodsItem): void
  // 点赞事件
  (e: 'like-click', item: GoodsItem): void
} 