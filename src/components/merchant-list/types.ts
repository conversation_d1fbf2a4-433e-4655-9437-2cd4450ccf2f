// 热销商品类型
export interface HotGoodsItem {
  id: string | number
  image: string
  price: number
}

// 商户数据类型
export interface MerchantItem {
  id: string | number
  name: string
  logo?: string
  description?: string
  address?: string
  distance?: string
  followers?: number
  goodsCount?: number
  rating?: number
  isFollowed?: boolean
  tags?: string[]
  status?: 'open' | 'closed' | 'break' // 营业状态
  hotGoods?: HotGoodsItem[]  // 添加热销商品字段
}

// 组件属性类型
export interface MerchantListProps {
  // 商户列表
  merchantList: MerchantItem[]
  // 是否显示关注按钮
  showFollow?: boolean
  // 是否显示距离信息
  showDistance?: boolean
  // 是否显示评分
  showRating?: boolean
  // 是否正在加载
  loading?: boolean
  // 是否还有更多数据
  hasMore?: boolean
  // 加载文本
  loadingText?: string
  // 没有更多数据文本
  noMoreText?: string
}

// 组件事件类型
export interface MerchantListEmits {
  // 商户点击事件
  (e: 'merchant-click', item: MerchantItem): void
  // 关注事件
  (e: 'follow-click', item: MerchantItem): void
} 