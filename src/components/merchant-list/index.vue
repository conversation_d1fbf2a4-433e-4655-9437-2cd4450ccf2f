<template>
  <view class="merchant-list">
    <!-- 商户列表 -->
    <view class="merchant-container">
      <view
        class="merchant-item"
        v-for="item in merchantList"
        :key="item.id"
        @click="handleMerchantClick(item)"
      >
        <!-- 顶部区域 -->
        <view class="merchant-header">
          <!-- 左侧商户logo -->
          <view class="merchant-logo-container">
            <image v-if="item.logo" class="merchant-logo" :src="item.logo" mode="aspectFill" />
            <view v-else class="merchant-logo-placeholder">
              <text class="logo-text">{{ item.name.charAt(0) }}</text>
            </view>

            <!-- 营业状态 -->
            <view class="status-badge" :class="item.status || 'open'">
              <text class="status-text">
                {{ getStatusText(item.status || 'open') }}
              </text>
            </view>
          </view>

          <!-- 中间商户信息 -->
          <view class="merchant-info">
            <!-- 商户名称和距离 -->
            <view class="name-distance-row">
              <text class="merchant-name">{{ item.name }}</text>
              <view v-if="item.distance" class="distance-tag">
                <text class="distance-text">{{ item.distance }}</text>
              </view>
            </view>

            <!-- 粉丝数和商品数 -->
            <view class="stats-row">
              <text v-if="item.followers" class="stat-item">{{ item.followers }}万粉丝</text>
              <text v-if="item.goodsCount" class="stat-item">{{ item.goodsCount }}件商品</text>
            </view>
          </view>

          <!-- 右侧进店按钮 -->
          <view class="enter-store-btn" @click.stop="handleEnterStore(item)">
            <text class="enter-text">进店</text>
          </view>
        </view>

        <!-- 底部商品展示区域 -->
        <view class="goods-section" v-if="item.hotGoods && item.hotGoods.length">
          <scroll-view class="goods-scroll" scroll-x>
            <view class="goods-list">
              <view
                class="goods-item"
                v-for="(goods, index) in item.hotGoods"
                :key="index"
                @click.stop="handleGoodsClick(goods)"
              >
                <image class="goods-image" :src="goods.image" mode="aspectFill" />
                <view class="price-tag">
                  <text class="price-text">¥{{ goods.price }}</text>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" v-if="loading || !hasMore">
      <view v-if="loading" class="loading-item">
        <wd-loading size="20px" />
        <text class="loading-text">{{ loadingText }}</text>
      </view>
      <view v-else-if="!hasMore" class="no-more-item">
        <text class="no-more-text">{{ noMoreText }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { MerchantItem, MerchantListProps, MerchantListEmits, HotGoodsItem } from './types'

// 设置默认值
const props = withDefaults(defineProps<MerchantListProps>(), {
  showFollow: true,
  showDistance: true,
  showRating: true,
  loading: false,
  hasMore: true,
  loadingText: '加载中...',
  noMoreText: '没有更多商户了',
})

const emit = defineEmits<MerchantListEmits>()

// 获取营业状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    open: '营业中',
    closed: '已打烊',
    break: '休息中',
  }
  return statusMap[status] || '营业中'
}

// 商户点击处理
const handleMerchantClick = (item: MerchantItem) => {
  emit('merchant-click', item)
}

// 商品点击处理
const handleGoodsClick = (goods: HotGoodsItem) => {
  console.log('商品点击:', goods)
  // 可以发出商品点击事件或直接跳转
  uni.navigateTo({
    url: `/pages/user/goods-detail?id=${goods.id}`,
  })
}

// 关注点击处理
const handleFollowClick = (item: MerchantItem) => {
  emit('follow-click', item)
}

// 进店按钮处理
const handleEnterStore = (item: MerchantItem) => {
  console.log('进店按钮被点击!')
  console.log('商户信息:', item)
  console.log('商户ID:', item.id)
  console.log('商户名称:', item.name)
  
  try {
    // 跳转到商户主页
    const url = `/pages/user/merchant-home?id=${item.id}&name=${encodeURIComponent(item.name)}`
    console.log('跳转URL:', url)
    
    uni.navigateTo({
      url: url,
      success: () => {
        console.log('跳转成功')
      },
      fail: (err) => {
        console.error('跳转失败:', err)
        uni.showToast({
          title: '跳转失败',
          icon: 'error'
        })
      }
    })
  } catch (error) {
    console.error('进店按钮处理错误:', error)
  }
}
</script>

<style lang="scss" scoped>
.merchant-list {
  width: 100%;
}

// 商户容器
.merchant-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

// 商户项
.merchant-item {
  background: #f5f5f5;
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 0;
  transition: background-color 0.1s;

  &:active {
    background-color: rgba(0, 0, 0, 0.02);
  }
}

// 顶部区域
.merchant-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

// 左侧logo容器
.merchant-logo-container {
  width: 60px;
  height: 60px;
  position: relative;
  flex-shrink: 0;
}

.merchant-logo {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: block;
}

.merchant-logo-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-text {
  font-size: 28px;
  color: #fff;
  font-weight: 600;
}

// 营业状态徽章
.status-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;

  &.open {
    background: #4caf50;
  }

  &.closed {
    background: #f44336;
  }

  &.break {
    background: #ff9800;
  }
}

.status-text {
  color: #fff;
  font-size: 10px;
  line-height: 1;
}

// 中间商户信息
.merchant-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.name-distance-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.merchant-name {
  font-size: 18px;
  color: #333;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}

.distance-tag {
  margin-left: 8px;
  flex-shrink: 0;
}

.distance-text {
  font-size: 12px;
  color: #666;
  font-weight: 400;
}

.stats-row {
  display: flex;
  align-items: center;
}

.stat-item {
  font-size: 13px;
  color: #666;
  margin-right: 12px;

  &:last-child {
    margin-right: 0;
  }
}

// 右侧进店按钮
.enter-store-btn {
  padding: 8px 16px;
  background: linear-gradient(to right, #ff9500, #ff6b35);
  border-radius: 18px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.enter-text {
  font-size: 13px;
  color: #fff;
  font-weight: 500;
  line-height: 1;
}

// 商品展示区域
.goods-section {
  margin-top: 12px;
}

.goods-scroll {
  width: 100%;
  border-radius: 8px;
  white-space: nowrap;
  
  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
  }
}

.goods-list {
  display: flex;
  gap: 4px;
}

.goods-item {
  position: relative;
  flex-shrink: 0;
  width: 120px;
  height: 120px;
  border-radius: 12px;
  overflow: visible;
  transition: transform 0.2s;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.goods-image {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 0;
}

.price-tag {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.6);
  padding: 3px 6px;
  border-radius: 8px;
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 18px;
}

.price-text {
  font-size: 11px;
  color: #fff;
  font-weight: 500;
  line-height: 1;
  text-align: center;
}

// 加载状态
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.loading-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-text {
  font-size: 14px;
  color: #999;
}

.no-more-item {
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-more-text {
  font-size: 14px;
  color: #ccc;
}
</style> 