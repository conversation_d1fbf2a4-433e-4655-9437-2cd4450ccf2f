<template>
  <view class="waterfall-goods">
    <!-- 瀑布流容器 -->
    <view class="waterfall-container">
      <view
        class="waterfall-column"
        v-for="(column, columnIndex) in waterfallColumns"
        :key="columnIndex"
      >
        <view
          class="goods-card"
          v-for="item in column"
          :key="item.id"
          @click="handleGoodsClick(item)"
        >
          <view class="goods-image-container">
            <image
              v-if="item.image"
              class="goods-image"
              :src="item.image"
              mode="aspectFill"
              :style="{ height: item.imageHeight + 'px' }"
            />
            <view
              v-else
              class="goods-image-placeholder"
              :style="{ height: item.imageHeight + 'px', backgroundColor: item.color }"
            ></view>
          </view>
          <view class="goods-info">
            <text class="goods-title">{{ item.title }}</text>
            <view class="goods-price" v-if="showPrice">
              <text class="current-price">¥{{ item.price }}</text>
              <text v-if="item.originalPrice" class="original-price">
                ¥{{ item.originalPrice }}
              </text>
            </view>
            <view class="goods-stats">
              <text class="goods-sales">已售{{ item.sales }}件</text>
              <view class="goods-like" @click.stop="handleLikeClick(item)">
                <wd-icon name="like" :color="item.isLiked ? '#ff4757' : '#ccc'" size="14px" />
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" v-if="loading || !hasMore">
      <view v-if="loading" class="loading-item">
        <wd-loading size="20px" />
        <text class="loading-text">{{ loadingText }}</text>
      </view>
      <view v-else-if="!hasMore" class="no-more-item">
        <text class="no-more-text">{{ noMoreText }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

// 定义商品数据类型
interface GoodsItem {
  id: number | string
  title: string
  price?: number
  originalPrice?: number | null
  color?: string
  sales: number
  isLiked: boolean
  imageHeight: number
  image?: string
}

// 定义组件属性
interface Props {
  // 商品列表
  goodsList: GoodsItem[]
  // 瀑布流列数
  columnCount?: number
  // 是否显示价格
  showPrice?: boolean
  // 是否正在加载
  loading?: boolean
  // 是否还有更多数据
  hasMore?: boolean
  // 加载文本
  loadingText?: string
  // 没有更多数据文本
  noMoreText?: string
}

// 定义事件
interface Emits {
  // 商品点击事件
  (e: 'goods-click', item: GoodsItem): void
  // 点赞事件
  (e: 'like-click', item: GoodsItem): void
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  columnCount: 2,
  showPrice: false,
  loading: false,
  hasMore: true,
  loadingText: '加载中...',
  noMoreText: '没有更多商品了',
})

const emit = defineEmits<Emits>()

// 瀑布流数据计算
const waterfallColumns = computed(() => {
  const columns = Array.from({ length: props.columnCount }, () => [] as GoodsItem[])
  const columnHeights = Array(props.columnCount).fill(0)

  props.goodsList.forEach((item) => {
    // 找到高度最小的列
    const minHeightIndex = columnHeights.indexOf(Math.min(...columnHeights))
    columns[minHeightIndex].push(item)
    // 更新列高度（简化处理，实际应该根据图片高度和内容高度计算）
    const cardHeight = item.imageHeight + (props.showPrice ? 134 : 114) // 包含文字区域高度，减少6px底部间距
    columnHeights[minHeightIndex] += cardHeight
  })

  return columns
})

// 商品点击处理
const handleGoodsClick = (item: GoodsItem) => {
  emit('goods-click', item)
}

// 点赞点击处理
const handleLikeClick = (item: GoodsItem) => {
  emit('like-click', item)
}
</script>

<style lang="scss" scoped>
.waterfall-goods {
  width: 100%;
}

// 瀑布流容器
.waterfall-container {
  display: flex;
  gap: 12px;
}

.waterfall-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

// 商品卡片
.goods-card {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.1s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.goods-image-container {
  width: 100%;
  overflow: hidden;
}

.goods-image {
  width: 100%;
  display: block;
  border-radius: 8px 8px 0 0;
}

.goods-image-placeholder {
  width: 100%;
  display: block;
  border-radius: 8px 8px 0 0;
}

.goods-info {
  padding: 12px 12px 6px 12px;
}

.goods-title {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  font-weight: 500;
}

.goods-price {
  display: flex;
  align-items: baseline;
  gap: 6px;
  margin-bottom: 8px;
}

.current-price {
  font-size: 16px;
  color: #ff4757;
  font-weight: 600;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.goods-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-sales {
  font-size: 12px;
  color: #999;
}

.goods-like {
  padding: 4px;
  transition: transform 0.2s;

  &:active {
    transform: scale(0.9);
  }
}

// 加载状态样式
.loading-container {
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.no-more-item {
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-more-text {
  font-size: 14px;
  color: #999;
  padding: 10px 0;
}
</style> 