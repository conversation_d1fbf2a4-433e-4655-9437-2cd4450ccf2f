<template>
  <view class="other-login-section">
    <text class="other-login-text">其他方式登录</text>
    <view class="other-login-options">
      <view class="login-option" @click="$emit('wechat-login')">
        <image src="/static/images/weixin.png" class="login-icon" mode="aspectFit" />
        <!-- #ifdef MP-WEIXIN -->
        <text class="login-option-text">微信登录</text>
        <!-- #endif -->
        <!-- #ifdef APP-PLUS -->
        <text class="login-option-text">微信登录</text>
        <!-- #endif -->
        <!-- #ifdef H5 -->
        <text class="login-option-text">微信扫码</text>
        <!-- #endif -->
      </view>
      
      <!-- #ifdef MP-WEIXIN -->
      <!-- 微信手机号快捷登录 -->
      <button 
        class="login-option phone-login-btn"
        open-type="getPhoneNumber" 
        @getphonenumber="$emit('get-phone-number', $event)"
      >
        <view class="phone-icon-wrapper">
          <text class="iconfont-sys iconsys-dianhua phone-icon"></text>
        </view>
        <text class="login-option-text">手机号登录</text>
      </button>
      <!-- #endif -->
      
      <!-- #ifndef MP-WEIXIN -->
      <!-- 非微信环境显示普通手机号登录 -->
      <view class="login-option" @click="$emit('switch-sms-login')">
        <view class="phone-icon-wrapper">
          <text class="iconfont-sys iconsys-dianhua phone-icon"></text>
        </view>
        <text class="login-option-text">手机号登录</text>
      </view>
      <!-- #endif -->
    </view>
  </view>
</template>

<script lang="ts" setup>
defineEmits(['wechat-login', 'get-phone-number', 'switch-sms-login'])
</script>

<style lang="scss" scoped>
.other-login-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 32px;
  gap: 20px;
}

.other-login-text {
  font-size: 14px;
  color: #666;
}

.other-login-options {
  display: flex;
  gap: 40px;
}

.login-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s;

  &:active {
    opacity: 0.7;
  }
}

.login-icon {
  width: 36px;
  height: 36px;
}

.phone-icon-wrapper {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50%;
}

.phone-icon {
  font-size: 20px;
  color: #333;
}

.login-option-text {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

/* 微信手机号登录按钮 */
.phone-login-btn {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s;

  &::after {
    display: none;
  }

  &:active {
    opacity: 0.7;
  }
}
</style> 