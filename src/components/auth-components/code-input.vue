<template>
  <view class="code-input-wrapper">
    <wd-input
      :modelValue="modelValue"
      @update:modelValue="$emit('update:modelValue', $event)"
      type="number"
      :placeholder="placeholder"
      :clearable="true"
      :maxlength="6"
      custom-class="code-input"
    >
      <template #suffix>
        <wd-button
          size="small"
          :disabled="!canSend || countdown > 0"
          @click="$emit('send')"
          custom-class="code-btn"
        >
          {{ countdown > 0 ? `${countdown}秒后重试` : '获取验证码' }}
        </wd-button>
      </template>
    </wd-input>
  </view>
</template>

<script lang="ts" setup>
interface Props {
  modelValue: string
  canSend: boolean
  countdown: number
  placeholder?: string
}

withDefaults(defineProps<Props>(), {
  placeholder: '请输入验证码'
})

defineEmits(['update:modelValue', 'send'])
</script>

<style lang="scss" scoped>
.code-input-wrapper {
  margin-bottom: 24px;

  :deep(.code-input) {
    border: none !important;
    border-radius: 0 !important;
    background: transparent !important;
    box-shadow: none !important;
    padding: 8px 0 !important;
    border-bottom: 1px solid #e1e5e9 !important;
    position: relative;

    .wd-input__inner {
      height: 48px;
      font-size: 16px;
      padding: 0 4px;
      background: transparent !important;
      border: none !important;
      box-shadow: none !important;
    }
  }

  :deep(.code-btn) {
    height: 32px;
    padding: 0 12px;
    font-size: 14px;
    border-radius: 6px;
    background: #007aff;
    color: #fff;
    border: none;
    margin-right: 4px;
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.95);
      box-shadow: 0 2px 4px rgba(0, 122, 255, 0.3);
    }

    &.wd-button--disabled {
      background: #ccc;
      transform: none;
      box-shadow: none;
    }
  }
}
</style> 