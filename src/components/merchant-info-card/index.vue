<template>
  <view class="shop-info-section">
    <view class="shop-info" @click.stop="handleClick">
      <view
        class="shop-logo"
        :style="{ backgroundImage: `url(${merchantLogo})` }"
      ></view>
      <view class="shop-detail">
        <text class="shop-name">{{ shopName }}</text>
        <view class="shop-location">
          <wd-icon name="location" size="12px" color="#999" />
          <text class="location-text">{{ location }}</text>
          <text class="distance-text">· {{ distance }}</text>
        </view>
      </view>
      <wd-icon name="arrow-right" size="14px" color="#999" />
    </view>
  </view>
</template>

<script lang="ts" setup>
// 定义props
interface Props {
  merchantLogo: string
  shopName: string
  location?: string
  distance?: string
  merchantId?: string
}

const props = withDefaults(defineProps<Props>(), {
  location: '朝阳区三里屯',
  distance: '1.2km'
})

// 定义事件
const emit = defineEmits<{
  click: [merchantId: string | undefined]
}>()

// 处理点击事件
const handleClick = () => {
  emit('click', props.merchantId)
}
</script>

<style lang="scss" scoped>
.shop-info-section {
  margin-bottom: 16px;
}

.shop-info {
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: #f8f8f8;
  padding: 16px;
  border-radius: 8px;
  transition: all 0.2s;
  
  &:active {
    background-color: #f0f0f0;
  }
}

.shop-logo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  flex-shrink: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-color: #f5f5f5;
}

.shop-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.shop-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.shop-location {
  display: flex;
  align-items: center;
  gap: 2px;
}

.location-text {
  font-size: 12px;
  color: #666;
}

.distance-text {
  font-size: 12px;
  color: #ff9500;
  font-weight: 500;
}
</style> 