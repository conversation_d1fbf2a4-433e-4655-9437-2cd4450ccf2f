export interface FilterOption {
  label: string
  value: string
}

export interface FilterOptions {
  distance: FilterOption[]
  freshness: FilterOption[]
}

export interface FilterValues {
  priceMin: string
  priceMax: string
  distance: string
  freshness: string
}

export interface FilterPopupProps {
  visible: boolean
  viewMode: 'list' | 'grid'
  filterOptions: FilterOptions
  currentFilters: FilterValues
  showViewModeSwitch?: boolean
}

export interface FilterPopupEmits {
  'update:visible': [value: boolean]
  'update:viewMode': [value: 'list' | 'grid']
  'update:currentFilters': [value: FilterValues]
  'apply': [filters: FilterValues]
  'reset': []
} 