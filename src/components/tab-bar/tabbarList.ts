/**
 * TabBar 配置
 * 纯粹的 TabBar 数据配置，不涉及复杂的策略选择
 */

export const tabbarList = [
  {
    name: 'index',
    text: '主页',
    icon: 'iconsys-shouye1',
    pagePath: 'pages/user/index',
  },
  {
    name: 'category',
    text: '分类',
    icon: 'iconsys-fenlei',
    pagePath: 'pages/user/category',
  },
  {
    name: 'cart',
    text: '购物车',
    icon: 'iconsys-gouwuche',
    pagePath: 'pages/user/cart',
  },
  {
    name: 'mine',
    text: '我的',
    icon: 'iconsys-user',
    pagePath: 'pages/user/mine',
  },
]
const _tabbar = {
  color: '#999999',
  selectedColor: '#018d71',
  backgroundColor: '#F8F8F8',
  borderStyle: 'black',
  height: '50px',
  fontSize: '10px',
  iconWidth: '24px',
  spacing: '3px',
  list: tabbarList,
}

// 0和1 需要显示底部的tabbar的各种配置，以利用缓存
export const tabBar = _tabbar
