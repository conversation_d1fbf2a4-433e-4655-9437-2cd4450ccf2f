import { ref, onMounted, readonly } from 'vue'

/**
 * 安全区域管理 Hook
 * 提供全局的安全区域距离获取和管理
 */
export function useSafeArea() {
  // 安全区域相关状态
  const safeAreaInsetsTop = ref(0)
  const safeAreaInsetsBottom = ref(0)
  const rightSafeArea = ref(0)
  const isInitialized = ref(false)

  /**
   * 初始化安全区域信息
   */
  const initSafeArea = () => {
    try {
      const systemInfo = uni.getSystemInfoSync()
      console.log('系统信息:', systemInfo)

      // 获取安全区域顶部距离，多重兜底
      let topSafeArea = 0
      let bottomSafeArea = 0

      // 方案1：优先使用 safeAreaInsets
      if (systemInfo.safeAreaInsets?.top) {
        topSafeArea = systemInfo.safeAreaInsets.top
      }
      // 方案2：使用 statusBarHeight
      else if (systemInfo.statusBarHeight) {
        topSafeArea = systemInfo.statusBarHeight
      }
      // 方案3：根据平台设置默认值
      else {
        // #ifdef APP-PLUS
        topSafeArea = 44 // iOS 默认状态栏高度
        // #endif
        // #ifdef H5
        topSafeArea = 0 // H5 通常不需要
        // #endif
        // #ifdef MP
        topSafeArea = 32 // 小程序默认状态栏高度
        // #endif
        // #ifdef APP-ANDROID
        topSafeArea = 100
        // #endif
      }

      // 获取底部安全区域
      if (systemInfo.safeAreaInsets?.bottom) {
        bottomSafeArea = systemInfo.safeAreaInsets.bottom
      }

      safeAreaInsetsTop.value = topSafeArea
      safeAreaInsetsBottom.value = bottomSafeArea

      console.log('设置安全区域 - 顶部:', topSafeArea, '底部:', bottomSafeArea)

      // #ifdef MP-WEIXIN
      // 获取胶囊按钮位置信息，避免页头内容与胶囊按钮重叠
      try {
        const menuButtonInfo = uni.getMenuButtonBoundingClientRect()
        const windowInfo = uni.getWindowInfo()
        console.log('胶囊按钮信息:', menuButtonInfo)
        console.log('窗口信息:', windowInfo)

        if (menuButtonInfo && windowInfo) {
          // 计算右侧安全区域：屏幕宽度 - 胶囊左边界坐标
          rightSafeArea.value = windowInfo.windowWidth - menuButtonInfo.left + 8
        }
      } catch (error) {
        console.warn('获取胶囊按钮信息失败:', error)
        rightSafeArea.value = 90 // 设置默认值
      }
      // #endif

      isInitialized.value = true
    } catch (error) {
      console.error('获取系统信息失败:', error)
      // 设置默认安全区域
      safeAreaInsetsTop.value = 44
      safeAreaInsetsBottom.value = 0
      rightSafeArea.value = 90
      isInitialized.value = true
    }
  }

  // 如果还没有初始化，则立即初始化
  if (!isInitialized.value) {
    initSafeArea()
  }

  return {
    safeAreaInsetsTop: readonly(safeAreaInsetsTop),
    safeAreaInsetsBottom: readonly(safeAreaInsetsBottom),
    rightSafeArea: readonly(rightSafeArea),
    isInitialized: readonly(isInitialized),
    initSafeArea,
  }
}

/**
 * 全局安全区域实例
 * 确保整个应用使用同一个安全区域实例
 */
let globalSafeAreaInstance: ReturnType<typeof useSafeArea> | null = null

/**
 * 获取全局安全区域实例
 */
export function useGlobalSafeArea() {
  if (!globalSafeAreaInstance) {
    globalSafeAreaInstance = useSafeArea()
  }
  return globalSafeAreaInstance
}
