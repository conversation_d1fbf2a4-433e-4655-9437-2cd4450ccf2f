import { CustomRequestOptions } from '@/interceptors/request'
import { getAuthAdapter } from './auth/factory'
import { toast } from '@/utils/toast'
import { userTypeEnum } from '@/store/globalRole'
import { SystemAdapter } from './auth/adapters/systemAdapter'

/**
 * 状态码处理策略配置
 */
const STATUS_HANDLERS = {
  SUCCESS: (code: number) => code >= 200 && code < 300,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
}

class HttpRequestHandler {
  /**
   * 处理成功响应
   */
  private handleSuccess<T>(res: UniApp.RequestSuccessCallbackResult): IResData<T> {
    const data = res.data as IResData<T>

    if(data.code !== 200){
      uni.showToast({
        title: data.message || '异常',
        icon: 'none',
      })
    }
    return data
  }

  /**
   * 处理错误响应
   */
  private async handleErrorResponse<T>(
    res: UniApp.RequestSuccessCallbackResult,
    options: CustomRequestOptions,
    resolve?: (value: IResData<T>) => void,
    reject?: (reason: any) => void,
  ): Promise<boolean> {
    const { statusCode } = res
    const adapter = getAuthAdapter()
    const userType = adapter.getUserType().toString().toUpperCase()

    // 使用策略模式处理不同状态码
    switch (statusCode) {
      case STATUS_HANDLERS.UNAUTHORIZED:
        console.log(`[${userType}] 401 未授权`)

        // 系统用户使用特殊的请求队列机制
        if (adapter.getUserType() === userTypeEnum.system && resolve && reject) {
          const systemAdapter = adapter as SystemAdapter
          const handled = await systemAdapter.handleUnauthorizedWithQueue<T>(options, resolve, reject)
          return handled
        }

        // 其他用户类型使用普通处理方式
        await adapter.handleUnauthorized()
        break

      case STATUS_HANDLERS.FORBIDDEN:
        console.log(`[${userType}] 403 权限不足`)
        adapter.handleForbidden()
        break

      default:
        this.handleOtherErrors<T>(res, options)
    }

    return false // 未处理，需要外部调用reject
  }

  /**
   * 处理其他错误
   */
  private handleOtherErrors<T>(
    res: UniApp.RequestSuccessCallbackResult,
    options: CustomRequestOptions,
  ): void {
    const errorMsg = (res.data as IResData<T>).message || '请求错误'

    if (!options.hideErrorToast) {
      uni.showToast({
        icon: 'none',
        title: errorMsg,
      })
    }
  }

  /**
   * 处理网络失败
   */
  private handleNetworkError(err: UniApp.GeneralCallbackResult): void {
    const adapter = getAuthAdapter()
    const userType = adapter.getUserType().toString().toUpperCase()
    console.error(`[${userType}] 网络请求失败:`, err)

    uni.showToast({
      icon: 'none',
      title: '网络错误，换个网络试试',
    })
  }

  /**
   * 处理响应头（主要用于 Token 刷新检查）
   */
  private async handleResponseHeaders(headers: Record<string, string>): Promise<boolean> {
    const adapter = getAuthAdapter()
    return await adapter.handleResponseHeaders(headers)
  }

  /**
   * 执行 HTTP 请求
   */
  async execute<T>(options: CustomRequestOptions): Promise<IResData<T>> {
    // 记录请求日志
    const adapter = getAuthAdapter()
    const userType = adapter.getUserType().toString().toUpperCase()
    console.log(`[${userType}-HTTP] ${options.method || 'GET'} ${options.url}`)

    return new Promise<IResData<T>>((resolve, reject) => {
      uni.request({
        ...options,
        dataType: 'json',
        // #ifndef MP-WEIXIN
        responseType: 'json',
        // #endif

        // 响应成功处理
        success: async (res) => {
          try {
            // 检查响应头（Token 刷新等）
            const responseHeaders = res.header || {}
            const headerHandleResult = await this.handleResponseHeaders(responseHeaders)

            if (!headerHandleResult) {
              reject(res)
              return
            }

            // 根据状态码处理响应
            if (STATUS_HANDLERS.SUCCESS(res.statusCode)) {
              resolve(this.handleSuccess<T>(res))
            } else {
              const handled = await this.handleErrorResponse<T>(res, options, resolve, reject)
              if (!handled) {
                reject(res)
              }
            }
          } catch (error) {
            console.error(`[${userType}] 响应处理错误:`, error)
            reject(error)
          }
        },

        // 网络失败处理
        fail: (err) => {
          this.handleNetworkError(err)
          reject(err)
        },
      })
    })
  }
}

/**
 * 主要的 HTTP 请求方法
 */
export const http = <T>(options: CustomRequestOptions) => {
  const handler = new HttpRequestHandler()
  return handler.execute<T>(options)
}

/**
 * GET 请求
 */
export const httpGet = <T>(
  url: string,
  query?: Record<string, any>,
  header?: Record<string, any>,
) => {
  return http<T>({
    url,
    query,
    method: 'GET',
    header,
  })
}

/**
 * POST 请求
 */
export const httpPost = <T>(
  url: string,
  data?: Record<string, any>,
  query?: Record<string, any>,
  header?: Record<string, any>,
) => {
  return http<T>({
    url,
    query,
    data,
    method: 'POST',
    header,
  })
}

/**
 * PUT 请求
 */
export const httpPut = <T>(
  url: string,
  data?: Record<string, any>,
  query?: Record<string, any>,
  header?: Record<string, any>,
) => {
  return http<T>({
    url,
    data,
    query,
    method: 'PUT',
    header,
  })
}

/**
 * DELETE 请求
 */
export const httpDelete = <T>(
  url: string,
  query?: Record<string, any>,
  header?: Record<string, any>,
) => {
  return http<T>({
    url,
    query,
    method: 'DELETE',
    header,
  })
}

// 为 http 对象添加便捷方法
http.get = httpGet
http.post = httpPost
http.put = httpPut
http.delete = httpDelete
