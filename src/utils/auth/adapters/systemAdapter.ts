import { useSystemAdminStore } from '@/store/systemAdmin'
import { userTypeEnum } from '@/store/globalRole'
import { systemRequestQueue } from '../systemRequestQueue'
import { CustomRequestOptions } from '@/interceptors/request'

/**
 * 系统管理员适配器
 * 支持 AccessToken + RefreshToken 机制
 */
export class SystemAdapter {
  private store = useSystemAdminStore()
  private isHandlingUnauthorized = false // 防止重复处理401错误
  private isHandlingLogout = false // 防止重复处理登出

  /**
   * 获取访问令牌
   */
  getToken(): string | null {
    return this.store.getAccessToken()
  }

  /**
   * 获取用户类型标识
   */
  getUserType(): userTypeEnum {
    return userTypeEnum.system
  }

  /**
   * 获取默认请求头
   *
   * NOTE: 这里不能设置
   * 'Content-Type': 'application/json',
   *
   * 如果设置了Content-Type，文件上传则会报 （Invalid `boundary` for `multipart/form-data` request）
   */
  getDefaultHeaders(): Record<string, string> {
    return {}
  }

  /**
   * 检查响应头并处理 Token 刷新
   */
  async handleResponseHeaders(headers: Record<string, string>): Promise<boolean> {
    const needRefresh =
      headers['x-token-expired'] === 'true' || headers['X-Token-Expired'] === 'true'

    if (needRefresh) {
      console.log('系统管理员令牌需要刷新')
      const refreshSuccess = await this.store.refreshAccessToken()
      return refreshSuccess
    }

    return true
  }

  /**
   * 检查当前是否在登录页面
   */
  private isOnLoginPage(): boolean {
    const pages = getCurrentPages()
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1]
      const route = currentPage.route || ''
      return route.includes('pages-sys/login/login')
    }
    return false
  }

  /**
   * 处理 401 未授权错误
   */
  async handleUnauthorized(): Promise<boolean> {
    // 防止重复处理
    if (this.isHandlingUnauthorized) {
      console.log('系统管理员401处理中，跳过重复调用')
      return false
    }

    // 如果已经在登录页面，直接返回，避免死循环
    if (this.isOnLoginPage()) {
      console.log('已在系统管理员登录页面，跳过401处理')
      return false
    }

    this.isHandlingUnauthorized = true
    console.log('系统管理员未授权，尝试刷新令牌')

    try {
      const refreshResult = await this.store.refreshAccessToken()
      if (!refreshResult) {
        // 刷新失败，登出并跳转
        this.handleLogout()
        return false
      }
      console.log('系统管理员token刷新成功')
      return true
    } catch (error) {
      console.error('系统管理员令牌刷新失败:', error)
      this.handleLogout()
      return false
    } finally {
      // 重置处理状态
      this.isHandlingUnauthorized = false
    }
  }

  /**
   * 处理系统用户的401错误（带请求队列支持）
   * 这是专门为系统用户设计的401处理方法
   */
  async handleUnauthorizedWithQueue<T>(
    options: CustomRequestOptions,
    resolve: (value: IResData<T>) => void,
    reject: (reason: any) => void,
  ): Promise<boolean> {
    // 特殊处理：如果是logout API的401错误，直接跳过处理，避免死循环
    if (options.url && options.url.includes('/logout')) {
      console.log('[SYSTEM] logout API 401错误，跳过处理避免死循环')
      return false
    }

    // 检查是否正在刷新Token
    if (systemRequestQueue.getRefreshing()) {
      // 正在刷新，将请求加入队列
      console.log(`[SYSTEM] Token刷新中，请求加入队列: ${options.method} ${options.url}`)
      systemRequestQueue.addToQueue(options, resolve, reject)
      return true // 已处理，不需要外部再调用reject
    }

    // 开始刷新Token
    systemRequestQueue.setRefreshing(true)
    console.log('[SYSTEM] 开始处理401错误并刷新Token')

    try {
      const refreshSuccess = await this.handleUnauthorized()

      if (refreshSuccess) {
        // Token刷新成功，处理队列中的请求
        await systemRequestQueue.processQueue(true)

        // 重试当前请求
        try {
          console.log(`[SYSTEM] 重试当前请求: ${options.method} ${options.url}`)
          const result = await this.executeRequest<T>(options)
          resolve(result)
          return true // 已处理，不需要外部再调用reject
        } catch (error) {
          reject(error)
          return true // 已处理，不需要外部再调用reject
        }
      } else {
        // Token刷新失败，处理队列中的请求
        await systemRequestQueue.processQueue(false)
        return false // 刷新失败，让外部处理reject
      }
    } catch (error) {
      console.error('[SYSTEM] 处理401错误时发生异常:', error)
      // 异常情况，处理队列中的请求
      await systemRequestQueue.processQueue(false)
      return false // 异常情况，让外部处理reject
    }
  }

  /**
   * 执行单个请求（用于重试）
   */
  private executeRequest<T>(options: CustomRequestOptions): Promise<IResData<T>> {
    return new Promise<IResData<T>>((resolve, reject) => {
      uni.request({
        ...options,
        dataType: 'json',
        // #ifndef MP-WEIXIN
        responseType: 'json',
        // #endif

        success: (res) => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data as IResData<T>)
          } else {
            // 重试请求失败，直接reject，不再进入401处理逻辑
            console.warn(`[SYSTEM] 重试请求失败，状态码: ${res.statusCode}`)
            reject(res)
          }
        },

        fail: (err) => {
          console.error(`[SYSTEM] 重试请求网络失败:`, err)
          reject(err)
        },
      })
    })
  }

  /**
   * 处理 403 权限不足错误
   */
  handleForbidden(): void {
    uni.showToast({
      icon: 'none',
      title: '系统管理员权限不足',
      duration: 2000,
    })
  }

  /**
   * 处理登出逻辑
   */
  handleLogout(): void {
    // 防止重复处理登出
    if (this.isHandlingLogout) {
      console.log('系统管理员登出处理中，跳过重复调用')
      return
    }

    this.isHandlingLogout = true

    // 清空请求队列
    systemRequestQueue.clearQueue()

    this.store.logout()

    // 如果已经在登录页面，不需要跳转
    if (this.isOnLoginPage()) {
      console.log('已在系统管理员登录页面，跳过跳转')
      this.isHandlingLogout = false
      return
    }

    uni.reLaunch({
      url: '/pages-sys/login/login',
      success: () => {
        uni.showToast({
          icon: 'none',
          title: '登录已过期，请重新登录',
          duration: 2000,
        })
        this.isHandlingLogout = false
      },
      fail: () => {
        console.error('跳转到系统管理员登录页面失败')
        this.isHandlingLogout = false
      },
    })
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn(): boolean {
    return this.store.isLoggedIn
  }

  /**
   * 获取用户信息
   */
  getUserInfo() {
    return this.store.adminInfo
  }
}
