import { useMerchantUserStore } from '@/store/merchantUser'
import { userTypeEnum } from '@/store/globalRole'

/**
 * 商户用户适配器
 * 使用 Redis Session 机制，无 Token 刷新
 */
export class MerchantAdapter {
  private store = useMerchantUserStore()

  /**
   * 获取访问令牌（Session-based）
   */
  getToken(): string | null {
    // 商户用户可能使用 Session ID 或其他标识
    // 这里假设使用某种形式的 token 或 session id
    return this.store.currentSelectedMerchant?.merchantInfo.id || null
  }

  /**
   * 获取用户类型标识
   */
  getUserType(): userTypeEnum {
    return userTypeEnum.merchants
  }


  /**
   * 获取默认请求头
   */
  getDefaultHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    }

    // 添加当前选中的商户信息
    if (this.store.currentSelectedMerchant) {
      headers['X-Merchant-Id'] = this.store.currentSelectedMerchant.merchantInfo.id
      headers['X-Merchant-Code'] = this.store.currentSelectedMerchant.merchantInfo.merchantCode
    }

    return headers
  }

  /**
   * 检查响应头（商户用户不支持刷新）
   */
  async handleResponseHeaders(headers: Record<string, string>): Promise<boolean> {
    // 商户用户使用 Redis Session，不需要处理 Token 刷新
    // 直接返回 true，让正常的错误处理流程处理过期问题
    return true
  }

  /**
   * 处理 401 未授权错误
   */
  async handleUnauthorized(): Promise<boolean> {
    console.log('商户用户 Session 已过期，需要重新登录')
    this.handleLogout()
    return false // 商户用户没有刷新机制，始终返回false
  }

  /**
   * 处理 403 权限不足错误
   */
  handleForbidden(): void {
    uni.showToast({
      icon: 'none',
      title: '商户权限不足',
      duration: 2000
    })
  }

  /**
   * 处理登出逻辑
   */
  handleLogout(): void {
    this.store.reset()
    uni.reLaunch({
      url: '/pages/merchant/login',
      success: () => {
        uni.showToast({
          icon: 'none',
          title: 'Session 已过期，请重新登录',
          duration: 2000
        })
      }
    })
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn(): boolean {
    return this.store.isLoggedIn && this.store.merchantCount > 0
  }

  /**
   * 获取用户信息
   */
  getUserInfo() {
    return {
      userInfo: this.store.userInfo,
      currentMerchant: this.store.currentSelectedMerchant,
      merchantList: this.store.merchantList
    }
  }

  /**
   * 检查商户权限
   */
  hasPermission(permission: string): boolean {
    return this.store.hasPermission(permission)
  }

  /**
   * 检查商户角色
   */
  hasRole(role: string): boolean {
    return this.store.hasRole(role)
  }

  /**
   * 获取当前商户信息
   */
  getCurrentMerchant() {
    return this.store.currentSelectedMerchant
  }
}
