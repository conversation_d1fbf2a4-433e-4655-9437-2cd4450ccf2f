import { CustomRequestOptions } from '@/interceptors/request'

/**
 * 系统用户专用请求队列管理器
 * 用于处理 Token 刷新期间的请求重试
 */
export class SystemRequestQueueManager {
  private queue: Array<{
    options: CustomRequestOptions
    resolve: (value: any) => void
    reject: (reason: any) => void
  }> = []

  private isRefreshing = false

  /**
   * 添加请求到队列
   */
  addToQueue(
    options: CustomRequestOptions,
    resolve: (value: any) => void,
    reject: (reason: any) => void,
  ) {
    this.queue.push({ options, resolve, reject })
    console.log(
      `[SystemQueue] 添加请求到队列: ${options.method} ${options.url}, 队列长度: ${this.queue.length}`,
    )
  }

  /**
   * 设置刷新状态
   */
  setRefreshing(refreshing: boolean) {
    this.isRefreshing = refreshing
    console.log(`[SystemQueue] 设置刷新状态: ${refreshing}`)
  }

  /**
   * 检查是否正在刷新
   */
  getRefreshing(): boolean {
    return this.isRefreshing
  }

  /**
   * 处理队列中的所有请求
   */
  async processQueue(success: boolean) {
    console.log(`[SystemQueue] 处理队列, 成功: ${success}, 队列长度: ${this.queue.length}`)

    const requests = [...this.queue]
    this.queue = [] // 清空队列
    this.isRefreshing = false

    if (success) {
      // Token刷新成功，重试所有请求
      for (const { options, resolve, reject } of requests) {
        try {
          console.log(`[SystemQueue] 重试请求: ${options.method} ${options.url}`)
          const result = await this.executeRequest<any>(options)
          resolve(result)
        } catch (error) {
          console.error(`[SystemQueue] 重试请求失败: ${options.method} ${options.url}`, error)
          reject(error)
        }
      }
    } else {
      // Token刷新失败，拒绝所有请求
      for (const { reject } of requests) {
        reject(new Error('系统用户Token刷新失败'))
      }
    }
  }

  /**
   * 执行请求（简化版，避免递归401处理）
   */
  private executeRequest<T>(options: CustomRequestOptions): Promise<IResData<T>> {
    return new Promise<IResData<T>>((resolve, reject) => {
      uni.request({
        ...options,
        dataType: 'json',
        // #ifndef MP-WEIXIN
        responseType: 'json',
        // #endif

        success: (res) => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data as IResData<T>)
          } else {
            // 重试请求失败，直接reject，不再进入401处理逻辑
            console.warn(`[SystemQueue] 重试请求失败，状态码: ${res.statusCode}`)
            reject(res)
          }
        },

        fail: (err) => {
          console.error(`[SystemQueue] 重试请求网络失败:`, err)
          reject(err)
        },
      })
    })
  }

  /**
   * 清空队列（用于登出时清理）
   */
  clearQueue() {
    console.log(`[SystemQueue] 清空队列, 队列长度: ${this.queue.length}`)
    // 拒绝所有等待中的请求
    for (const { reject } of this.queue) {
      reject(new Error('用户已登出'))
    }
    this.queue = []
    this.isRefreshing = false
  }

  /**
   * 获取队列状态信息
   */
  getQueueInfo() {
    return {
      queueLength: this.queue.length,
      isRefreshing: this.isRefreshing,
    }
  }
}

// 全局系统用户请求队列管理器实例
export const systemRequestQueue = new SystemRequestQueueManager()
