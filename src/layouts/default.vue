<template>
  <wd-config-provider :themeVars="themeVars">
    <!-- 提供全局安全区域上下文 -->
    <view class="global-layout" :style="globalLayoutStyle">
<!--      <wd-watermark :content="content" fullScreen></wd-watermark>-->
      <slot />
    </view>
    <wd-toast />
    <wd-message-box />
    <privacy-popup />
  </wd-config-provider>
</template>

<script lang="ts" setup>
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import { useGlobalSafeArea } from '@/hooks/useSafeArea'

const content = ref<string>('开发中！！')
const themeVars: ConfigProviderThemeVars = {
  // colorTheme: 'red',
  // buttonPrimaryBgColor: '#07c160',
  // buttonPrimaryColor: '#07c160',
}

// 获取全局安全区域实例
const safeArea = useGlobalSafeArea()

// 提供安全区域给子组件使用
provide('safeArea', safeArea)

// 全局布局样式（可选，主要用于调试）
const globalLayoutStyle = computed(() => ({
  // 这里可以添加全局的安全区域样式
  // 通常页面会自己处理安全区域，这里主要是提供数据
}))
</script>

<style scoped>
.global-layout {
  width: 100%;
  height: 100%;
}
</style>
