/**
 * by 菲鸽 on 2024-03-06
 * 路由拦截，白名单模式 - 默认所有页面都需要登录认证
 * 只有明确配置 requireAuth: false 的页面才允许匿名访问
 *
 * 增强版：支持多用户类型的智能拦截
 * - 根据全局用户角色动态决定拦截逻辑
 * - 支持不同用户类型的独立登录页面
 * - 白名单模式：默认需要认证，明确标记才允许匿名访问
 */
import { useMerchantUserStore, useNormalUserStore, useSystemAdminStore } from '@/store'
import { useGlobalRole, userTypeEnum } from '@/store/globalRole'
import { getLastPage, checkPathAuth, getAuthRequiredPages } from '@/utils'

/**
 * 检查用户是否已登录
 * 根据不同用户类型使用不同的登录状态检查逻辑
 */
const isLogined = (userType: userTypeEnum): boolean => {
  try {
    switch (userType) {
      case userTypeEnum.system:
        // 系统管理员：检查 systemAdmin store
        const systemStore = useSystemAdminStore()
        return systemStore.isLoggedIn

      case userTypeEnum.merchants:
        // 商户用户：检查 merchantUser store
        const merchantStore = useMerchantUserStore()
        return merchantStore.isLoggedIn

      case userTypeEnum.user:
      default:
        // 普通用户：检查 normalUser store
        const normalUserStore = useNormalUserStore()
        return normalUserStore.isLoggedIn
    }
  } catch (error) {
    console.error('[ROUTE-INTERCEPTOR] 检查登录状态时出错:', error)
    return false
  }
}

const isDev = import.meta.env.DEV

// 白名单登录拦截器 - 默认需要认证，支持多用户类型
const navigateToInterceptor = {
  // 注意，这里的url是 '/' 开头的，如 '/pages/index/index'，跟 'pages.json' 里面的 path 不同
  // 增加对相对路径的处理，BY 网友 @ideal
  invoke({ url }: { url: string }) {
    try {
      // console.log(url) // /pages/route-interceptor/index?name=feige&age=30
      let path = url.split('?')[0]

      // 处理相对路径
      if (!path.startsWith('/')) {
        const currentPath = getLastPage().route
        const normalizedCurrentPath = currentPath.startsWith('/') ? currentPath : `/${currentPath}`
        const baseDir = normalizedCurrentPath.substring(0, normalizedCurrentPath.lastIndexOf('/'))
        path = `${baseDir}/${path}`
      }

      // 获取当前用户角色类型
      const userType = useGlobalRole().getRole

      // 使用新的权限检查系统
      const authResult = checkPathAuth(path, userType)

      if (authResult.allowed) {
        if (isDev) {
          console.log(`[${userType.toUpperCase()}-ROUTE] 访问允许: ${path} - ${authResult.reason}`)
        }
        return true
      }

      // 检查用户是否已登录
      const hasLogin = isLogined(userType)
      if (hasLogin) {
        if (isDev) {
          console.log(`[${userType.toUpperCase()}-ROUTE] 用户已登录，允许访问: ${path}`)
        }
        return true
      }

      // 需要登录但用户未登录，重定向到对应的登录页面
      const loginRoute = authResult.loginRoute!
      const redirectRoute = `${loginRoute}?redirect=${encodeURIComponent(url)}`

      if (isDev) {
        console.log(`[${userType.toUpperCase()}-ROUTE] 需要登录，重定向: ${path} -> ${loginRoute}`)
      }
      uni.navigateTo({ url: redirectRoute })
      return false
    } catch (error) {
      console.error('[ROUTE-INTERCEPTOR] 路由拦截处理出错:', error)
      return true // 出错时允许访问，避免阻塞用户操作
    }
  },
}

/**
 * 开发环境下的调试信息
 */
const logAuthDebugInfo = () => {
  if (!isDev) return

  const globalRole = useGlobalRole()
  const currentRole = globalRole.getRole

  console.log('当前全局角色:', currentRole)
  console.log('权限模式: 白名单 (默认需要认证)')

  // 打印当前用户的登录状态
  console.log('登录状态检查:')
  const userTypes = [userTypeEnum.system, userTypeEnum.merchants, userTypeEnum.user]
  userTypes.forEach((userType) => {
    const loginStatus = isLogined(userType)
    console.log(`  ${userType}: ${loginStatus ? '已登录' : '未登录'}`)
  })

  // 打印各个用户类型的需要认证页面（白名单模式下大部分页面都需要认证）
  userTypes.forEach((userType) => {
    const authPages = getAuthRequiredPages(userType)
    console.log(
      `${userType} 需要认证的页面 (${authPages.length}个):`,
      authPages.slice(0, 5),
      authPages.length > 5 ? '...' : '',
    )
  })
}

export const routeInterceptor = {
  install() {
    // 开发环境下打印调试信息
    if (isDev) {
      logAuthDebugInfo()
    }

    uni.addInterceptor('navigateTo', navigateToInterceptor)
    uni.addInterceptor('reLaunch', navigateToInterceptor)
    uni.addInterceptor('redirectTo', navigateToInterceptor)
    uni.addInterceptor('switchTab', navigateToInterceptor)
  },
}
