/**
 * 全局类型声明
 */

// UniApp 平台变量
declare const __UNI_PLATFORM__: string

// Vite 环境变量
declare const __VITE_APP_PROXY__: string

// 路由配置相关
declare global {
  interface ImportMeta {
    env: {
      DEV: boolean
      VITE_SERVER_BASEURL: string
      VITE_UPLOAD_BASEURL: string
      VITE_SERVER_BASEURL__WEIXIN_DEVELOP?: string
      VITE_SERVER_BASEURL__WEIXIN_TRIAL?: string
      VITE_SERVER_BASEURL__WEIXIN_RELEASE?: string
      VITE_UPLOAD_BASEURL__WEIXIN_DEVELOP?: string
      VITE_UPLOAD_BASEURL__WEIXIN_TRIAL?: string
      VITE_UPLOAD_BASEURL__WEIXIN_RELEASE?: string
      [key: string]: any
    }
  }
}

export {} 