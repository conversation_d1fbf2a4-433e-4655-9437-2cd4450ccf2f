/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AgreementCheckbox: typeof import('./../components/auth-components/agreement-checkbox.vue')['default']
    AgreementPopup: typeof import('./../components/auth-components/agreement-popup.vue')['default']
    CodeInput: typeof import('./../components/auth-components/code-input.vue')['default']
    FgNavbar: typeof import('./../components/fg-navbar/fg-navbar.vue')['default']
    FilterPopup: typeof import('./../components/filter-popup/index.vue')['default']
    GoodsInfoCard: typeof import('./../components/goods-info-card/index.vue')['default']
    GoodsList: typeof import('./../components/goods-list/index.vue')['default']
    LoadingSpinner: typeof import('./../components/loading-spinner/index.vue')['default']
    LocationHeader: typeof import('./../components/location-header/index.vue')['default']
    LoginInput: typeof import('./../components/auth-components/login-input.vue')['default']
    LoginTabs: typeof import('./../components/auth-components/login-tabs.vue')['default']
    MerchantBottomNav: typeof import('./../components/merchant-bottom-nav/index.vue')['default']
    MerchantInfoCard: typeof import('./../components/merchant-info-card/index.vue')['default']
    MerchantList: typeof import('./../components/merchant-list/index.vue')['default']
    Navbar: typeof import('./../components/layout-components/navbar.vue')['default']
    OtherLogin: typeof import('./../components/auth-components/other-login.vue')['default']
    PrivacyPopup: typeof import('./../components/privacy-popup/privacy-popup.vue')['default']
    TabBar: typeof import('./../components/tab-bar/tab-bar.vue')['default']
    WaterfallGoods: typeof import('./../components/waterfall-goods/index.vue')['default']
  }
}
