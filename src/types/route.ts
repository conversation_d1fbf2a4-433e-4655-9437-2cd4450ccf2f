import { userTypeEnum } from '@/store/globalRole'

/**
 * 页面访问控制配置
 */
export interface PageAccessConfig {
  /** 是否需要认证 */
  requireAuth?: boolean
  /** 允许访问的用户角色列表 */
  allowedRoles?: userTypeEnum[]
  /** 未授权时重定向的页面 */
  redirectTo?: string
}

/**
 * 路由 meta 配置
 */
export interface RouteMeta {
  /** 新版访问控制配置 */
  access?: PageAccessConfig
  /** 页面样式配置 */
  style?: Record<string, any>
}

/**
 * 页面配置接口
 */
export interface PageConfig {
  path: string
  [key: string]: any
}

/**
 * 权限检查结果
 */
export interface AuthCheckResult {
  /** 是否允许访问 */
  allowed: boolean
  /** 需要重定向的登录页面 */
  loginRoute?: string
  /** 检查失败的原因 */
  reason?: string
}
