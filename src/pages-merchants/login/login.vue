<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '商户登录',
  },
  access: {
    requireAuth: false,
  },
}
</route>

<template>
  <view class="login-page">
    <!-- 使用自定义导航栏 -->
    <Navbar
      title="商户登录"
      :fixed="true"
      :placeholder="true"
      @back="goBack"
    />

    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 切换登录方式 -->
      <LoginTabs
        v-model="loginType"
        :tabs="[
          { label: '密码登录', value: 'password' },
          { label: '手机号登录', value: 'sms' }
        ]"
      />

      <!-- 登录表单 -->
      <view class="form-content">
        <!-- 密码登录 -->
        <block v-if="loginType === 'password'">
          <LoginInput
            v-model="loginForm.username"
            placeholder="请输入商户账号/手机号"
            :clearable="true"
            :maxlength="50"
          />
          <LoginInput
            v-model="loginForm.password"
            show-password
            placeholder="请输入密码"
            :clearable="true"
            :maxlength="20"
          />
        </block>

        <!-- 手机号登录 -->
        <block v-else>
          <LoginInput
            v-model="smsForm.phone"
            type="number"
            placeholder="请输入手机号"
            :clearable="true"
            :maxlength="11"
          />
          <CodeInput
            v-model="smsForm.code"
            :can-send="canSendCode"
            :countdown="countdown"
            @send="sendSmsCode"
          />
        </block>

        <!-- 协议同意 -->
        <AgreementCheckbox
          v-model="agreeTerms"
          user-agreement-text="商户服务协议"
          @show-agreement="showUserAgreement"
          @show-privacy="showPrivacyPolicy"
        />

        <!-- 登录按钮 -->
        <wd-button
          type="primary"
          size="large"
          :loading="loginLoading"
          :disabled="!canLogin"
          @click="handleLogin"
          custom-class="login-btn"
        >
          {{ loginLoading ? '登录中...' : agreeTerms ? '登录' : '同意协议并登录' }}
        </wd-button>

        <!-- 忘记密码和注册 -->
        <view class="form-footer">
          <text class="link-text" @click="goToForgotPassword">忘记密码？</text>
          <text class="link-text" @click="goToRegister">申请入驻</text>
        </view>
      </view>

      <!-- 其他登录方式 -->
      <OtherLogin
        @wechat-login="handleWechatLogin"
        @get-phone-number="handleGetPhoneNumber"
        @switch-sms-login="switchToSmsLogin"
      />
    </view>

    <!-- 协议弹窗 -->
    <AgreementPopup
      v-model="showAgreementPopup"
      :title="agreementTitle"
      :content="agreementContent"
    />
  </view>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import PLATFORM from '../../utils/platform'
import WxUtil from '../../utils/wxUtil'

// 登录方式
const loginType = ref<'password' | 'sms'>('password')

// 表单数据
const loginForm = ref({
  username: '',
  password: '',
})

const smsForm = ref({
  phone: '',
  code: '',
})

// 状态管理
const loginLoading = ref(false)
const wechatLoginLoading = ref(false)
const countdown = ref(0)
let countdownTimer: any = null

// 协议弹窗
const showAgreementPopup = ref(false)
const agreementTitle = ref('')
const agreementContent = ref('')

// 协议同意
const agreeTerms = ref(false)

// 计算属性
const canLogin = computed(() => {
  if (loginType.value === 'password') {
    return loginForm.value.username.trim() && loginForm.value.password.trim()
  } else {
    return smsForm.value.phone.trim() && smsForm.value.code.trim()
  }
})

const canSendCode = computed(() => {
  return /^1[3-9]\d{9}$/.test(smsForm.value.phone)
})

// 发送验证码
const sendSmsCode = async () => {
  if (!canSendCode.value) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none',
    })
    return
  }

  try {
    // TODO: 调用发送验证码API
    console.log('发送验证码到:', smsForm.value.phone)

    // 模拟发送成功
    uni.showToast({
      title: '验证码已发送',
      icon: 'success',
    })

    // 开始倒计时
    countdown.value = 60
    countdownTimer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(countdownTimer)
      }
    }, 1000)
  } catch (error: any) {
    console.error('发送验证码失败:', error)
    uni.showToast({
      title: error.message || '发送失败',
      icon: 'none',
    })
  }
}

// 页面方法
const handleLogin = async () => {
  if (!canLogin.value) {
    uni.showToast({
      title: '请完善登录信息',
      icon: 'none',
    })
    return
  }

  // 如果用户没有同意协议，点击按钮时自动同意
  if (!agreeTerms.value) {
    agreeTerms.value = true
  }

  loginLoading.value = true
  try {
    if (loginType.value === 'password') {
      // TODO: 调用密码登录API
      console.log('商户密码登录信息:', loginForm.value)
    } else {
      // TODO: 调用手机号登录API
      console.log('商户手机号登录信息:', smsForm.value)
    }

    // 模拟登录延迟
    await new Promise((resolve) => setTimeout(resolve, 1500))

    // 模拟登录成功
    uni.showToast({
      title: '登录成功',
      icon: 'success',
    })

    // 登录成功后跳转到商户首页
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/merchants/index',
      })
    }, 1000)
  } catch (error: any) {
    console.error('登录失败:', error)

    // 获取错误信息
    let errorMessage = '登录失败'
    if (error instanceof Error) {
      errorMessage = error.message
    } else if (typeof error === 'string') {
      errorMessage = error
    }

    // 显示错误信息
    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000, // 延长显示时间到3秒
    })
  } finally {
    loginLoading.value = false
  }
}

// 微信登录
const handleWechatLogin = async () => {
  // 微信登录需要用户主动同意协议
  if (!agreeTerms.value) {
    uni.showToast({
      title: '请先同意商户服务协议',
      icon: 'none',
    })
    return
  }

  wechatLoginLoading.value = true
  try {
    console.log('开始商户微信登录')

    // TODO: 实现微信登录逻辑
    // 这里可以复用user/login.vue中的微信登录逻辑
    // 但需要调用商户专用的后端接口

    // 模拟微信登录
    await new Promise((resolve) => setTimeout(resolve, 1500))

    uni.showToast({
      title: '微信登录成功',
      icon: 'success',
    })

    // 登录成功后跳转到商户首页
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/merchants/index',
      })
    }, 1000)
  } catch (error: any) {
    console.error('微信登录失败:', error)
    uni.showToast({
      title: error.message || '微信登录失败',
      icon: 'none',
      duration: 3000
    })
  } finally {
    wechatLoginLoading.value = false
  }
}

// 协议相关
const showUserAgreement = () => {
  agreementTitle.value = '商户协议'
  agreementContent.value = `测试商户协议内容

这是测试环境的商户协议。

1. 测试商户条款一
2. 测试商户条款二
3. 测试商户条款三

仅供测试使用。`
  showAgreementPopup.value = true
}

const showPrivacyPolicy = () => {
  agreementTitle.value = '隐私政策'
  agreementContent.value = `测试隐私政策内容

这是测试环境的隐私政策。

1. 测试隐私条款一
2. 测试隐私条款二
3. 测试隐私条款三

仅供测试使用。`
  showAgreementPopup.value = true
}

// 其他页面跳转
const goToForgotPassword = () => {
  uni.showToast({
    title: '请联系客服找回密码',
    icon: 'none',
  })
}

const goToRegister = () => {
  uni.showToast({
    title: '商户入驻功能开发中',
    icon: 'none',
  })
}

// 手机号快捷登录
const handleQuickPhoneLogin = () => {
  // 切换到手机号登录方式
  loginType.value = 'sms'
}

// 切换到短信登录
const switchToSmsLogin = () => {
  loginType.value = 'sms'
}

// 处理微信获取手机号回调
const handleGetPhoneNumber = async (e: any) => {
  console.log('商户微信获取手机号回调:', e)

  if (e.detail.errMsg === 'getPhoneNumber:ok') {
    // 获取到加密的手机号信息
    const { encryptedData, iv, code } = e.detail

    try {
      // TODO: 将加密数据发送到后端解密获取手机号
      console.log('获取到的加密数据:', {
        encryptedData,
        iv,
        code
      })

      // 获取登录code
      const loginResult = await WxUtil.login()
      const wxLoginCode = loginResult.code

      // 模拟后端解密并登录
      uni.showLoading({
        title: '登录中...'
      })

      await new Promise(resolve => setTimeout(resolve, 1500))

      // TODO: 实际项目中应该调用商户专用的后端接口
      /*
      const response = await uni.request({
        url: 'https://your-api-domain.com/api/merchant/auth/wx-phone-login',
        method: 'POST',
        data: {
          code: wxLoginCode,
          phoneCode: code,
          encryptedData,
          iv
        }
      })
      */

      uni.hideLoading()

      // 模拟登录成功
      uni.showToast({
        title: '登录成功',
        icon: 'success'
      })

      // 登录成功后跳转到商户首页
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/merchants/index',
        })
      }, 1000)

    } catch (error: any) {
      uni.hideLoading()
      console.error('商户手机号登录失败:', error)
      uni.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      })
    }
  } else {
    // 用户拒绝授权
    console.log('用户拒绝授权手机号')
    uni.showToast({
      title: '需要授权手机号才能登录',
      icon: 'none'
    })
  }
}

// 返回按钮
const goBack = () => {
  uni.navigateBack({
    fail: () => {
      uni.reLaunch({
        url: '/pages/index/index',
      })
    },
  })
}
</script>

<style lang="scss" scoped>
.login-page {
  width: 100%;
  min-height: 100vh;
  background: #ffffff;
  position: relative;
}

/* 内容区域 - 调整顶部内边距，因为现在有固定的导航栏 */
.content-area {
  padding: 24px 24px 40px;
  width: 100%;
  box-sizing: border-box;
}

/* 表单内容 */
.form-content {
  display: flex;
  flex-direction: column;
  padding: 24px 0;
}

/* 登录按钮 */
:deep(.login-btn) {
  margin-top: 20px;  // 减小顶部间距
  height: 50px;
  border-radius: 12px;
  background: #ff4757;
  font-size: 16px;
  font-weight: 600;
  width: 100%;  // 确保按钮宽度100%
  box-shadow: none !important;  // 移除阴影

  // 移除所有状态下的阴影
  &:hover,
  &:active,
  &:focus {
    box-shadow: none !important;
  }

  &.wd-button--disabled {
    background: #ccc;
    box-shadow: none !important;
  }

  // 确保文字居中
  &.wd-button {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* 表单底部 */
.form-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.link-text {
  font-size: 14px;
  color: #007aff;
  text-decoration: none;

  &:active {
    opacity: 0.7;
  }
}
</style>
