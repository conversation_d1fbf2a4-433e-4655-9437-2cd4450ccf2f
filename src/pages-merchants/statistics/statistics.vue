<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '统计',
  },
}
</route>

<template>
  <view class="statistics-container" :style="{ paddingTop: safeAreaInsets?.top + 'px' }">
    <!-- 页面内容 -->
    <view class="content">
      <text>商户用户 - 统计页面内容</text>
    </view>
    <!-- 底部导航栏 -->
    <TabBar current-page="statistics" />
  </view>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'MerchantStatistics',
})

// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()

</script>

<style lang="scss" scoped>
.statistics-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
