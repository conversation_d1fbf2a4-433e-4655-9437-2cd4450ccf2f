<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '订单',
  },
}
</route>

<template>
  <view class="orders-container" :style="{ paddingTop: safeAreaInsets?.top + 'px' }">
    <!-- 页面内容 -->
    <view class="content">
      <text>商户用户 - 订单页面内容</text>
    </view>
    <!-- 底部导航栏 -->
    <TabBar current-page="orders" />
  </view>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'MerchantOrders',
})

// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()

</script>

<style lang="scss" scoped>
.orders-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
