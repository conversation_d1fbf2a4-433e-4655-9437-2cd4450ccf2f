<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '商户信息管理',
  },
  access: {
    requireAuth: true,
    allowedRoles: ['system'],
  },
}
</route>

<template>
  <view class="merchant-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      title="商户信息管理"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <!-- 搜索区域 - 放在顶部 -->
      <view class="search-section">
        <!-- 第一行 - 两个输入框 -->
        <view class="search-row">
          <view class="search-item">
            <text class="search-label">商户名称：</text>
            <wd-input
              class="search-input"
              placeholder="搜索商户名称"
              v-model="searchKeyword"
              clearable
              @clear="handleSearch"
            />
          </view>
          <view class="search-item">
            <text class="search-label">联系电话：</text>
            <wd-input
              class="search-input"
              placeholder="请输入联系电话"
              v-model="searchPhone"
              clearable
              @clear="handleSearch"
            />
          </view>
        </view>

        <!-- 第二行 - 两个下拉框 -->
        <view class="search-row">
          <view class="search-item">
            <text class="search-label">状态：</text>
            <wd-select-picker
              class="search-select"
              v-model="searchStatusValue"
              :columns="searchStatusOptions"
              type="radio"
              :show-confirm="false"
              @change="onStatusChange"
            />
          </view>
          <view class="search-item">
            <text class="search-label">邮箱地址：</text>
            <wd-input
              class="search-input"
              placeholder="请输入邮箱地址"
              v-model="searchEmail"
              clearable
              @clear="handleSearch"
            />
          </view>
        </view>
      </view>

      <!-- 按钮操作栏 -->
      <view class="button-section">
        <view class="left-buttons">
          <wd-button type="success" size="small" @click="addMerchant">新增</wd-button>
          <wd-button type="info" size="small" icon="refresh" @click="refreshData"></wd-button>
        </view>
        <view class="right-buttons">
          <wd-button type="primary" size="small" @click="handleSearch">搜索</wd-button>
          <wd-button plain size="small" @click="resetSearch">重置</wd-button>
        </view>
      </view>

      <!-- 主要内容区域 -->
      <view class="main-wrapper">
        <!-- 左侧商户分类列表 -->
        <view class="sidebar-container">
          <wd-sidebar
            v-model="selectedCategoryIndex"
            @change="handleCategoryChange"
            custom-class="custom-sidebar"
          >
            <!-- 全部商户选项 -->
            <wd-sidebar-item :value="0" label="全部商户" />
            <!-- 商户分类选项 -->
            <wd-sidebar-item
              v-for="(category, index) in categories"
              :key="category.id"
              :value="index + 1"
              :label="category.category_name"
            />
          </wd-sidebar>
        </view>

        <!-- 右侧商户内容区域 -->
        <view class="content-container">
          <!-- 商户列表区域 -->
          <view class="merchant-list-section">
            <view class="merchant-list-card">
              <!-- 加载状态 -->
              <view v-if="loading" class="loading-state">
                <wd-loading />
                <text class="loading-text">加载中...</text>
              </view>

              <!-- 商户列表 -->
              <view v-else-if="merchantList.length > 0" class="merchant-list">
                <view
                  v-for="merchant in merchantList"
                  :key="merchant.id"
                  class="merchant-item"
                  @click.stop="viewMerchantDetail(merchant)"
                >
                  <!-- 商户头像 -->
                  <view class="merchant-avatar-wrapper">
                    <view class="merchant-avatar">
                      <wd-img
                        v-if="getMerchantAvatar(merchant)"
                        :src="getMerchantAvatar(merchant)"
                        :width="60"
                        :height="60"
                        round
                        mode="aspectFill"
                        custom-class="merchant-avatar-img"
                      >
                        <template #error>
                          <view class="avatar-placeholder">
                            <wd-icon name="store" size="40" color="#ccc" />
                          </view>
                        </template>
                        <template #loading>
                          <view class="avatar-placeholder">
                            <wd-icon name="store" size="40" color="#ddd" />
                          </view>
                        </template>
                      </wd-img>
                      <view v-else class="avatar-placeholder">
                        <wd-icon name="store" size="40" color="#ccc" />
                      </view>
                    </view>
                    <text
                      class="status-tag"
                      :class="{
                        'status-normal': merchant.status === 1,
                        'status-temporary': merchant.status === 2,
                        'status-permanent': merchant.status === 3,
                      }"
                    >
                      {{ getStatusText(merchant.status) }}
                    </text>
                  </view>

                  <!-- 商户信息 -->
                  <view class="merchant-info">
                    <view class="merchant-main">
                      <view class="merchant-left">
                        <text class="merchant-name">{{ merchant.merchant_name }}</text>
                        <text v-if="merchant.platform_commission_rate" class="commission-tag">
                          {{ (parseFloat(merchant.platform_commission_rate) * 100).toFixed(1) }}%
                        </text>
                      </view>
                      <!-- 右箭头移到这里，与商户名称同行 -->
                      <view class="merchant-arrow">
                        <wd-icon name="arrow-right" size="20" color="#999" />
                      </view>
                    </view>
                    <view class="merchant-phone" v-if="merchant.phone">
                      <text class="iconfont-sys iconsys-dianhua"></text>
                      <text class="phone-text">{{ merchant.phone }}</text>
                    </view>
                    <view class="merchant-address" v-if="merchant.address">
                      <text class="address-text">{{ merchant.address }}</text>
                    </view>
                    <view class="merchant-revenue">
                      <text class="revenue-item">月营业额：¥150,000</text>
                      <!-- 操作按钮移到这里 -->
                      <view class="merchant-actions" @click.stop>
                        <wd-button
                          type="icon"
                          custom-class="action-btn"
                          @click="showActionSheet(merchant)"
                        >
                          <text class="iconfont-sys iconsys-gengduo"></text>
                        </wd-button>
                      </view>
                    </view>
                  </view>
                </view>

                <!-- 操作菜单 -->
                <wd-popup
                  v-model="showActions"
                  position="bottom"
                  :safe-area-inset-bottom="true"
                  custom-style="border-radius: 16px 16px 0 0; padding: 0;"
                  @close="closeActionSheet"
                >
                  <view class="merchant-popup-content">
                    <!-- 标题栏 -->
                    <view class="popup-header">
                      <text class="popup-title">商户操作</text>
                      <view class="popup-close" @click="closeActionSheet">
                        <wd-icon name="close" size="20" color="#999" />
                      </view>
                    </view>

                    <!-- 操作选项 -->
                    <view class="popup-actions">
                      <!-- 切换状态 - 直接展示状态选项 -->
                      <view class="status-section">
                        <view class="section-title">
                          <text class="title-text">切换商户状态</text>
                        </view>
                        <view class="status-options">
                          <view
                            v-for="statusOption in statusOptions"
                            :key="statusOption.value"
                            class="status-option"
                            :class="{
                              'status-current':
                                currentMerchant && currentMerchant.status === statusOption.value,
                            }"
                            @click="handleStatusChange(statusOption.value)"
                          >
                            <view
                              class="status-dot"
                              :style="{ backgroundColor: statusOption.color }"
                            ></view>
                            <text class="status-text">{{ statusOption.label }}</text>
                            <view
                              v-if="
                                currentMerchant && currentMerchant.status === statusOption.value
                              "
                              class="status-current-tag"
                            >
                              <text class="current-text">当前</text>
                            </view>
                          </view>
                        </view>
                      </view>

                      <!-- 跟进信息 - 简化为普通文字 -->
                      <view class="follow-info-section">
                        <view class="info-item">
                          <text class="info-label">跟进人：</text>
                          <text class="info-value">张三</text>
                        </view>
                        <view class="info-item">
                          <text class="info-label">跟进佣金：</text>
                          <text class="info-value">1.5%</text>
                        </view>
                      </view>

                      <!-- 删除操作（条件显示） -->
                      <view
                        v-if="
                          currentMerchant &&
                          (currentMerchant.status === 2 || currentMerchant.status === 3)
                        "
                        class="delete-section"
                      >
                        <view class="section-title">
                          <text class="title-text">危险操作</text>
                        </view>
                        <view class="delete-option" @click="deleteMerchant(currentMerchant)">
                          <view class="delete-icon">
                            <wd-icon name="delete" size="20" color="#ff4d4f" />
                          </view>
                          <view class="delete-content">
                            <text class="delete-text">删除商户</text>
                            <text class="delete-desc">此操作不可恢复，请谨慎操作</text>
                          </view>
                        </view>
                      </view>
                    </view>

                    <!-- 修改按钮 -->
                    <view class="popup-edit" @click="editMerchantFromPopup">
                      <text class="edit-text">修改</text>
                    </view>
                  </view>
                </wd-popup>
              </view>

              <!-- 空状态 -->
              <view v-else-if="!loading" class="empty-state">
                <wd-status-tip image="content" :tip="getEmptyTip()" />
              </view>

              <!-- 分页组件 -->
              <view v-if="totalCount > 0" class="pagination-wrapper">
                <wd-pagination
                  v-model="currentPage"
                  :total="totalCount"
                  :page-size="pageSize"
                  @change="handlePageChange"
                  show-icon
                  show-message
                />
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, watch } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import {
  getSystemMerchantsApi,
  deleteSystemMerchantApi,
  changeSystemMerchantStatusApi,
  type ISysMerchantListResponse,
  type ISysMerchantPageRequest,
  type ISysMerchantStatusRequest,
} from '@/api/sys/systemMerchantApi'
import {
  getSystemMerchantCategorySelectItemsApi,
  type ISysMerchantCategorySelectItem,
} from '@/api/sys/systemMerchantCategoryApi'

defineOptions({
  name: 'MerchantManagement',
})

// 商户分类相关状态
const categories = ref<ISysMerchantCategorySelectItem[]>([])
const selectedCategoryIndex = ref<number>(0)
const selectedCategory = computed(() => {
  // 索引0是"全部商户"，返回null
  if (selectedCategoryIndex.value === 0) return null
  // 其他索引减1获取对应分类
  return selectedCategoryIndex.value > 0 ? categories.value[selectedCategoryIndex.value - 1] : null
})

// 搜索参数
const searchKeyword = ref('')
const searchPhone = ref('')
const searchEmail = ref('')
const searchStatusValue = ref('')

// 商户数据
const merchantList = ref<ISysMerchantListResponse[]>([])
const totalCount = ref(0)
const loading = ref(false)

// 状态选项
const statusOptions = ref([
  { label: '正常营业', value: 1, color: '#4caf50' },
  { label: '临时关闭', value: 2, color: '#ff9800' },
  { label: '永久关闭', value: 3, color: '#f44336' },
])

// 搜索状态选项（用于搜索功能）
const searchStatusOptions = ref([
  { label: '全部', value: '' },
  { label: '正常营业', value: 1 },
  { label: '临时关闭', value: 2 },
  { label: '永久关闭', value: 3 },
])

// 分页相关状态
const currentPage = ref(1)
const pageSize = ref(10)

// 首次加载标记
const isFirstLoad = ref(true)

// ActionSheet相关状态
const showActions = ref(false)
const currentMerchant = ref<ISysMerchantListResponse | null>(null)

// 获取商户分类列表
const fetchCategories = async () => {
  try {
    const result = await getSystemMerchantCategorySelectItemsApi()
    if (result.code === 200 && result.data) {
      categories.value = result.data
      console.log('获取商户分类列表成功:', result.data)

      // 默认选择"全部商户"
      selectedCategoryIndex.value = 0
      await fetchMerchants()
    }
  } catch (error) {
    console.error('获取商户分类列表失败:', error)
    uni.showToast({
      title: '获取分类列表失败',
      icon: 'none',
    })
  }
}

// 获取商户列表
const fetchMerchants = async () => {
  loading.value = true
  try {
    // 构建查询参数对象
    const queryParams: any = {
      page: currentPage.value,
      page_size: pageSize.value,
    }

    // 只有选择了具体分类才传递category_id参数
    if (selectedCategory.value) {
      queryParams.category_id = selectedCategory.value.id
    }

    // 处理搜索关键词
    if (searchKeyword.value.trim()) {
      queryParams.merchant_name = searchKeyword.value.trim()
    }

    // 设置其他搜索参数
    if (searchPhone.value.trim()) {
      queryParams.phone = searchPhone.value.trim()
    }
    if (searchEmail.value.trim()) {
      queryParams.email = searchEmail.value.trim()
    }
    if (searchStatusValue.value) {
      queryParams.status = Number(searchStatusValue.value)
    }

    console.log('获取商户列表参数:', queryParams)

    const result = await getSystemMerchantsApi(queryParams)

    merchantList.value = result.data.items || []
    totalCount.value = result.data.total || 0

    console.log('获取商户列表成功:', result.data)
  } catch (error) {
    console.error('获取商户列表失败:', error)
    uni.showToast({
      title: '获取商户列表失败',
      icon: 'none',
    })
    merchantList.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 分类切换处理
const handleCategoryChange = async ({ value }: { value: number }) => {
  console.log('切换分类:', value)
  if (value === 0) {
    console.log('选择全部商户')
  } else {
    console.log('选择分类:', categories.value[value - 1])
  }
  selectedCategoryIndex.value = value
  currentPage.value = 1
  await fetchMerchants()
}

// 获取状态显示文本
const getStatusText = (status: number) => {
  switch (status) {
    case 1:
      return '正常营业'
    case 2:
      return '临时关闭'
    case 3:
      return '永久关闭'
    default:
      return '未知'
  }
}

// 处理搜索
const handleSearch = async () => {
  console.log('执行搜索')
  currentPage.value = 1
  await fetchMerchants()
}

// 状态选择器变化事件
const onStatusChange = ({ value }: { value: number | string }) => {
  console.log('状态变化:', value)
  searchStatusValue.value = value as string
  handleSearch()
}

// 重置搜索
const resetSearch = async () => {
  searchKeyword.value = ''
  searchPhone.value = ''
  searchEmail.value = ''
  searchStatusValue.value = ''
  currentPage.value = 1

  await uni.showToast({
    title: '已重置搜索条件',
    icon: 'success',
  })
  await fetchCategories()
}

// 处理分页变化
const handlePageChange = async ({ value }: { value: number }) => {
  console.log('切换到第', value, '页')
  currentPage.value = value
  await fetchMerchants()
}

// 刷新数据
const refreshData = async () => {
  console.log('刷新商户数据')
  await fetchCategories()
  uni.showToast({
    title: '数据已刷新',
    icon: 'success',
  })
}

// 新增商户
const addMerchant = () => {
  console.log('新增商户')
  uni.navigateTo({
    url: '/pages-sys/merchant/merchant/merchant-form?mode=add',
  })
}

// 编辑商户
const editMerchant = (merchant: ISysMerchantListResponse) => {
  console.log('编辑商户:', merchant)
  uni.navigateTo({
    url: `/pages-sys/merchant/merchant/merchant-form?mode=edit&merchantId=${merchant.id}`,
  })
}

// 查看商户详情
const viewMerchantDetail = (merchant: ISysMerchantListResponse) => {
  console.log('查看商户详情:', merchant)

  uni.navigateTo({
    url: `/pages-sys/merchant/merchant/merchant-detail?merchantId=${
      merchant.id
    }&merchantName=${encodeURIComponent(merchant.merchant_name)}`,
  })
}

// 删除商户
const deleteMerchant = async (merchant: ISysMerchantListResponse) => {
  console.log('删除商户:', merchant)

  uni.showModal({
    title: '确认删除',
    content: `确定要删除商户 "${merchant.merchant_name}" 吗？此操作不可恢复。`,
    success: async (res) => {
      if (res.confirm) {
        try {
          await deleteSystemMerchantApi(merchant.id)

          uni.showToast({
            title: '删除成功',
            icon: 'success',
          })

          // 关闭弹层
          closeActionSheet()

          // 从本地列表中移除已删除的商户，提供更好的用户体验
          const deletedIndex = merchantList.value.findIndex(m => m.id === merchant.id)
          if (deletedIndex > -1) {
            merchantList.value.splice(deletedIndex, 1)
            totalCount.value = Math.max(0, totalCount.value - 1)
          }

          // 如果当前页没有数据了，且不是第一页，则返回上一页
          if (merchantList.value.length === 0 && currentPage.value > 1) {
            currentPage.value = currentPage.value - 1
            await fetchMerchants()
          }
        } catch (error) {
          console.error('删除商户失败:', error)
          uni.showToast({
            title: '删除失败',
            icon: 'none',
          })
        }
      }
    },
  })
}

// 返回功能
const handleBack = () => {
  console.log('返回功能')
  uni.navigateBack()
}

// 获取空状态提示文本
const getEmptyTip = () => {
  if (selectedCategoryIndex.value === 0) {
    return '暂无商户数据'
  }
  if (selectedCategory.value) {
    return `分类 '${selectedCategory.value.category_name}' 下暂无商户`
  }
  return '请选择分类查看商户'
}

// 获取商户头像
const getMerchantAvatar = (merchant: ISysMerchantListResponse) => {
  return merchant.avatar || null
}

// ActionSheet相关方法
const showActionSheet = (merchant: ISysMerchantListResponse) => {
  currentMerchant.value = merchant
  showActions.value = true
}

const closeActionSheet = () => {
  showActions.value = false
  currentMerchant.value = null
}

// 处理状态切换
const handleStatusChange = async (newStatus: number) => {
  if (!currentMerchant.value || currentMerchant.value.status === newStatus) return

  uni.showModal({
    title: '确认切换状态',
    content: `确定要将商户 "${currentMerchant.value.merchant_name}" 的状态切换为 "${getStatusText(
      newStatus,
    )}" 吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          const statusRequest: ISysMerchantStatusRequest = {
            status: newStatus,
          }
          await changeSystemMerchantStatusApi(currentMerchant.value!.id, statusRequest)

          uni.showToast({
            title: '状态切换成功',
            icon: 'success',
          })

          // 更新当前商户状态
          currentMerchant.value!.status = newStatus
          
          // 同时更新商户列表中对应商户的状态，避免刷新页面
          const merchantIndex = merchantList.value.findIndex(m => m.id === currentMerchant.value!.id)
          if (merchantIndex > -1) {
            merchantList.value[merchantIndex].status = newStatus
          }

          // 保持弹层打开，用户可以继续操作
        } catch (error) {
          console.error('切换状态失败:', error)
          uni.showToast({
            title: '切换状态失败',
            icon: 'none',
          })
        }
      }
    },
  })
}

// 处理跟进人（模拟数据）
const handleFollowPerson = (merchant: ISysMerchantListResponse) => {
  console.log('设置跟进人:', merchant)
  uni.showToast({
    title: '跟进人功能（模拟数据）',
    icon: 'none',
  })
}

// 处理跟进佣金（模拟数据）
const handleFollowCommission = (merchant: ISysMerchantListResponse) => {
  console.log('设置跟进佣金:', merchant)
  uni.showToast({
    title: '跟进佣金功能（模拟数据）',
    icon: 'none',
  })
}

// 编辑商户从ActionSheet弹窗
const editMerchantFromPopup = () => {
  if (currentMerchant.value) {
    editMerchant(currentMerchant.value)
    closeActionSheet()
  }
}

// 页面初始化
onMounted(async () => {
  console.log('商户管理页面挂载完成')
  await fetchCategories() // 先获取分类列表
  isFirstLoad.value = false
})

// 页面显示时刷新数据
onShow(async () => {
  console.log('商户管理页面显示')
  if (!isFirstLoad.value) {
    console.log('从其他页面返回，刷新商户数据')
    await fetchMerchants()
  }
})
</script>

<style lang="scss" scoped>
.merchant-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  position: relative;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

// 标题文字设为白色
:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

// 自定义返回按钮样式
:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  background-color: #ffffff;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.main-wrapper {
  display: flex;
  flex: 1;
  flex-direction: row;
  overflow: hidden;
}

.sidebar-container {
  width: 110px;
  min-width: 110px;
  max-width: 110px;
  background-color: #fff;
  overflow-y: auto;
  flex-shrink: 0;
}

// 自定义侧边栏样式
:deep(.custom-sidebar) {
  height: 100%;
  width: 100%;
}

// 修复Sidebar内容宽度问题
:deep(.sidebar-container .wd-sidebar) {
  width: 100% !important;
  height: 100% !important;
}

:deep(.sidebar-container .wd-sidebar-item) {
  width: 100% !important;
  padding: 12px 16px !important;
  min-height: 48px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

:deep(.sidebar-container .wd-sidebar-item__label) {
  flex: 1 !important;
  text-align: left !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  font-size: 14px !important;
  color: #333 !important;
}

:deep(.sidebar-container .wd-sidebar-item--active .wd-sidebar-item__label) {
  color: #4285f4 !important;
  font-weight: 600 !important;
}

.content-container {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: #ffffff;
  overflow-y: auto;
}

.search-section {
  padding: 12px 16px;
  background-color: #ffffff;
  border-bottom: 1px solid #eee;
}

.button-section {
  padding: 12px 16px;
  background-color: #ffffff;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-row {
  display: flex;
  gap: 20px;
  align-items: flex-end;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.search-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1px;
}

.search-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
  min-width: 45px;
  line-height: 1.2;
  display: inline-block;
  vertical-align: middle;
}

.search-input {
  width: 120px;
  height: 28px;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  background-color: white !important;
  overflow: hidden;
}

:deep(.search-input .wd-input) {
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

:deep(.search-input .wd-input__inner) {
  padding: 6px 10px;
  font-size: 12px;
  height: 28px;
  box-sizing: border-box;
  border: none;
  background-color: transparent;
}

:deep(.search-input .wd-input:focus-within) {
  border-color: #4285f4;
}

:deep(.search-input .wd-input__inner:focus) {
  outline: none;
}



:deep(.search-select .wd-select-picker__label) {
  font-size: 12px !important;
  line-height: 1.2 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.left-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.right-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.merchant-list-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0 12px;
}

.merchant-list-card {
  background-color: transparent;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow-y: auto;
}

.merchant-list {
  background-color: transparent;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
  flex: 1;
}

.merchant-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s;
  min-width: 0;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f5f7fa;
  }
}

.merchant-avatar-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  margin-right: 16px;
  flex-shrink: 0;
}

.merchant-avatar {
  width: 58px;
  height: 58px;
}

.status-tag {
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  white-space: nowrap;
  text-align: center;
  min-width: 56px;

  &.status-normal {
    background-color: #e8f5e8;
    color: #4caf50;
  }

  &.status-temporary {
    background-color: #fff3e0;
    color: #ff9800;
  }

  &.status-permanent {
    background-color: #ffebee;
    color: #f44336;
  }
}

.merchant-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-right: 12px;
  min-width: 0;
  overflow: hidden;
}

.merchant-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  width: 100%;
  min-width: 0;
  position: relative;
}

.merchant-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1; /* 占用剩余空间 */
  min-width: 0; /* 允许收缩 */
}

.merchant-name {
  font-size: 15px;
  font-weight: bold;
  color: #333;
  flex: 1; /* 占用剩余空间 */
  min-width: 0; /* 允许收缩 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  /* 移除固定的max-width，改用flex自适应 */
}

.commission-tag {
  font-size: 11px;
  color: #f57c00;
  background-color: #fff3e0;
  padding: 2px 6px;
  border-radius: 10px;
}

.merchant-phone {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;

  .iconsys-dianhua {
    font-size: 14px;
    color: #666;
  }

  .phone-text {
    font-size: 13px;
    color: #666;
  }
}

.merchant-address {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;

  .address-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
  }
}

.merchant-revenue {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  align-items: center;

  .revenue-item {
    font-size: 12px;
    color: #1976d2;
    background-color: #e3f2fd;
    padding: 2px 6px;
    border-radius: 10px;
    white-space: nowrap;
  }
}

.merchant-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-left: 8px;
}

.merchant-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-tag {
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  white-space: nowrap;

  &.status-normal {
    background-color: #e8f5e8;
    color: #4caf50;
  }

  &.status-temporary {
    background-color: #fff3e0;
    color: #ff9800;
  }

  &.status-permanent {
    background-color: #ffebee;
    color: #f44336;
  }
}

.commission-tag {
  font-size: 11px;
  color: #f57c00;
  background-color: #fff3e0;
  padding: 2px 6px;
  border-radius: 10px;
}

.merchant-actions-wrapper {
  margin-left: 0;
  min-width: auto;
}

.merchant-actions {
  display: flex;
  gap: 6px;
  justify-content: center;
  align-items: center;
}

:deep(.action-btn) {
  width: 32px !important;
  height: 32px !important;
  padding: 0 !important;
  min-width: 32px !important;
  border-radius: 50% !important;
  background-color: #f5f5f5 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  &:hover {
    background-color: #eeeeee !important;
  }

  .iconfont-sys {
    color: #666666 !important;
    font-size: 16px !important;
    margin: 0 !important;
  }

  .wd-icon {
    color: #666666 !important;
    font-size: 16px !important;
    margin: 0 !important;
  }
}

.pagination-wrapper {
  padding: 12px 0;
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
  margin-top: 12px;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
}

.loading-text {
  color: #999;
  font-size: 14px;
  margin-top: 10px;
}

// ActionSheet 自定义样式
:deep(.wd-action-sheet__item) {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;

  .wd-action-sheet__name {
    flex: 1;
    font-size: 16px;
    font-weight: 500;
  }

  .wd-action-sheet__subname {
    font-size: 14px;
    opacity: 0.7;
    margin-left: 8px;
  }
}

// 特殊处理跟进相关选项
:deep(.wd-action-sheet__item:nth-child(2)),
:deep(.wd-action-sheet__item:nth-child(3)) {
  display: inline-flex;
  width: 48%;
  margin: 8px 1%;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background-color: #fafafa;

  .wd-action-sheet__name {
    font-size: 14px;
  }

  .wd-action-sheet__subname {
    font-size: 12px;
  }
}

// 新增的Popup自定义样式
:deep(.wd-popup) {
  background-color: #fff;
  border-radius: 16px 16px 0 0;
  padding: 0;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
}

:deep(.wd-popup__content) {
  padding: 0;
}

.merchant-popup-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;

  .popup-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
  }

  .popup-close {
    padding: 8px;
  }
}

.popup-actions {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;
  margin-bottom: 16px;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }

  &.primary {
    .action-icon {
      background-color: #e3f2fd;
      border-radius: 8px;
      padding: 8px;
    }

    .action-content {
      .action-name {
        color: #4285f4;
      }

      .action-desc {
        color: #999;
      }
    }
  }

  &.danger {
    .action-icon {
      background-color: #fffbe6;
      border-radius: 8px;
      padding: 8px;
    }

    .action-content {
      .action-name {
        color: #ff4d4f;
      }

      .action-desc {
        color: #ff9800;
      }
    }
  }
}

.action-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.action-content {
  flex: 1;
  margin-left: 16px;
}

.action-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.action-desc {
  font-size: 14px;
  color: #999;
}

.popup-cancel {
  padding: 16px 20px;
  text-align: center;
  border-top: 1px solid #eee;
}

.cancel-text {
  font-size: 16px;
  color: #666;
}

.popup-edit {
  padding: 16px 20px;
  text-align: center;
  border-top: 1px solid #eee;
  background-color: #4285f4;
}

.edit-text {
  font-size: 16px;
  color: white;
  font-weight: 500;
}

.follow-section {
  display: flex;
  gap: 12px;
  margin: 16px 0;
}

.follow-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 12px;
  background-color: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  transition: all 0.2s;

  &:active {
    background-color: #f0f0f0;
    transform: scale(0.98);
  }
}

.follow-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 50%;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.follow-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.follow-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.follow-desc {
  font-size: 12px;
  color: #666;
}

/* Collapse 自定义样式 */
:deep(.wd-collapse) {
  background-color: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

:deep(.wd-collapse-item) {
  background-color: #fff;
  border-bottom: 1px solid #eee;
  border-radius: 0;
  margin-bottom: 0;
}

:deep(.wd-collapse-item__header) {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f5f5f5;
  border-bottom: 1px solid #eee;
  border-radius: 8px 8px 0 0;
}

:deep(.wd-collapse-item__title) {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  flex: 1;
  margin-right: 10px;
}

:deep(.wd-collapse-item__arrow) {
  transition: transform 0.3s ease;
}

:deep(.wd-collapse-item--active .wd-collapse-item__arrow) {
  transform: rotate(180deg);
}

:deep(.wd-collapse-item__body) {
  padding: 0 20px 16px;
}

.status-section {
  margin-bottom: 16px;
}

.section-title {
  padding: 12px 0 8px 0;
  border-bottom: 1px solid #eee;
  margin-bottom: 12px;
}

.title-text {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.status-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding: 12px 0;
}

.status-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
  background-color: #f9f9f9;

  &.status-current {
    background-color: #e3f2fd;
    border-color: #4285f4;
    box-shadow: 0 2px 8px rgba(66, 133, 244, 0.2);
  }
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.status-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.status-current-tag {
  background-color: #4285f4;
  color: white;
  padding: 2px 6px;
  border-radius: 6px;
  font-size: 10px;
  font-weight: bold;
  margin-left: 8px;
}

.follow-info-section {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

.delete-section {
  padding-top: 16px;
}

.delete-option {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-radius: 12px;
  background-color: #fff5f5;
  border: 1px solid #ffcdd2;
  margin-top: 8px;
  transition: all 0.2s;

  &:active {
    background-color: #ffebee;
    transform: scale(0.98);
  }
}

.delete-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffebee;
  border-radius: 12px;
  margin-right: 16px;
  margin-left: 16px;
  flex-shrink: 0;
}

.delete-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.delete-text {
  font-size: 16px;
  color: #ff4d4f;
  font-weight: 600;
}

.delete-desc {
  font-size: 12px;
  color: #ff7875;
  opacity: 0.8;
}
</style>
