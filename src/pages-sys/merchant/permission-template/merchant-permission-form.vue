<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '权限模板表单',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="permission-form-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      :title="pageTitle"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <view class="main-layout">
        <view class="permission-form">
          <wd-form ref="formRef" :model="formData" :rules="formRules" errorType="message">
            <!-- 基本信息 -->
            <view class="form-section">
              <view class="section-title">基本信息</view>

              <wd-cell-group border>
                <!-- 权限名称 -->
                <wd-input
                  label="权限名称"
                  label-width="80px"
                  prop="label"
                  v-model="formData.label"
                  placeholder="请输入权限名称"
                  :readonly="isViewMode"
                  :maxlength="50"
                  show-word-limit
                  clearable
                  :rules="formRules.label"
                />

                <!-- 父级权限 -->
                <wd-picker
                  label="父级权限"
                  label-width="80px"
                  prop="parent_id"
                  v-model="formData.parent_id"
                  :columns="parentPermissionOptions"
                  value-key="id"
                  label-key="permission_name"
                  placeholder="请选择父级权限"
                  :disabled="isViewMode"
                  @confirm="onParentPermissionChange"
                />

                <!-- 权限类型 -->
                <wd-picker
                  label="权限类型"
                  label-width="80px"
                  prop="permission_type"
                  v-model="formData.permission_type"
                  :columns="permissionTypeOptions"
                  placeholder="请选择权限类型"
                  :disabled="isViewMode"
                  :rules="formRules.permission_type"
                  @confirm="onPermissionTypeChange"
                />

                <!-- 权限编码 -->
                <wd-input
                  label="权限编码"
                  label-width="80px"
                  prop="permission_code"
                  v-model="formData.permission_code"
                  placeholder="请输入权限编码"
                  :readonly="isViewMode"
                  clearable
                  :rules="formRules.permission_code"
                />

                <!-- 显示顺序 -->
                <wd-input
                  label="显示顺序"
                  label-width="80px"
                  prop="order_num"
                  v-model="formData.order_num"
                  type="number"
                  placeholder="请输入显示顺序"
                  :readonly="isViewMode"
                  clearable
                />

                <!-- 图标 -->
                <wd-input
                  label="图标"
                  label-width="80px"
                  prop="icon"
                  v-model="formData.icon"
                  placeholder="请输入图标名称"
                  :readonly="isViewMode"
                  clearable
                />
              </wd-cell-group>
            </view>

            <!-- 其他设置 -->
            <view class="form-section">
              <view class="section-title">其他设置</view>

              <wd-cell-group border>
                <!-- 是否显示 -->
                <wd-cell title="是否显示">
                  <wd-switch
                    v-model="visibleSwitch"
                    :disabled="isViewMode"
                    @change="onVisibleChange"
                  />
                </wd-cell>

                <!-- 备注 -->
                <wd-textarea
                  label="备注"
                  label-width="80px"
                  prop="remark"
                  v-model="formData.remark"
                  placeholder="请输入备注信息"
                  :readonly="isViewMode"
                  :maxlength="200"
                  show-word-limit
                  :rows="3"
                  auto-height
                />
              </wd-cell-group>
            </view>
          </wd-form>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="form-actions" v-if="!isViewMode">
      <wd-button @click="handleCancel" size="large" custom-class="cancel-btn">取消</wd-button>
      <wd-button
        type="primary"
        @click="handleSubmit"
        size="large"
        :loading="submitting"
        custom-class="submit-btn"
      >
        {{ isEditMode ? '更新' : '创建' }}
      </wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import {
  getSystemPermissionTemplateSelectItemsApi,
  getSystemPermissionTemplateDetailApi,
  createSystemPermissionTemplateApi,
  updateSystemPermissionTemplateApi,
} from '@/api/sys/systemPermissionTemplateApi'

defineOptions({
  name: 'MerchantPermissionForm',
})

// 页面参数
const pageParams = ref<{
  mode: 'create' | 'edit' | 'view'
  id?: string
  parent_id?: string
}>({
  mode: 'create',
})

// 页面模式
const isCreateMode = computed(() => pageParams.value.mode === 'create')
const isEditMode = computed(() => pageParams.value.mode === 'edit')
const isViewMode = computed(() => pageParams.value.mode === 'view')

// 页面标题
const pageTitle = computed(() => {
  if (isCreateMode.value) {
    return pageParams.value.parent_id ? '新增子权限' : '新增权限'
  }
  if (isEditMode.value) return '编辑权限'
  return '权限详情'
})

// 表单引用
const formRef = ref()
const submitting = ref(false)

// 表单数据
const formData = ref<{
  id?: string
  label: string
  parent_id: string
  parent_name?: string
  permission_type: number
  permission_code: string
  visible: number
  icon?: string
  order_num: number
  remark?: string
  is_frame: number
  is_cache: number
}>({
  label: '',
  parent_id: '',
  permission_type: 1,
  permission_code: '',
  visible: 0,
  icon: undefined,
  order_num: 0,
  remark: '',
  is_frame: 0, // 默认内嵌
  is_cache: 1, // 默认开启缓存
})

// 父级权限选项
const parentPermissionOptions = ref<
  Array<{
    id: string
    permission_name: string
    parent_id?: string | null
    permission_type: number | null
  }>
>([])

// 权限类型选项
const permissionTypeOptions = ref([
  { label: '菜单', value: 1 },
  { label: '页面', value: 2 },
  { label: '按钮', value: 3 },
])

// Switch开关状态
const visibleSwitch = ref(true) // 是否显示：true=显示，false=隐藏

// 表单验证规则
const formRules = ref<Record<string, any[]>>({
  label: [
    { required: true, message: '权限名称不能为空' },
    { min: 2, max: 50, message: '权限名称长度在2到50个字符', required: false },
  ],
  permission_type: [{ required: true, message: '请选择权限类型' }],
})

// 监听switch状态变化
watch(
  () => visibleSwitch.value,
  (val) => {
    formData.value.visible = val ? 0 : 1
  },
)

// 监听order_num变化，确保始终是数字类型
watch(
  () => formData.value.order_num,
  (val) => {
    if (typeof val === 'string') {
      const numVal = val === '' ? 0 : Number(val)
      if (!isNaN(numVal)) {
        formData.value.order_num = numVal
      }
    }
  },
)

// 父级权限变化
const onParentPermissionChange = ({ value }: { value: string }) => {
  console.log('父级权限变化:', value)
}

// 权限类型变化
const onPermissionTypeChange = ({ value }: { value: number }) => {
  console.log('权限类型变化:', value)
}

// Switch变化事件
const onVisibleChange = (value: boolean) => {
  formData.value.visible = value ? 0 : 1
}

// 加载父级权限选项
const loadParentPermissionOptions = async () => {
  try {
    uni.showLoading({ title: '加载父级权限...' })
    const response = await getSystemPermissionTemplateSelectItemsApi()

    // 添加"无上级权限"选项并过滤掉按钮类型的权限
    parentPermissionOptions.value = [
      { id: '', permission_name: '无上级权限', parent_id: null, permission_type: null },
      ...response.data.filter((item) => item.permission_type !== 3), // 过滤掉按钮类型的权限
    ]

    console.log('加载父级权限选项:', parentPermissionOptions.value)
  } catch (error) {
    console.error('加载父级权限失败:', error)
    uni.showToast({
      title: '加载父级权限失败',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

// 加载权限详情
const loadPermissionDetail = async () => {
  if (!pageParams.value.id) return

  try {
    uni.showLoading({ title: '加载权限详情...' })

    // 编辑模式和查看模式都需要加载父级权限选项
    if (isEditMode.value || isViewMode.value) {
      await loadParentPermissionOptions()
    }

    // 调用API获取权限详情
    const response = await getSystemPermissionTemplateDetailApi(pageParams.value.id)
    const detail = response.data

    // 设置表单数据
    formData.value = {
      id: detail.id,
      label: detail.permission_name,
      parent_id: detail.parent_id || '',
      permission_type: detail.permission_type,
      permission_code: detail.permission_code,
      visible: detail.visible,
      icon: detail.icon,
      order_num: detail.order_num,
      remark: detail.remark,
      is_frame: detail.is_frame,
      is_cache: detail.is_cache,
    }

    // 设置switch状态
    visibleSwitch.value = detail.visible === 0

    console.log('权限详情数据已设置:', formData.value)
  } catch (error) {
    console.error('加载权限详情失败:', error)
    uni.showToast({
      title: '加载权限详情失败',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    const { valid, errors } = await formRef.value.validate()

    if (!valid) {
      console.log('表单验证失败:', errors)
      uni.showToast({
        title: '请检查表单信息',
        icon: 'none',
      })
      return
    }

    submitting.value = true

    // 准备提交数据
    const submitData = {
      permission_name: formData.value.label,
      parent_id: formData.value.parent_id || null,
      permission_type: formData.value.permission_type,
      permission_code: formData.value.permission_code,
      visible: formData.value.visible,
      icon: formData.value.icon || 'home',
      order_num:
        typeof formData.value.order_num === 'string'
          ? formData.value.order_num === ''
            ? 0
            : Number(formData.value.order_num)
          : formData.value.order_num,
      remark: formData.value.remark || null,
      is_frame: formData.value.is_frame,
      is_cache: formData.value.is_cache,
    }

    console.log('提交数据:', submitData)

    // 调用相应的API
    let res
    if (isEditMode.value) {
      res = await updateSystemPermissionTemplateApi(pageParams.value.id!, submitData)
    } else {
      res = await createSystemPermissionTemplateApi(submitData)
    }

    if (res.code !== 200) {
      uni.showToast({
        title: res.message || (isEditMode.value ? '更新失败' : '创建失败'),
        icon: 'none',
      })
      return
    }

    uni.showToast({
      title: isEditMode.value ? '更新成功' : '创建成功',
      icon: 'success',
    })

    // 延迟返回，让用户看到成功提示
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)

  } catch (error) {
    console.error('提交失败:', error)
    uni.showToast({
      title: isEditMode.value ? '更新失败' : '创建失败',
      icon: 'none',
    })
  } finally {
    submitting.value = false
  }
}

// 取消操作
const handleCancel = () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消操作吗？未保存的内容将丢失。',
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack()
      }
    },
  })
}

// 返回功能
const handleBack = () => {
  if (isViewMode.value) {
    uni.navigateBack()
    return
  }
  handleCancel()
}

// 页面加载事件
onLoad((options) => {
  console.log('权限表单页面参数:', options)

  if (options) {
    pageParams.value.mode = (options.mode as 'create' | 'edit' | 'view') || 'create'
    pageParams.value.id = options.id
    pageParams.value.parent_id = options.parent_id
  }

  console.log('页面参数设置完成:', pageParams.value)

  // 如果是编辑或查看模式，加载详情
  if ((isEditMode.value || isViewMode.value) && pageParams.value.id) {
    loadPermissionDetail()
  }
})

onMounted(() => {
  console.log('权限表单页面挂载完成')

  // 只在新增模式时加载父级权限选项
  if (isCreateMode.value) {
    loadParentPermissionOptions()
  }

  // 如果是新增子权限，预设父级权限
  if (isCreateMode.value && pageParams.value.parent_id) {
    formData.value.parent_id = pageParams.value.parent_id
    console.log('设置父级权限ID:', pageParams.value.parent_id)
  }
})
</script>

<style lang="scss" scoped>
.permission-form-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding-bottom: 80px; /* 为固定按钮留出空间 */
}

.main-layout {
  padding: 8px;
}

.form-section {
  margin-bottom: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 15px 8px 15px;
  padding-bottom: 6px;
  border-bottom: 1px solid #eee;
}

.form-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 12px;
  padding: 12px;
  background-color: white;
  border-top: 1px solid #eee;
  z-index: 100;
  safe-area-inset-bottom: env(safe-area-inset-bottom);
}

:deep(.cancel-btn) {
  flex: 1;
  background-color: #f5f5f5 !important;
  color: #666 !important;
  border-color: #ddd !important;
}

:deep(.submit-btn) {
  flex: 1;
}

// Wot UI 表单项样式优化
:deep(.wd-cell-group) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.wd-cell) {
  background-color: white;
}

:deep(.wd-input) {
  background-color: transparent;
}

:deep(.wd-picker) {
  background-color: transparent;
}

:deep(.wd-textarea) {
  background-color: transparent;
}

// 查看模式下优化禁用组件的显示效果
:deep(.wd-picker.wd-picker--disabled) {
  opacity: 0.8 !important;
}

:deep(.wd-switch.wd-switch--disabled) {
  opacity: 0.8 !important;
}
</style>
