<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '权限模板管理',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="pts-wrapper">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      title="权限模板管理"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="pts-navbar"
    />

    <view class="pts-content">
      <view class="pts-main">
        <!-- 权限模板管理页面内容 -->
        <view class="pts-management">
          <!-- 操作栏 -->
          <view class="pts-toolbar">
            <view class="pts-toolbar-left">
              <wd-button type="success" size="small" @click="addPermission">新增权限</wd-button>
              <wd-button type="info" size="small" icon="refresh" @click="refreshData"></wd-button>
            </view>
          </view>

          <!-- 搜索栏 -->
          <view class="pts-search-bar">
            <view class="pts-search-input-wrapper">
              <wd-input
                v-model="searchKeyword"
                placeholder="请输入权限标题进行搜索..."
                clearable
                custom-class="pts-search-input"
                @input="handleSearchInput"
                @clear="handleSearchClear"
              />
            </view>
            <view class="pts-search-buttons">
              <wd-button type="primary" size="small" @click="handleSearch">搜索</wd-button>
              <wd-button type="default" size="small" @click="handleClearSearch">清空</wd-button>
            </view>
          </view>

          <!-- 权限模板树形结构 -->
          <view class="pts-tree">
            <merchant-permission-display
              v-if="!loading"
              :permission-tree="permissionTree"
              v-model="selectedPermissions"
              @edit-permission="handleEditPermission"
              @add-child-permission="handleAddChildPermission"
              @delete-permission="handleDeletePermission"
            />

            <!-- 加载状态 -->
            <view v-if="loading" class="pts-loading">
              <text class="pts-loading-text">加载中...</text>
            </view>

            <!-- 空状态 -->
            <view v-if="!loading && permissionTree.length === 0" class="pts-empty">
              <text class="pts-empty-text">暂无权限模板数据</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
// @ts-ignore
import MerchantPermissionDisplay from './merchant-permission-display.vue'
import {
  getSystemPermissionTemplateTreeApi,
  deleteSystemPermissionTemplateApi,
  batchDeleteSystemPermissionTemplatesApi,
} from '@/api/sys/systemPermissionTemplateApi'

defineOptions({
  name: 'PermissionTemplateManagement',
})

// 权限模板数据类型定义
interface PermissionTemplateNode {
  id: string
  label: string
  permission_code: string
  parent_id?: string | null
  permission_type: number
  visible: number
  icon?: string
  order_num: number
  path?: string
  component?: string
  description?: string
  children: PermissionTemplateNode[]
}

// 数据状态
const permissionTree = ref<PermissionTemplateNode[]>([])
const selectedPermissions = ref<string[]>([])
const loading = ref(false)
const searchKeyword = ref('')

// 深度克隆函数
const deepClone = (obj: any): any => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map((item) => deepClone(item))
  if (typeof obj === 'object') {
    const cloned: any = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }
}

// 递归搜索权限节点
const searchInPermissionTree = (
  nodes: PermissionTemplateNode[],
  keyword: string,
): PermissionTemplateNode[] => {
  const result: PermissionTemplateNode[] = []

  for (const node of nodes) {
    const clonedNode = deepClone(node)
    let shouldInclude = false

    // 检查当前节点是否匹配
    if (node.label.toLowerCase().includes(keyword.toLowerCase())) {
      shouldInclude = true
    }

    // 递归搜索子节点
    if (node.children && node.children.length > 0) {
      const filteredChildren = searchInPermissionTree(node.children, keyword)
      if (filteredChildren.length > 0) {
        clonedNode.children = filteredChildren
        shouldInclude = true
      } else {
        clonedNode.children = []
      }
    }

    if (shouldInclude) {
      result.push(clonedNode)
    }
  }

  return result
}

// 刷新数据
const refreshData = () => {
  console.log('刷新权限模板数据')
  searchKeyword.value = ''
  fetchPermissionTree()
}

// 新增权限
const addPermission = () => {
  console.log('新增权限模板')
  uni.navigateTo({
    url: '/pages-sys/merchant/permission-template/merchant-permission-form?mode=create',
  })
}

// 编辑权限
const handleEditPermission = (node: PermissionTemplateNode) => {
  console.log('编辑权限:', node)
  uni.navigateTo({
    url: `/pages-sys/merchant/permission-template/merchant-permission-form?mode=edit&id=${node.id}`,
  })
}

// 新增子权限
const handleAddChildPermission = (node: PermissionTemplateNode) => {
  console.log('新增子权限:', node)
  uni.navigateTo({
    url: `/pages-sys/merchant/permission-template/merchant-permission-form?mode=create&parent_id=${node.id}`,
  })
}

// 删除权限
const handleDeletePermission = async (node: PermissionTemplateNode) => {
  console.log('删除权限:', node)
  uni.showModal({
    title: '确认删除',
    content: `确定要删除权限 "${node.label}" 吗？删除后将无法恢复，请谨慎操作。`,
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '删除中...' })

          let response
          // 如果有子节点，使用批量删除
          if (node.children && node.children.length > 0) {
            const ids = [node.id, ...getAllChildrenIds(node)]
            response = await batchDeleteSystemPermissionTemplatesApi({ template_ids: ids })
          } else {
            response = await deleteSystemPermissionTemplateApi(node.id)
          }

          // 检查删除结果
          // if (response.code !== 200) {
          //   uni.showToast({
          //     title: response.message || '删除失败',
          //     icon: 'none',
          //   })
          //   return
          // }

          if (response.code !== 200) {
            uni.showToast({
              title: '删除成功',
              icon: 'success',
            })
          }

          // 刷新数据
          await fetchPermissionTree()
        } catch (error) {
          console.error('删除失败:', error)
          uni.showToast({
            title: '删除失败',
            icon: 'none',
          })
        } finally {
          uni.hideLoading()
        }
      }
    },
  })
}

// 获取所有子节点ID
const getAllChildrenIds = (node: PermissionTemplateNode): string[] => {
  const ids: string[] = []
  if (node.children && node.children.length > 0) {
    node.children.forEach((child) => {
      ids.push(child.id)
      ids.push(...getAllChildrenIds(child))
    })
  }
  return ids
}

// 返回功能
const handleBack = () => {
  console.log('返回功能')
  uni.navigateBack()
}

// 搜索功能
const handleSearchInput = (value: string) => {
  console.log('搜索输入:', value)
}

const handleSearchClear = () => {
  console.log('搜索清空')
  searchKeyword.value = ''
  permissionTree.value = [] // 重置为原始数据
  fetchPermissionTree()
}

const handleSearch = () => {
  console.log('执行搜索:', searchKeyword.value)
  if (!searchKeyword.value.trim()) {
    fetchPermissionTree()
    return
  }
  const filtered = searchInPermissionTree(permissionTree.value, searchKeyword.value.trim())
  permissionTree.value = filtered
}

const handleClearSearch = () => {
  console.log('清空搜索')
  searchKeyword.value = ''
  fetchPermissionTree()
}

// 加载权限树数据
const fetchPermissionTree = async () => {
  loading.value = true
  try {
    const response = await getSystemPermissionTemplateTreeApi()
    if (response.code === 200 && response.data) {
      permissionTree.value = response.data
      console.log('获取权限模板树形数据成功:', response.data)
    } else {
      throw new Error(response.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取权限模板数据失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

onShow(() => {
  console.log('权限模板管理页面显示')
  fetchPermissionTree()
})
</script>

<style lang="scss" scoped>
.pts-wrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
}

// 自定义导航栏样式
:deep(.pts-navbar) {
  background-color: #4285f4 !important;
}

// 标题文字设为白色
:deep(.pts-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

// 自定义返回按钮样式
:deep(.pts-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.pts-content {
  flex: 1;
  background-color: transparent;
  overflow: hidden;
  position: relative;
  height: calc(100vh - var(--window-top));
}

.pts-main {
  height: 100%;
  background-color: white;
  overflow-y: auto;
  padding: 20px;
  -webkit-overflow-scrolling: touch; // 增加 iOS 滚动优化
}

.pts-management {
  height: auto;
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

.pts-toolbar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 0 15px 0;
  border-bottom: 1px solid #eee;
}

.pts-toolbar-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.pts-search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.pts-search-input-wrapper {
  flex: 1;
}

.pts-search-input {
  width: 100%;
}

.pts-search-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.pts-tree {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 20px; // 增加底部间距
}

// 权限项样式
.pts-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  width: 100%;
}

.pts-node-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.pts-node-icon {
  color: #666;
}

.pts-node-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.pts-node-badge {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  white-space: nowrap;

  &.type-menu {
    background-color: #e3f2fd;
    color: #1976d2;
  }

  &.type-page {
    background-color: #f3e5f5;
    color: #7b1fa2;
  }

  &.type-button {
    background-color: #e8f5e8;
    color: #388e3c;
  }
}

.pts-node-actions {
  display: flex;
  gap: 4px;
  align-items: center;
  margin-left: auto;
  padding-left: 12px;
}

.pts-children {
  padding-left: 20px;
  border-left: 2px solid #f0f0f0;
  margin-top: 8px;
}

// 不同层级的样式
:deep(.pts-node-lv0) {
  .wd-collapse-item__header {
    background-color: #fafafa;
    font-weight: 600;
  }
}

:deep(.pts-node-lv1) {
  .wd-collapse-item__header {
    background-color: #f5f5f5;
    font-weight: 500;
    margin-top: 4px;
  }
}

.pts-node-lv2 {
  padding: 10px 4px 10px 5px;
  background-color: #fbfbfb;
  border-bottom: 1px solid #f0f0f0;
  margin-top: 4px;

  .pts-node-title {
    font-size: 13px;
    font-weight: normal;
  }

  .pts-node-info {
    flex: 1;
    min-width: 0;
    overflow: hidden;
  }

  .pts-node-actions {
    margin-left: 4px !important;
    padding-left: 0 !important;
    flex-shrink: 0;
  }
}

// 第三层按钮特殊样式
.pts-node-lv3 {
  position: relative;
  right: 8px !important;
  margin-left: 0 !important;
  padding-left: 0 !important;
  max-width: 100px !important;
  flex-shrink: 0 !important;
}

// 折叠面板自定义样式
:deep(.wd-collapse-item) {
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e8e8e8;
}

:deep(.wd-collapse-item__header) {
  padding: 12px 16px;
  background-color: #f8f9fa;
}

:deep(.wd-collapse-item__body) {
  background-color: white;
}

.pts-empty {
  text-align: center;
  padding: 60px 20px;
}

.pts-empty-text {
  color: #999;
  font-size: 14px;
}

.pts-loading {
  text-align: center;
  padding: 40px 20px;
}

.pts-loading-text {
  color: #666;
  font-size: 14px;
}
</style>
