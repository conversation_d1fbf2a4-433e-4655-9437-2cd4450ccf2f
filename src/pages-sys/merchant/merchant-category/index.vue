<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '商户分类管理',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="merchant-category-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      title="商户分类管理"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <view class="main-layout">
        <!-- 商户分类管理页面内容 -->
        <view class="merchant-category-management">
          <!-- 操作栏 -->
          <view class="action-bar">
            <!-- 搜索条件栏 -->
            <view class="search-bar">
              <view class="search-form">
                <!-- 第一行搜索条件 -->
                <view class="search-row">
                  <view class="search-item">
                    <text class="search-label">分类名称：</text>
                    <wd-input
                      class="search-input-component"
                      placeholder="请输入分类名称"
                      v-model="searchParams.category_name"
                      clearable
                      @clear="handleSearch"
                    />
                  </view>
                  <view class="search-item">
                    <text class="search-label">分类编码：</text>
                    <wd-input
                      class="search-input-component"
                      placeholder="请输入分类编码"
                      v-model="searchParams.category_code"
                      clearable
                      @clear="handleSearch"
                    />
                  </view>
                </view>

                <!-- 第二行搜索条件 -->
                <view class="search-row">
                  <view class="search-item">
                    <text class="search-label">状态：</text>
                    <wd-select-picker
                      class="search-select-picker"
                      v-model="searchParams.status"
                      :columns="statusOptions"
                      type="radio"
                      :show-confirm="false"
                      @change="onStatusChange"
                    />
                  </view>
                </view>
                <!-- 按钮行 -->
                <view class="button-row">
                  <view class="left-buttons">
                    <wd-button type="success" size="small" @click="addCategory">新增</wd-button>
                    <wd-button
                      type="info"
                      size="small"
                      icon="refresh"
                      @click="refreshData"
                    ></wd-button>
                  </view>
                  <view class="right-buttons">
                    <wd-button type="primary" size="small" @click="handleSearch">搜索</wd-button>
                    <wd-button plain size="small" @click="resetSearch">重置</wd-button>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 商户分类列表 -->
          <view class="merchant-category-list">
            <!-- 表格组件 -->
            <wd-table
              :data="categoryList"
              :height="500"
              :stripe="true"
              :border="true"
              :row-key="(row) => row.id"
              :loading="loading"
            >
              <!-- 分类名称列 -->
              <wd-table-col
                prop="category_name"
                label="分类名称"
                :width="90"
                align="left"
              ></wd-table-col>

              <!-- 描述列 -->
              <wd-table-col prop="description" label="描述" :width="150" align="left">
                <template #value="{ row }">
                  <text class="description-text">{{ row.description || '-' }}</text>
                </template>
              </wd-table-col>

              <!-- 状态列 -->
              <wd-table-col prop="status" label="状态" :width="70" align="center">
                <template #value="{ row }">
                  <text class="status-tag" :class="getStatusClass(row.status)">
                    {{ getStatusDesc(row.status) }}
                  </text>
                </template>
              </wd-table-col>

              <!-- 操作列 -->
              <wd-table-col prop="actions" label="操作" :width="60" align="center">
                <template #value="{ row }">
                  <view class="table-actions">
                    <wd-button
                      type="icon"
                      custom-class="more-btn"
                      @click.stop="showMoreActions(row)"
                    >
                      <text class="iconfont-sys iconsys-gengduo"></text>
                    </wd-button>
                  </view>
                </template>
              </wd-table-col>
            </wd-table>

            <!-- 空状态 -->
            <view v-if="categoryList.length === 0 && !loading" class="empty-state">
              <wd-status-tip image="search" tip="当前搜索无结果" />
            </view>

            <!-- 分页组件 -->
            <view v-if="totalCount > 0" class="pagination-wrapper">
              <wd-pagination
                v-model="currentPage"
                :total="totalCount"
                :page-size="pageSize"
                @change="handlePageChange"
                show-icon
                show-message
              />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作弹出层 -->
    <wd-popup
      v-model="showActionPopup"
      position="bottom"
      :safe-area-inset-bottom="true"
      custom-style="border-radius: 16px 16px 0 0; padding: 0;"
      @close="closeActionPopup"
    >
      <view class="action-popup-content">
        <!-- 标题栏 -->
        <view class="popup-header">
          <text class="popup-title">分类操作</text>
          <view class="popup-close" @click="closeActionPopup">
            <wd-icon name="close" size="20" color="#999" />
          </view>
        </view>

        <!-- 操作按钮列表 -->
        <view class="popup-actions">
          <view class="action-grid">
            <view class="action-item" @click="editCurrentCategory">
              <text class="action-text">编辑分类</text>
            </view>

            <view class="action-item" @click="toggleCurrentCategoryStatus">
              <text class="action-text">
                {{ currentCategory ? getStatusActionText(currentCategory.status) : '切换状态' }}
              </text>
            </view>

            <view class="action-item delete-item" @click="deleteCurrentCategory">
              <text class="action-text delete-text">删除分类</text>
            </view>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  changeSystemMerchantCategoryStatusApi,
  getSystemMerchantCategoriesApi,
  deleteSystemMerchantCategoryApi,
} from '@/api/sys/systemMerchantCategoryApi'
import type {
  ISysMerchantCategoryListResponse,
  ISysMerchantCategoryPageRequest,
} from '@/api/sys/types/merchantCategory'

defineOptions({
  name: 'MerchantCategoryManagement',
})

// 分页数据类型
interface PageData {
  items: ISysMerchantCategoryListResponse[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// 搜索参数
const searchParams = ref<ISysMerchantCategoryPageRequest>({
  category_name: '',
  category_code: '',
  status: undefined,
  created_start: '',
  created_end: '',
  page: 1,
  page_size: 10,
})

// 状态选项 - 1启用 2禁用
const statusOptions = ref([
  { label: '全部状态', value: '' },
  { label: '启用', value: 1 },
  { label: '禁用', value: 2 },
])

// 数据状态
const categoryList = ref<ISysMerchantCategoryListResponse[]>([])
const totalCount = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const loading = ref(false)

// 弹出层相关状态
const showActionPopup = ref(false)
const currentCategory = ref<ISysMerchantCategoryListResponse | null>(null)

// 获取状态样式类
const getStatusClass = (status: number) => {
  switch (status) {
    case 1:
      return 'status-enabled'
    case 2:
      return 'status-disabled'
    default:
      return ''
  }
}

// 获取状态操作文本
const getStatusActionText = (status: number) => {
  switch (status) {
    case 1:
      return '禁用'
    case 2:
      return '启用'
    default:
      return '操作'
  }
}

// 获取状态显示文本
const getStatusDesc = (status: number) => {
  switch (status) {
    case 1:
      return '启用'
    case 2:
      return '禁用'
    default:
      return '-'
  }
}

// 获取商户分类数据（真实API）
const fetchCategories = async () => {
  loading.value = true
  try {
    const iResData = await getSystemMerchantCategoriesApi(searchParams.value)
    const pageData: PageData = iResData.data
    console.log('获取到的分类数据:', pageData)
    categoryList.value = pageData.items
    totalCount.value = pageData.total
    // 记录当前页
    currentPage.value = pageData.page
    pageSize.value = pageData.page_size
    console.log('处理后的分类列表:', categoryList.value)
  } catch (error) {
    console.error('获取商户分类数据失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = async () => {
  // console.log('搜索商户分类:', searchParams.value)
  currentPage.value = 1
  searchParams.value.page = 1
  await fetchCategories()
}

// 状态选择器变化事件
const onStatusChange = ({ value }: { value: number | string }) => {
  // console.log('状态变化:', value)
  searchParams.value.status = value as any
}

// 重置搜索
const resetSearch = () => {
  searchParams.value = {
    category_name: '',
    category_code: '',
    status: undefined,
    created_start: '',
    created_end: '',
    page: 1,
    page_size: 10,
  }
  currentPage.value = 1
  fetchCategories()
}

// 处理分页变化
const handlePageChange = ({ value }: { value: number }) => {
  // console.log('切换到第', value, '页')
  currentPage.value = value
  searchParams.value.page = value
  fetchCategories()
}

// 刷新数据
const refreshData = () => {
  console.log('刷新商户分类数据')
  fetchCategories()
}

// 新增分类
const addCategory = () => {
  // console.log('新增商户分类')
  uni.navigateTo({
    url: '/pages-sys/merchant/merchant-category/merchant-category-form?mode=add'
  })
}

// 编辑分类
const editCategory = (category: ISysMerchantCategoryListResponse) => {
  // console.log('编辑商户分类:', category)
  uni.navigateTo({
    url: `/pages-sys/merchant/merchant-category/merchant-category-form?mode=edit&categoryId=${category.id}`
  })
}

// 切换分类状态
const toggleCategoryStatus = async (category: ISysMerchantCategoryListResponse) => {
  // console.log('切换分类状态:', category)
  const actionText = getStatusActionText(category.status)
  uni.showModal({
    title: '确认操作',
    content: `确定要${actionText}分类 "${category.category_name}" 吗？`,
    success: async (res) => {
      if (res.confirm) {
        // 模拟状态切换（如需真实API，需调用changeSystemMerchantCategoryStatusApi）
        const newStatus = category.status === 1 ? 2 : 1
        const index = categoryList.value.findIndex((c) => c.id === category.id)
        if (index > -1) {
          await changeCategoryStatus(category.id, newStatus)
          uni.showToast({
            title: `${actionText}成功`,
            icon: 'success',
          })
        }
      }
      refreshData()
    },
  })
}

const changeCategoryStatus = async (catgory_id: string, status: number) => {
  await changeSystemMerchantCategoryStatusApi(catgory_id, { status })
}

// 删除分类
const deleteCategory = (category: ISysMerchantCategoryListResponse) => {
  // console.log('删除商户分类:', category)
  uni.showModal({
    title: '确认删除',
    content: `确定要删除分类 "${category.category_name}" 吗？删除后将无法恢复，请谨慎操作。`,
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '删除中...' })
          await deleteSystemMerchantCategoryApi(category.id)
          uni.showToast({
            title: '删除成功',
            icon: 'success',
          })
          // 刷新列表数据
          await fetchCategories()
        } catch (error) {
          console.error('删除失败:', error)
          uni.showToast({
            title: '删除失败',
            icon: 'error',
          })
        } finally {
          uni.hideLoading()
        }
      }
    },
  })
}
import { onShow } from '@dcloudio/uni-app'

// 返回功能
const handleBack = () => {
  console.log('返回功能')
  uni.navigateBack()
}
onShow(() => {
  console.log('商户分类管理页面显示')
  fetchCategories()
})

// 显示更多操作
const showMoreActions = (category: ISysMerchantCategoryListResponse) => {
  currentCategory.value = category
  showActionPopup.value = true
}

// 关闭操作弹出层
const closeActionPopup = () => {
  showActionPopup.value = false
  currentCategory.value = null
}

// 编辑当前分类
const editCurrentCategory = () => {
  if (currentCategory.value) {
    editCategory(currentCategory.value)
    closeActionPopup()
  }
}

// 切换当前分类状态
const toggleCurrentCategoryStatus = () => {
  if (currentCategory.value) {
    toggleCategoryStatus(currentCategory.value)
    closeActionPopup()
  }
}

// 删除当前分类
const deleteCurrentCategory = () => {
  if (currentCategory.value) {
    deleteCategory(currentCategory.value)
    closeActionPopup()
  }
}
</script>

<style lang="scss" scoped>
.merchant-category-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

// 标题文字设为白色
:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

// 自定义返回按钮样式
:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  background-color: transparent;
  overflow: hidden;
  position: relative;
}

.main-layout {
  flex: 1;
  background-color: white;
  overflow-y: auto;
  padding: 20px;
}

.merchant-category-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.action-bar {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
  padding: 0 0 15px 0;
  border-bottom: 1px solid #eee;
}

.search-bar {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
}

.search-row {
  display: flex;
  gap: 20px;
  align-items: flex-end;
}

.search-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1px;
}

.search-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
  min-width: 60px;
}

.search-input-component {
  width: 120px;
  height: 28px;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  background-color: white !important;
  overflow: hidden;
}

:deep(.search-input-component .wd-input) {
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

:deep(.search-input-component .wd-input__inner) {
  padding: 6px 10px;
  font-size: 12px;
  height: 28px;
  box-sizing: border-box;
  border: none;
  background-color: transparent;
}

:deep(.search-input-component .wd-input:focus-within) {
  border-color: #4285f4;
}

:deep(.search-input-component .wd-input__inner:focus) {
  outline: none;
}


.button-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.right-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.merchant-category-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

// 表格样式
:deep(.wd-table) {
  flex: 1;
  overflow: auto;
  border-radius: 8px;
  border: 1px solid #eee;
}

:deep(.wd-table__header) {
  background-color: #f8f9fa;
  font-weight: 600;
}

:deep(.wd-table__body) {
  background-color: #fff;
}

:deep(.wd-table__row:hover) {
  background-color: #f5f7fa;
}

.table-actions {
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.more-btn) {
  width: 32px !important;
  height: 32px !important;
  padding: 0 !important;
  min-width: 32px !important;
  border-radius: 50% !important;
  background-color: #f5f5f5 !important;
  border: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  &:hover {
    background-color: #eeeeee !important;
  }

  .iconfont-sys {
    color: #666666 !important;
    font-size: 14px !important;
    margin: 0 !important;
  }
}

.pagination-wrapper {
  padding: 16px 0;
  background-color: transparent;
}

// 描述文本
.description-text {
  font-size: 14px;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px;
  display: inline-block;
}

// 状态标签
.status-tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  white-space: nowrap;
  font-weight: 500;

  &.status-enabled {
    background-color: #e8f5e8;
    color: #4caf50;
  }

  &.status-disabled {
    background-color: #ffebee;
    color: #f44336;
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background-color: transparent;
}

// 弹出层样式
.action-popup-content {
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;

  .popup-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
  }

  .popup-close {
    padding: 8px;
    cursor: pointer;
  }
}

.popup-actions {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow-y: auto;
  background-color: white;
}

.action-grid {
  display: flex;
  flex-wrap: nowrap;
  gap: 8px;
  justify-content: space-between;
}

.action-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 8px;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
  background-color: #f9f9f9;
  flex: 1;
  min-width: 60px;
  transition: background-color 0.2s;

  &:active {
    background-color: #f0f0f0;
  }

  &:hover {
    background-color: #f0f0f0;
  }
}

.action-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  text-align: center;
}

// 删除操作样式
.delete-item {
  &.action-item {
    border-color: #e0e0e0;
    background-color: #f9f9f9;

    &:active {
      background-color: #f0f0f0;
    }

    &:hover {
      background-color: #fff5f5;
    }
  }
}

.delete-text {
  color: #f44336;
  font-weight: 500;
}
</style>
