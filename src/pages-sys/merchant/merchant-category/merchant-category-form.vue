<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '商户分类表单',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="category-form-container">
    <!-- 导航栏 -->
    <wd-navbar
      :title="isEditMode ? '编辑商户分类' : '新增商户分类'"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <view class="main-layout">
        <view class="category-form">
          <wd-form ref="formRef" :model="formData" :rules="formRules" errorType="message">
            <!-- 基本信息 -->
            <view class="form-section">
              <view class="section-title">基本信息</view>
              <wd-cell-group border>
                <!-- 分类名称 -->
                <wd-input
                  label="分类名称"
                  label-width="100px"
                  prop="category_name"
                  v-model="formData.category_name"
                  placeholder="请输入分类名称"
                  :maxlength="50"
                  show-word-limit
                  clearable
                />

                <!-- 分类编码 -->
                <wd-input
                  label="分类编码"
                  label-width="100px"
                  prop="category_code"
                  v-model="formData.category_code"
                  placeholder="请输入分类编码"
                  :maxlength="20"
                  show-word-limit
                  clearable
                />

                <!-- 分类描述 -->
                <wd-textarea
                  label="分类描述"
                  label-width="100px"
                  prop="description"
                  v-model="formData.description"
                  placeholder="请输入分类描述"
                  :maxlength="200"
                  show-word-limit
                  :rows="3"
                  clearable
                  auto-height
                />
              </wd-cell-group>
            </view>

            <!-- 设置信息 -->
            <view class="form-section">
              <view class="section-title">设置信息</view>
              <wd-cell-group border>
                <!-- 状态 -->
                <wd-picker
                  label="状态"
                  label-width="100px"
                  prop="status"
                  v-model="formData.status"
                  :columns="statusOptions"
                  placeholder="请选择状态"
                  @confirm="onStatusChange"
                />

                <!-- 排序权重 -->
                <wd-cell title="排序权重" label-width="100px">
                  <view class="sort-order-container">
                    <wd-input-number
                      v-model="formData.sort_order"
                      placeholder="请输入排序权重"
                      :min="0"
                      :max="9999"
                      :step="1"
                      :precision="0"
                      style="width: 120px"
                    />
                  </view>
                </wd-cell>
              </wd-cell-group>
            </view>

            <!-- 其他信息 -->
            <view class="form-section">
              <view class="section-title">其他信息</view>
              <wd-cell-group border>
                <!-- 备注 -->
                <wd-textarea
                  label="备注"
                  label-width="100px"
                  prop="remark"
                  v-model="formData.remark"
                  placeholder="请输入备注信息"
                  :maxlength="200"
                  show-word-limit
                  :rows="3"
                  auto-height
                />
              </wd-cell-group>
            </view>
          </wd-form>

          <!-- 操作按钮 -->
          <view class="form-actions">
            <wd-button @click="handleCancel" size="large" custom-class="cancel-btn">取消</wd-button>
            <wd-button
              type="primary"
              @click="handleSubmit"
              size="large"
              :loading="submitting"
              custom-class="submit-btn"
            >
              {{ isEditMode ? '更新' : '创建' }}
            </wd-button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import {
  createSystemMerchantCategoryApi,
  updateSystemMerchantCategoryApi,
  getSystemMerchantCategoryDetailApi,
} from '@/api/sys/systemMerchantCategoryApi'
import type {
  ISysMerchantCategoryCreateRequest,
  ISysMerchantCategoryUpdateRequest,
  ISysMerchantCategoryDetailResponse,
} from '@/api/sys/types/merchantCategory'

defineOptions({
  name: 'MerchantCategoryForm',
})

// 页面参数
const pageParams = ref<{
  mode: 'add' | 'edit'
  categoryId?: string
}>({
  mode: 'add',
})

// 计算属性
const isEditMode = computed(() => pageParams.value.mode === 'edit')

// 表单引用
const formRef = ref()

// 提交状态
const submitting = ref(false)

// 表单数据
const formData = reactive<ISysMerchantCategoryCreateRequest>({
  category_name: '',
  category_code: '',
  description: '',
  sort_order: 0,
  status: 1, // 默认启用
  remark: '',
})

// 状态选项
const statusOptions = ref([
  { label: '启用', value: 1 },
  { label: '禁用', value: 2 },
])

// 表单验证规则
const formRules = {
  category_name: [
    { required: true, message: '请输入分类名称' },
    { required: true, min: 2, max: 50, message: '分类名称长度应在2-50个字符之间' },
  ],
  category_code: [
    {
      required: true,
      pattern: /^[A-Z0-9_]{2,20}$/,
      message: '分类编码只能包含大写字母、数字、下划线，长度2-20位',
    },
  ],
  status: [{ required: true, message: '请选择状态' }],
}

// 状态选择事件
const onStatusChange = ({ value }: { value: number }) => {
  formData.status = value
}

// 加载商户分类详情
const loadCategoryDetail = async (categoryId: string) => {
  try {
    uni.showLoading({ title: '加载中...' })

    const result = await getSystemMerchantCategoryDetailApi(categoryId)
    const categoryDetail = result.data

    // 填充表单数据
    Object.assign(formData, {
      category_name: categoryDetail.category_name,
      category_code: categoryDetail.category_code || '',
      description: categoryDetail.description || '',
      sort_order: categoryDetail.sort_order || 0,
      status: categoryDetail.status,
      remark: categoryDetail.remark || '',
    })

    console.log('商户分类详情加载成功:', categoryDetail)
  } catch (error) {
    console.error('加载商户分类详情失败:', error)
    uni.showToast({
      title: '加载分类详情失败',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 验证表单
    const { valid, errors } = await formRef.value.validate()

    console.log('表单验证结果:', { valid, errors })

    if (!valid) {
      console.log('表单验证失败:', errors)
      uni.showToast({
        title: '请检查表单信息',
        icon: 'none',
      })
      return
    }

    console.log('表单验证通过，开始提交数据')
    submitting.value = true

    if (isEditMode.value) {
      // 编辑模式
      const updateData: ISysMerchantCategoryUpdateRequest = {
        category_name: formData.category_name,
        category_code: formData.category_code || null,
        description: formData.description || null,
        sort_order: formData.sort_order || null,
        status: formData.status,
        remark: formData.remark || null,
      }

      console.log('编辑提交数据:', updateData)
      await updateSystemMerchantCategoryApi(pageParams.value.categoryId!, updateData)

      uni.showToast({
        title: '更新成功',
        icon: 'success',
      })
    } else {
      // 新增模式
      const createData: ISysMerchantCategoryCreateRequest = {
        category_name: formData.category_name,
        category_code: formData.category_code || null,
        description: formData.description || null,
        sort_order: formData.sort_order || null,
        status: formData.status,
        remark: formData.remark || null,
      }

      console.log('新增提交数据:', createData)
      await createSystemMerchantCategoryApi(createData)

      uni.showToast({
        title: '创建成功',
        icon: 'success',
      })
    }

    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1000)
  } catch (error) {
    console.error('操作失败:', error)
    uni.showToast({
      title: isEditMode.value ? '更新失败' : '创建失败',
      icon: 'none',
    })
  } finally {
    submitting.value = false
  }
}

// 取消操作
const handleCancel = () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消操作吗？未保存的内容将丢失。',
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack()
      }
    },
  })
}

// 返回功能
const handleBack = () => {
  handleCancel()
}

// 页面加载事件
onLoad((options) => {
  console.log('商户分类表单页面参数:', options)

  if (options) {
    pageParams.value.mode = (options.mode as 'add' | 'edit') || 'add'
    pageParams.value.categoryId = options.categoryId
  }

  // 如果是编辑模式，加载分类详情
  if (isEditMode.value && pageParams.value.categoryId) {
    loadCategoryDetail(pageParams.value.categoryId)
  }
})

onMounted(() => {
  console.log('商户分类表单页面挂载完成')
  console.log('页面模式:', pageParams.value.mode)
  console.log('分类ID:', pageParams.value.categoryId)
})
</script>

<style lang="scss" scoped>
.category-form-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  overflow-y: auto;
  background-color: #f5f5f5;
}

.main-layout {
  padding: 12px;
}

.category-form {
  // 表单容器样式
}

.form-section {
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 15px 12px 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.form-actions {
  display: flex;
  gap: 12px;
  padding: 12px;
}

:deep(.cancel-btn) {
  flex: 1;
  background-color: #f5f5f5 !important;
  color: #666 !important;
  border-color: #ddd !important;
}

:deep(.submit-btn) {
  flex: 1;
}

// 排序权重容器样式
.sort-order-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

// Wot UI 表单项样式优化
:deep(.wd-cell-group) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.wd-cell) {
  background-color: white;
}

:deep(.wd-input) {
  background-color: transparent;
}

:deep(.wd-picker) {
  background-color: transparent;
}

:deep(.wd-textarea) {
  background-color: transparent;
}
</style>
