<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '商户用户表单',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="merchant-user-form-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      :title="getPageTitle()"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <view class="main-layout">
        <view class="merchant-user-form">
          <wd-form ref="formRef" :model="userForm" :rules="formRules" errorType="message">
            <!-- 基本信息 -->
            <view class="form-section">
              <view class="section-title">基本信息</view>

              <!-- 头像上传 -->
              <view class="avatar-section">
                <text class="avatar-label">头像</text>
                <view class="avatar-upload">
                  <view class="avatar-container" @click="chooseAvatar" v-if="!isViewMode">
                    <image
                      v-if="userForm.avatar"
                      :src="userForm.avatar"
                      class="avatar-image"
                      mode="aspectFill"
                    />
                    <view v-else class="avatar-placeholder">
                      <wd-icon name="camera" size="24" color="#999" />
                      <text class="placeholder-text">点击上传头像</text>
                    </view>
                  </view>
                  <!-- 查看模式的头像显示 -->
                  <view v-else class="avatar-view">
                    <image
                      v-if="userForm.avatar"
                      :src="userForm.avatar"
                      class="avatar-image"
                      mode="aspectFill"
                    />
                    <view v-else class="avatar-placeholder">
                      <wd-icon name="user-circle" size="32" color="#ccc" />
                    </view>
                  </view>
                </view>
              </view>

              <wd-cell-group border>
                <!-- 用户名 -->
                <wd-input
                  label="用户名"
                  label-width="80px"
                  prop="username"
                  v-model="userForm.username"
                  placeholder="请输入用户名"
                  :disabled="isEditMode || isViewMode"
                  :readonly="isViewMode"
                  :maxlength="20"
                  show-word-limit
                  clearable
                  :rules="formRules.username"
                  @blur="handleUsernameBlur"
                />

                <!-- 密码 - 仅新增时显示 -->
                <wd-input
                  v-if="isAddMode"
                  label="密码"
                  label-width="80px"
                  prop="password"
                  v-model="userForm.password"
                  show-password
                  placeholder="请输入密码"
                  :maxlength="20"
                  show-word-limit
                  clearable
                  :rules="formRules.password"
                />

                <!-- 真实姓名 -->
                <wd-input
                  label="真实姓名"
                  label-width="80px"
                  prop="real_name"
                  v-model="userForm.real_name"
                  placeholder="请输入真实姓名"
                  :readonly="isViewMode"
                  :maxlength="10"
                  show-word-limit
                  clearable
                  :rules="formRules.real_name"
                  @blur="handleRealNameBlur"
                />

                <!-- 性别 -->
                <wd-picker
                  label="性别"
                  label-width="80px"
                  prop="gender"
                  v-model="userForm.gender"
                  :columns="genderOptions"
                  placeholder="请选择性别"
                  :disabled="isViewMode"
                  @confirm="onGenderChange"
                />
              </wd-cell-group>
            </view>

            <!-- 联系信息 -->
            <view class="form-section">
              <view class="section-title">联系信息</view>

              <wd-cell-group border>
                <!-- 手机号 -->
                <wd-input
                  label="手机号"
                  label-width="80px"
                  prop="phone"
                  v-model="userForm.phone"
                  placeholder="请输入手机号"
                  :readonly="isViewMode"
                  :maxlength="11"
                  clearable
                  :rules="formRules.phone"
                  @blur="handlePhoneBlur"
                />

                <!-- 邮箱 -->
                <wd-input
                  label="邮箱"
                  label-width="80px"
                  prop="email"
                  v-model="userForm.email"
                  placeholder="请输入邮箱"
                  :readonly="isViewMode"
                  clearable
                  :rules="formRules.email"
                  @blur="handleEmailBlur"
                />

                <!-- 身份证号 -->
                <wd-input
                  v-if="isAddMode || isViewMode"
                  label="身份证号"
                  label-width="80px"
                  prop="id_card"
                  v-model="userForm.id_card"
                  placeholder="请输入身份证号"
                  :readonly="isViewMode"
                  :maxlength="18"
                  clearable
                  :rules="formRules.id_card"
                  @blur="handleIdCardBlur"
                />

                <!-- 身份证提示信息 - 仅新增模式显示 -->
                <view v-if="isAddMode" class="id-card-tip">
                  <text class="tip-text">💡 身份证添加后不允许修改！</text>
                </view>
              </wd-cell-group>
            </view>

            <!-- 账户设置 -->
            <view class="form-section">
              <view class="section-title">账户设置</view>

              <wd-cell-group border>
                <!-- 状态 -->
                <wd-picker
                  label="状态"
                  label-width="80px"
                  prop="status"
                  v-model="userForm.status"
                  :columns="statusOptions"
                  placeholder="请选择状态"
                  :disabled="isViewMode"
                  :rules="formRules.status"
                  @confirm="onStatusChange"
                />

                <!-- 角色选择 -->
                <wd-picker
                  v-if="!isViewMode"
                  label="用户角色"
                  label-width="80px"
                  prop="role_ids"
                  v-model="selectedRoles"
                  :columns="roleOptions"
                  type="checkbox"
                  placeholder="请选择用户角色"
                  @confirm="onRoleChange"
                />
              </wd-cell-group>
            </view>

            <!-- 其他信息 -->
            <view class="form-section">
              <view class="section-title">其他信息</view>

              <wd-cell-group border>
                <!-- 备注 -->
                <wd-textarea
                  label="备注"
                  label-width="80px"
                  prop="remark"
                  v-model="userForm.remark"
                  placeholder="请输入备注信息"
                  :readonly="isViewMode"
                  :maxlength="200"
                  show-word-limit
                  :rows="3"
                />
              </wd-cell-group>
            </view>

            <!-- 查看模式显示额外信息 -->
            <view v-if="isViewMode && userDetail" class="form-section">
              <view class="section-title">系统信息</view>

              <wd-cell-group border>
                <!-- 最后登录时间 -->
                <wd-cell title="最后登录时间" :value="userDetail.last_login_date || '从未登录'" />

                <!-- 最后登录IP -->
                <wd-cell title="最后登录IP" :value="userDetail.last_login_ip || '-'" />

                <!-- 创建时间 -->
                <wd-cell title="创建时间" :value="userDetail.created_date" />

                <!-- 更新时间 -->
                <wd-cell title="更新时间" :value="userDetail.updated_date" />
              </wd-cell-group>
            </view>

            <!-- 角色信息 - 查看模式显示 -->
            <view v-if="isViewMode && userDetail?.roles?.length" class="form-section">
              <view class="section-title">角色信息</view>

              <wd-cell-group border>
                <wd-cell title="用户角色">
                  <template #value>
                    <view class="roles-display">
                      <text
                        v-for="role in userDetail.roles"
                        :key="role.role_id"
                        class="role-tag"
                        :class="getRoleStatusClass(role.status)"
                      >
                        {{ role.role_name }}
                        <text v-if="role.commission_rate" class="commission-rate">
                          ({{ (parseFloat(role.commission_rate) * 100).toFixed(2) }}%)
                        </text>
                      </text>
                    </view>
                  </template>
                </wd-cell>
              </wd-cell-group>
            </view>
          </wd-form>

          <!-- 操作按钮 -->
          <view v-if="!isViewMode" class="form-actions">
            <wd-button @click="handleCancel" size="large" custom-class="cancel-btn">取消</wd-button>
            <wd-button
              type="primary"
              @click="handleSubmit"
              size="large"
              :loading="submitting"
              custom-class="submit-btn"
            >
              {{ isEditMode ? '更新' : '创建' }}
            </wd-button>
          </view>

          <!-- 查看模式的操作按钮 -->
          <view v-else class="view-actions">
            <wd-button
              type="primary"
              @click="switchToEditMode"
              size="large"
              custom-class="edit-btn"
            >
              编辑用户
            </wd-button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import {
  createSystemMerchantUserApi,
  updateSystemMerchantUserApi,
  getSystemMerchantUserDetailApi,
  checkMerchantUsernameExistsApi,
  checkMerchantEmailExistsApi,
  checkMerchantPhoneExistsApi,
  checkMerchantRealNameExistsApi,
  assignRolesToMerchantUserApi,
  type ISysMerchantUserCreateRequest,
  type ISysMerchantUserUpdateRequest,
  type ISysMerchantUserDetailResponse,
} from '@/api/sys/systemMerchantUserApi'
import {
  getMerchantRoleSelectItemsApi,
  type ISysMerchantRoleSelectItem,
} from '@/api/sys/systemMerchantRoleApi'
import {
  SysMerchantUserStatus,
  SysMerchantUserStatusMap,
  SysMerchantUserGender,
  SysMerchantUserGenderMap,
  SysMerchantUserValidation,
} from '@/api/sys/constants/systemMerchantUser'
import { watch } from '@vue/runtime-core'

defineOptions({
  name: 'MerchantUserForm',
})

// 页面参数
const pageParams = ref<{
  mode: 'add' | 'edit' | 'view'
  userId?: string
}>({
  mode: 'add',
})

// 计算属性
const isAddMode = computed(() => pageParams.value.mode === 'add')
const isEditMode = computed(() => pageParams.value.mode === 'edit')
const isViewMode = computed(() => pageParams.value.mode === 'view')

// 获取页面标题
const getPageTitle = () => {
  switch (pageParams.value.mode) {
    case 'add':
      return '新增商户用户'
    case 'edit':
      return '编辑商户用户'
    case 'view':
      return '查看商户用户'
    default:
      return '商户用户表单'
  }
}

// 表单引用
const formRef = ref()

// 提交状态
const submitting = ref(false)

// 用户详情（查看模式）
const userDetail = ref<ISysMerchantUserDetailResponse | null>(null)

// 用户表单数据
const userForm = reactive<ISysMerchantUserCreateRequest & { id?: string }>({
  username: '',
  password: '',
  real_name: '',
  phone: '',
  email: '',
  avatar: '',
  gender: SysMerchantUserGender.UNKNOWN,
  id_card: '',
  status: SysMerchantUserStatus.ENABLED, // 默认启用
  remark: '',
})

// 性别选项
const genderOptions = ref([
  {
    label: SysMerchantUserGenderMap[SysMerchantUserGender.MALE],
    value: SysMerchantUserGender.MALE,
  },
  {
    label: SysMerchantUserGenderMap[SysMerchantUserGender.FEMALE],
    value: SysMerchantUserGender.FEMALE,
  },
  {
    label: SysMerchantUserGenderMap[SysMerchantUserGender.UNKNOWN],
    value: SysMerchantUserGender.UNKNOWN,
  },
])

// 状态选项
const statusOptions = ref([
  {
    label: SysMerchantUserStatusMap[SysMerchantUserStatus.ENABLED],
    value: SysMerchantUserStatus.ENABLED,
  },
  {
    label: SysMerchantUserStatusMap[SysMerchantUserStatus.DISABLED],
    value: SysMerchantUserStatus.DISABLED,
  },
  {
    label: SysMerchantUserStatusMap[SysMerchantUserStatus.LOCKED],
    value: SysMerchantUserStatus.LOCKED,
  },
])

// 角色选择相关
const selectedRoles = ref<string[]>([])
const roleOptions = ref<{ label: string; value: string }[]>([])
const showRoleCommissionDialog = ref(false)
const roleCommissionRates = ref<Record<string, string>>({}) // 角色佣金比例

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名' },
    {
      pattern: SysMerchantUserValidation.USERNAME_PATTERN,
      message: `用户名只能包含字母、数字、下划线，长度${SysMerchantUserValidation.USERNAME_MIN_LENGTH}-${SysMerchantUserValidation.USERNAME_MAX_LENGTH}位`,
    },
    {
      validator: async (value) => {
        if (!value || value.length < SysMerchantUserValidation.USERNAME_MIN_LENGTH) {
          return Promise.resolve()
        }

        try {
          const params = {
            username: value,
            exclude_id: isEditMode.value ? pageParams.value.userId : undefined,
          }
          const result = await checkMerchantUsernameExistsApi(params)
          if (result.data) {
            return Promise.reject('用户名已存在，请重新输入')
          }
          return Promise.resolve()
        } catch (error) {
          console.error('检查用户名失败:', error)
          return Promise.resolve()
        }
      },
    },
  ],
  password: [
    { required: true, message: '请输入密码' },
    {
      min: SysMerchantUserValidation.PASSWORD_MIN_LENGTH,
      max: SysMerchantUserValidation.PASSWORD_MAX_LENGTH,
      message: `密码长度必须在${SysMerchantUserValidation.PASSWORD_MIN_LENGTH}-${SysMerchantUserValidation.PASSWORD_MAX_LENGTH}个字符之间`,
    },
    {
      pattern: SysMerchantUserValidation.PASSWORD_PATTERN,
      message: `密码必须包含字母和数字，长度${SysMerchantUserValidation.PASSWORD_MIN_LENGTH}-${SysMerchantUserValidation.PASSWORD_MAX_LENGTH}位`,
    },
  ],
  real_name: [
    { required: true, message: '请输入真实姓名' },
    {
      pattern: SysMerchantUserValidation.REAL_NAME_PATTERN,
      message: `真实姓名只能包含中英文，长度1-${SysMerchantUserValidation.REAL_NAME_MAX_LENGTH}位`,
    },
    {
      validator: async (value) => {
        if (!value || !value.trim()) {
          return Promise.resolve()
        }

        try {
          const params = {
            real_name: value,
            exclude_id: isEditMode.value ? pageParams.value.userId : undefined,
          }
          const result = await checkMerchantRealNameExistsApi(params)
          if (result.data) {
            return Promise.reject('真实姓名已被使用，请更换姓名')
          }
          return Promise.resolve()
        } catch (error) {
          console.error('检查真实姓名失败:', error)
          return Promise.resolve()
        }
      },
    },
  ],
  phone: [
    { required: true, message: '请输入手机号' },
    {
      pattern: SysMerchantUserValidation.PHONE_PATTERN,
      message: '手机号长度必须为11位且格式正确',
    },
    {
      validator: async (value) => {
        if (!value || !value.trim()) {
          return Promise.resolve()
        }

        try {
          const params = {
            phone: value,
            exclude_id: isEditMode.value ? pageParams.value.userId : undefined,
          }
          const result = await checkMerchantPhoneExistsApi(params)
          if (result.data) {
            return Promise.reject('手机号已被使用，请更换手机号')
          }
          return Promise.resolve()
        } catch (error) {
          console.error('检查手机号失败:', error)
          return Promise.resolve()
        }
      },
    },
  ],
  email: [
    {
      pattern: SysMerchantUserValidation.EMAIL_PATTERN,
      message: '邮箱格式不正确',
    },
    {
      validator: async (value) => {
        if (!value || !value.trim()) {
          return Promise.resolve()
        }

        try {
          const params = {
            email: value,
            exclude_id: isEditMode.value ? pageParams.value.userId : undefined,
          }
          const result = await checkMerchantEmailExistsApi(params)
          if (result.data) {
            return Promise.reject('邮箱已被使用，请更换邮箱')
          }
          return Promise.resolve()
        } catch (error) {
          console.error('检查邮箱失败:', error)
          return Promise.resolve()
        }
      },
    },
  ],
  id_card: [
    {
      pattern: SysMerchantUserValidation.ID_CARD_PATTERN,
      message: '身份证号码格式不正确',
    },
  ],
  status: [{ required: true, message: '请选择状态' }],
} as any

// 获取角色状态样式类
const getRoleStatusClass = (status: number) => {
  return status === 1 ? 'role-enabled' : 'role-disabled'
}

// 失去焦点时校验对应字段
const handleUsernameBlur = () => {
  if (formRef.value && userForm.username) {
    formRef.value.validate('username')
  }
}

const handleRealNameBlur = () => {
  if (formRef.value && userForm.real_name) {
    formRef.value.validate('real_name')
  }
}

const handlePhoneBlur = () => {
  if (formRef.value && userForm.phone) {
    formRef.value.validate('phone')
  }
}

const handleEmailBlur = () => {
  if (formRef.value && userForm.email) {
    formRef.value.validate('email')
  }
}

const handleIdCardBlur = () => {
  if (formRef.value && userForm.id_card) {
    formRef.value.validate('id_card')
  }
}

// 性别选择事件
const onGenderChange = ({ value }: { value: number }) => {
  userForm.gender = value
}

// 状态选择事件
const onStatusChange = ({ value }: { value: number }) => {
  userForm.status = value
}

// 角色选择事件
const onRoleChange = ({ value }: { value: string[] }) => {
  console.log('角色选择变化:', value)
  selectedRoles.value = value || []
}

// 加载商户角色数据
const loadMerchantRoles = async () => {
  try {
    // TODO: 这里需要传入实际的商户ID，可能从全局状态或路由参数中获取
    const merchantId = 1 // 临时使用固定值，实际应从上下文获取
    const result = await getMerchantRoleSelectItemsApi(merchantId)

    roleOptions.value = result.data.map((role: ISysMerchantRoleSelectItem) => ({
      label: role.role_name,
      value: role.id
    }))

    console.log('商户角色数据加载成功:', roleOptions.value)
  } catch (error) {
    console.error('加载商户角色数据失败:', error)
    uni.showToast({
      title: '加载角色数据失败',
      icon: 'none'
    })
  }
}

// 头像上传功能
const chooseAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      // 这里只是设置本地图片路径，实际项目中需要上传到服务器
      userForm.avatar = res.tempFilePaths[0]

      // TODO: 上传到服务器
      // uploadAvatar(res.tempFilePaths[0]).then(url => {
      //   userForm.avatar = url
      // })

      uni.showToast({
        title: '头像设置成功',
        icon: 'success',
      })
    },
    fail: (error) => {
      console.error('选择头像失败:', error)
      uni.showToast({
        title: '选择头像失败',
        icon: 'none',
      })
    },
  })
}

// 加载用户详情
const loadUserDetail = async (userId: string) => {
  try {
    uni.showLoading({ title: '加载中...' })

    const result = await getSystemMerchantUserDetailApi(userId)
    userDetail.value = result.data

    // 填充表单数据
    Object.assign(userForm, {
      id: userDetail.value.id,
      username: userDetail.value.username,
      real_name: userDetail.value.real_name || '',
      phone: userDetail.value.phone || '',
      email: userDetail.value.email || '',
      avatar: userDetail.value.avatar || '',
      gender: userDetail.value.gender || SysMerchantUserGender.UNKNOWN,
      id_card: userDetail.value.id_card || '',
      status: userDetail.value.status,
      remark: userDetail.value.remark || '',
    })

    // 设置用户角色
    if (userDetail.value.roles && userDetail.value.roles.length > 0) {
      selectedRoles.value = userDetail.value.roles.map(role => role.role_id)
    }

    console.log('用户详情加载成功:', userDetail.value)
  } catch (error) {
    console.error('加载用户详情失败:', error)
    uni.showToast({
      title: '加载用户详情失败',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

// 切换到编辑模式
const switchToEditMode = () => {
  const currentUrl = getCurrentPages()[getCurrentPages().length - 1].route
  const newUrl = `/${currentUrl}?mode=edit&userId=${pageParams.value.userId}`

  uni.redirectTo({
    url: newUrl,
  })
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 验证表单
    const { valid, errors } = await formRef.value.validate()

    console.log('表单验证结果:', { valid, errors })

    if (!valid) {
      console.log('表单验证失败:', errors)
      uni.showToast({
        title: '请检查表单信息',
        icon: 'none',
      })
      return
    }

    console.log('表单验证通过，开始提交数据')
    submitting.value = true

    let userId = pageParams.value.userId

    if (isEditMode.value) {
      // 编辑模式
      const updateData: ISysMerchantUserUpdateRequest = {
        username: userForm.username,
        real_name: userForm.real_name,
        phone: userForm.phone,
        email: userForm.email,
        avatar: userForm.avatar,
        gender: userForm.gender,
        id_card: userForm.id_card,
        status: userForm.status,
        remark: userForm.remark,
      }

      console.log('编辑提交数据:', updateData)
      await updateSystemMerchantUserApi(userId!, updateData)

      uni.showToast({
        title: '更新成功',
        icon: 'success',
      })
    } else {
      // 新增模式
      const createData: ISysMerchantUserCreateRequest = {
        username: userForm.username,
        password: userForm.password,
        real_name: userForm.real_name,
        phone: userForm.phone,
        email: userForm.email,
        avatar: userForm.avatar,
        gender: userForm.gender,
        id_card: userForm.id_card,
        status: userForm.status,
        remark: userForm.remark,
      }

      console.log('新增提交数据:', createData)
      const createResult = await createSystemMerchantUserApi(createData)
      userId = createResult.data // 获取新创建用户的ID

      uni.showToast({
        title: '创建成功',
        icon: 'success',
      })
    }

    // 分配用户角色（如果选择了角色）
    if (selectedRoles.value.length > 0 && userId) {
      try {
        const roleAssignData = {
          merchant_id: 1, // TODO: 从上下文获取实际商户ID
          user_id: userId,
          role_ids: selectedRoles.value,
          remark: '表单分配角色'
        }

        console.log('分配角色数据:', roleAssignData)
        await assignRolesToMerchantUserApi(userId, roleAssignData)

        console.log('角色分配成功')
      } catch (roleError) {
        console.error('角色分配失败:', roleError)
        uni.showToast({
          title: '角色分配失败，但用户已保存',
          icon: 'none',
        })
      }
    }

    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1000)

  } catch (error) {
    console.error('操作失败:', error)

    // 如果是验证错误，显示验证失败的提示
    if (error && typeof error === 'object' && 'errors' in error) {
      console.log('表单验证异常:', error)
      uni.showToast({
        title: '表单验证失败',
        icon: 'none',
      })
    } else {
      // 其他错误（如API调用失败）
      uni.showToast({
        title: isEditMode.value ? '更新失败' : '创建失败',
        icon: 'none',
      })
    }
  } finally {
    submitting.value = false
  }
}

// 取消操作
const handleCancel = () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消操作吗？未保存的内容将丢失。',
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack()
      }
    },
  })
}

// 返回功能
const handleBack = () => {
  if (isViewMode.value) {
    uni.navigateBack()
  } else {
    handleCancel()
  }
}

// 页面加载事件
onLoad((options) => {
  console.log('商户用户表单页面参数:', options)

  if (options) {
    pageParams.value.mode = (options.mode as 'add' | 'edit' | 'view') || 'add'
    pageParams.value.userId = options.userId
  }

  // 如果是编辑或查看模式，加载用户详情
  if ((isEditMode.value || isViewMode.value) && pageParams.value.userId) {
    loadUserDetail(pageParams.value.userId)
  }
})

onMounted(() => {
  console.log('商户用户表单页面挂载完成')
  console.log('页面模式:', pageParams.value.mode)
  console.log('用户ID:', pageParams.value.userId)

  // 加载商户角色数据
  loadMerchantRoles()
})
</script>

<style lang="scss" scoped>
.merchant-user-form-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  overflow-y: auto;
  background-color: #f5f5f5;
}

.main-layout {
  padding: 12px;
}

.merchant-user-form {
  // 简化表单样式，直接展示
}

.form-section {
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 15px 12px 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

// 头像区域样式
.avatar-section {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: white;
  margin-bottom: 12px;
  border-radius: 8px;
}

.avatar-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  width: 80px;
  text-align: right;
  padding-right: 12px;
}

.avatar-upload {
  flex: 1;
}

.avatar-container,
.avatar-view {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  border: 2px dashed #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  transition: border-color 0.3s;

  &:hover {
    border-color: #4285f4;
  }
}

.avatar-view {
  border-style: solid;
  border-color: #eee;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 6px;
}

.avatar-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.placeholder-text {
  font-size: 12px;
  color: #999;
  text-align: center;
}

// 角色显示样式
.roles-display {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.role-tag {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  gap: 4px;

  &.role-enabled {
    background-color: #e8f5e8;
    color: #4caf50;
  }

  &.role-disabled {
    background-color: #ffebee;
    color: #f44336;
  }
}

.commission-rate {
  font-size: 10px;
  opacity: 0.8;
}

.form-actions {
  display: flex;
  gap: 12px;
  padding: 12px;
}

.view-actions {
  padding: 12px;
}

:deep(.cancel-btn) {
  flex: 1;
  background-color: #f5f5f5 !important;
  color: #666 !important;
  border-color: #ddd !important;
}

:deep(.submit-btn) {
  flex: 1;
}

:deep(.edit-btn) {
  width: 100%;
}

// Wot UI 表单项样式优化
:deep(.wd-cell-group) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.wd-cell) {
  background-color: white;
}

:deep(.wd-input) {
  background-color: transparent;
}

:deep(.wd-picker) {
  background-color: transparent;
}

:deep(.wd-textarea) {
  background-color: transparent;
}

// 身份证提示样式
.id-card-tip {
  padding: 8px 15px 0 15px;
  margin-bottom: 12px;
}

.tip-text {
  font-size: 12px;
  color: #ff9500;
  background-color: #fff7e6;
  padding: 6px 10px;
  border-radius: 4px;
  border-left: 3px solid #ff9500;
  display: block;
  line-height: 1.4;
}
</style>
