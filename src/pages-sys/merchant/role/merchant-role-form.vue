<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '商户角色表单',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="merchant-role-form-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      :title="pageTitle"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <view class="main-layout">
        <view class="merchant-role-form">
          <wd-form ref="formRef" :model="roleForm" :rules="formRules" errorType="message">
            <!-- 基本信息 -->
            <view class="form-section">
              <view class="section-title">基本信息</view>

              <wd-cell-group border>
                <!-- 角色编码 -->
                <wd-input
                  label="角色编码"
                  label-width="80px"
                  prop="role_code"
                  v-model="roleForm.role_code"
                  placeholder="请输入角色编码"
                  :readonly="isViewMode"
                  :maxlength="50"
                  show-word-limit
                  clearable
                  :rules="formRules.role_code"
                />

                <!-- 角色名称 -->
                <wd-input
                  label="角色名称"
                  label-width="80px"
                  prop="role_name"
                  v-model="roleForm.role_name"
                  placeholder="请输入角色名称"
                  :readonly="isViewMode"
                  :maxlength="50"
                  show-word-limit
                  clearable
                  :rules="formRules.role_name"
                />

                <!-- 角色类型 -->
                <wd-picker
                  label="角色类型"
                  label-width="80px"
                  prop="role_type"
                  v-model="roleForm.role_type"
                  :columns="roleTypeOptions"
                  placeholder="请选择角色类型"
                  :disabled="isViewMode"
                  @confirm="onRoleTypeChange"
                />

                <!-- 数据范围 -->
                <wd-picker
                  label="数据范围"
                  label-width="80px"
                  prop="data_scope"
                  v-model="roleForm.data_scope"
                  :columns="dataScopeOptions"
                  placeholder="请选择数据范围"
                  :disabled="isViewMode"
                  @confirm="onDataScopeChange"
                />

                <!-- 状态 -->
                <wd-cell title="状态">
                  <wd-switch
                    v-model="roleForm.status"
                    active-value="1"
                    inactive-value="0"
                    :disabled="isViewMode"
                    @change="onStatusChange"
                  />
                </wd-cell>

                <!-- 默认角色 -->
                <wd-cell title="默认角色">
                  <wd-switch
                    v-model="roleForm.is_default"
                    :active-value="true"
                    :inactive-value="false"
                    :disabled="isViewMode"
                    @change="onDefaultChange"
                  />
                </wd-cell>
              </wd-cell-group>
            </view>

            <!-- 详细信息 -->
            <view class="form-section">
              <view class="section-title">详细信息</view>

              <wd-cell-group border>
                <!-- 备注 -->
                <wd-textarea
                  label="备注"
                  label-width="80px"
                  prop="remark"
                  v-model="roleForm.remark"
                  placeholder="请输入备注信息"
                  :readonly="isViewMode"
                  :maxlength="200"
                  show-word-limit
                  :auto-height="true"
                  :min-height="60"
                />
              </wd-cell-group>
            </view>
          </wd-form>
        </view>
      </view>

      <!-- 底部操作按钮 -->
      <view v-if="!isViewMode" class="form-actions">
        <wd-button custom-class="cancel-btn" @click="handleCancel">取消</wd-button>
        <wd-button
          type="primary"
          custom-class="submit-btn"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          {{ isEditMode ? '更新' : '创建' }}
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  createSystemMerchantRoleApi,
  updateSystemMerchantRoleApi,
  getSystemMerchantRoleDetailApi,
  type ISysMerchantRoleCreateRequest,
  type ISysMerchantRoleUpdateRequest,
  type ISysMerchantRoleDetailResponse,
} from '@/api/sys/systemMerchantRoleApi'
import { getSystemMerchantBasicListApi } from '@/api/sys/systemMerchantApi'
import type { ISysMerchantBasicResponse } from '@/api/sys/types/merchant'

defineOptions({
  name: 'MerchantRoleForm',
})

// 页面参数
const pageParams = ref({
  mode: 'add' as 'add' | 'edit' | 'view',
  roleId: '',
  merchantId: '',
})

// 表单引用
const formRef = ref()

// 提交状态
const submitLoading = ref(false)

// 计算页面模式
const isAddMode = computed(() => pageParams.value.mode === 'add')
const isEditMode = computed(() => pageParams.value.mode === 'edit')
const isViewMode = computed(() => pageParams.value.mode === 'view')

// 计算页面标题
const pageTitle = computed(() => {
  switch (pageParams.value.mode) {
    case 'add':
      return '新增商户角色'
    case 'edit':
      return '编辑商户角色'
    case 'view':
      return '查看商户角色'
    default:
      return '商户角色表单'
  }
})

// 表单数据
const roleForm = ref({
  merchant_id: '',
  role_code: '',
  role_name: '',
  role_type: 2, // 默认为商户定义角色
  data_scope: 1, // 默认为商户全部数据
  status: '1', // 默认启用 - 使用字符串以匹配开关组件
  is_default: false, // 默认非默认角色
  remark: '',
})

// 角色类型选项
const roleTypeOptions = ref([
  { label: '系统定义角色', value: 1 },
  { label: '商户定义角色', value: 2 },
])

// 数据范围选项
const dataScopeOptions = ref([
  { label: '商户全部数据', value: 1 },
  { label: '个人数据', value: 2 },
])

// 表单验证规则
const formRules = ref({
  role_code: [
    { required: true, message: '请输入角色编码' },
    { min: 2, max: 50, message: '角色编码长度在 2 到 50 个字符' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '角色编码只能包含字母、数字、下划线和横线' },
  ],
  role_name: [
    { required: true, message: '请输入角色名称' },
    { min: 2, max: 50, message: '角色名称长度在 2 到 50 个字符' },
  ],
  role_type: [{ required: true, message: '请选择角色类型' }],
  data_scope: [{ required: true, message: '请选择数据范围' }],
  remark: [{ max: 200, message: '备注不能超过200个字符' }],
})

// 商户信息
const merchantInfo = ref<ISysMerchantBasicResponse | null>(null)

// 加载商户信息
const loadMerchantInfo = async () => {
  if (!pageParams.value.merchantId) return

  try {
    const result = await getSystemMerchantBasicListApi()
    if (result.code === 200 && result.data) {
      const merchant = result.data.find((m) => m.id.toString() === pageParams.value.merchantId)
      if (merchant) {
        merchantInfo.value = merchant
        roleForm.value.merchant_id = merchant.id.toString()
      }
    }
  } catch (error) {
    console.error('获取商户信息失败:', error)
  }
}

// 加载角色详情
const loadRoleDetail = async (roleId: string) => {
  try {
    const result = await getSystemMerchantRoleDetailApi(roleId)
    if (result.code === 200 && result.data) {
      const data = result.data
      roleForm.value = {
        merchant_id: data.merchant_id?.toString() || '',
        role_code: data.role_code || '',
        role_name: data.role_name || '',
        role_type: data.role_type || 2,
        data_scope: data.data_scope || 1,
        status: data.status ? '1' : '0',
        is_default: data.is_default || false,
        remark: data.remark || '',
      }
    } else {
      throw new Error(result.message || '获取角色详情失败')
    }
  } catch (error) {
    console.error('获取角色详情失败:', error)
    uni.showToast({
      title: '获取角色详情失败',
      icon: 'none',
    })
  }
}

// 角色类型变化事件
const onRoleTypeChange = ({ value }: { value: number }) => {
  console.log('角色类型变化:', value)
  roleForm.value.role_type = value
}

// 数据范围变化事件
const onDataScopeChange = ({ value }: { value: number }) => {
  console.log('数据范围变化:', value)
  roleForm.value.data_scope = value
}

// 状态变化事件
const onStatusChange = (value: string) => {
  console.log('状态变化:', value)
  roleForm.value.status = value
}

// 默认角色变化事件
const onDefaultChange = (value: boolean) => {
  console.log('默认角色变化:', value)
  roleForm.value.is_default = value
}

// 表单提交
const handleSubmit = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate()
    if (!valid) {
      uni.showToast({
        title: '请完善表单信息',
        icon: 'none',
      })
      return
    }

    submitLoading.value = true

    if (isAddMode.value) {
      // 新增角色
      const createRequest: ISysMerchantRoleCreateRequest = {
        merchant_id: Number(roleForm.value.merchant_id),
        role_code: roleForm.value.role_code,
        role_name: roleForm.value.role_name,
        role_type: roleForm.value.role_type,
        data_scope: roleForm.value.data_scope,
        status: Number(roleForm.value.status),
        is_default: roleForm.value.is_default,
      }

      const result = await createSystemMerchantRoleApi(createRequest)
      if (result.code === 200) {
        uni.showToast({
          title: '创建成功',
          icon: 'success',
        })
        // 返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } else {
        throw new Error(result.message || '创建失败')
      }
    } else if (isEditMode.value) {
      // 更新角色
      const updateRequest: ISysMerchantRoleUpdateRequest = {
        role_code: roleForm.value.role_code,
        role_name: roleForm.value.role_name,
        role_type: roleForm.value.role_type,
        data_scope: roleForm.value.data_scope,
        status: Number(roleForm.value.status),
        is_default: roleForm.value.is_default,
      }

      const result = await updateSystemMerchantRoleApi(pageParams.value.roleId, updateRequest)
      if (result.code === 200) {
        uni.showToast({
          title: '更新成功',
          icon: 'success',
        })
        // 返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } else {
        throw new Error(result.message || '更新失败')
      }
    }
  } catch (error) {
    console.error('提交失败:', error)
    uni.showToast({
      title: error instanceof Error ? error.message : '操作失败',
      icon: 'none',
    })
  } finally {
    submitLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消操作吗？未保存的内容将丢失。',
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack()
      }
    },
  })
}

// 返回功能
const handleBack = () => {
  if (isViewMode.value) {
    uni.navigateBack()
  } else {
    handleCancel()
  }
}

// 页面加载事件 - UniApp 生命周期
// @ts-ignore
onLoad((options) => {
  console.log('商户角色表单页面参数:', options)

  if (options) {
    pageParams.value.mode = (options.mode as 'add' | 'edit' | 'view') || 'add'
    pageParams.value.roleId = options.roleId || ''
    pageParams.value.merchantId = options.merchantId || ''
  }

  // 如果是编辑或查看模式，加载角色详情
  if ((isEditMode.value || isViewMode.value) && pageParams.value.roleId) {
    loadRoleDetail(pageParams.value.roleId)
  }

  // 加载商户信息
  if (pageParams.value.merchantId) {
    loadMerchantInfo()
  }
})

onMounted(() => {
  console.log('商户角色表单页面挂载完成')
  console.log('页面模式:', pageParams.value.mode)
  console.log('角色ID:', pageParams.value.roleId)
  console.log('商户ID:', pageParams.value.merchantId)
})
</script>

<style lang="scss" scoped>
.merchant-role-form-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding-bottom: 80px; /* 为固定按钮留出空间 */
}

.main-layout {
  padding: 8px;
}

.form-section {
  margin-bottom: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 15px 8px 15px;
  padding-bottom: 6px;
  border-bottom: 1px solid #eee;
}

.form-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 12px;
  padding: 12px;
  background-color: white;
  border-top: 1px solid #eee;
  z-index: 100;
  safe-area-inset-bottom: env(safe-area-inset-bottom);
}

:deep(.cancel-btn) {
  flex: 1;
  background-color: #f5f5f5 !important;
  color: #666 !important;
  border-color: #ddd !important;
}

:deep(.submit-btn) {
  flex: 1;
}

// Wot UI 表单项样式优化
:deep(.wd-cell-group) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.wd-cell) {
  background-color: white;
}

:deep(.wd-input) {
  background-color: transparent;
}

:deep(.wd-picker) {
  background-color: transparent;
}

:deep(.wd-textarea) {
  background-color: transparent;
}

// 查看模式下优化禁用组件的显示效果
:deep(.wd-picker.wd-picker--disabled) {
  opacity: 0.8 !important;
}

:deep(.wd-switch.wd-switch--disabled) {
  opacity: 0.8 !important;
}

:deep(.wd-input.wd-input--readonly) {
  opacity: 0.8 !important;
}

:deep(.wd-textarea.wd-textarea--readonly) {
  opacity: 0.8 !important;
}
// 确保在 Cell 中正确显示
:deep(.wd-cell) {
  .wd-input-number {
    justify-content: flex-end !important;
  }
}
</style> 