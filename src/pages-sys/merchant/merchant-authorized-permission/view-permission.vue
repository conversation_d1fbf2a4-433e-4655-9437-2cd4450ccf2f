<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '查看商户权限',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="view-permission-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      title="商户权限详情"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <view class="main-layout">
        <!-- 商户信息 -->
        <view class="merchant-info-section" v-if="merchantInfo">
          <!-- 商户头像和基本信息 -->
          <view class="merchant-header">
            <view class="merchant-avatar">
              <wd-img
                v-if="merchantInfo.avatar"
                :src="merchantInfo.avatar"
                :width="80"
                :height="80"
                round
                mode="aspectFill"
                custom-class="avatar-img"
              >
                <template #error>
                  <view class="avatar-placeholder">
                    <wd-icon name="store" size="40" color="#4285f4" />
                  </view>
                </template>
                <template #loading>
                  <view class="avatar-placeholder">
                    <wd-icon name="store" size="40" color="#ddd" />
                  </view>
                </template>
              </wd-img>
              <view v-else class="avatar-placeholder">
                <wd-icon name="store" size="40" color="#4285f4" />
              </view>
            </view>
            <view class="merchant-info">
              <text class="merchant-name">{{ merchantInfo.merchant_name }}</text>
              <view class="merchant-meta">
                <text class="merchant-category" v-if="merchantInfo.category_name">
                  {{ merchantInfo.category_name }}
                </text>
                <view class="permission-summary">
                  <text class="permission-text">
                    已授权 {{ permissionStats.authorized_count || 0 }} / {{ permissionStats.total_count || 0 }} 项权限
                  </text>
                </view>
              </view>
            </view>
          </view>

          <!-- 详细信息 -->
          <view class="merchant-details">
            <view class="detail-row">
              <view class="detail-item" v-if="merchantInfo.phone">
                <text class="detail-label">联系电话</text>
                <text class="detail-value">{{ merchantInfo.phone }}</text>
              </view>
              <view class="detail-item" v-if="merchantInfo.email">
                <text class="detail-label">邮箱地址</text>
                <text class="detail-value">{{ merchantInfo.email }}</text>
              </view>
            </view>
            
            <view class="detail-row" v-if="merchantInfo.address">
              <view class="detail-item detail-full">
                <text class="detail-label">经营地址</text>
                <text class="detail-value">{{ merchantInfo.address }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 权限树形展示 -->
        <view class="permission-tree-section">
          <view class="section-header">
            <text class="section-title">权限详情</text>
            <view class="tree-actions">
              <wd-button 
                type="info" 
                size="small" 
                @click="expandAll"
                :disabled="loading"
              >
                全部展开
              </wd-button>
              <wd-button 
                type="info" 
                size="small" 
                @click="collapseAll"
                :disabled="loading"
              >
                全部收起
              </wd-button>
              <wd-button 
                type="primary" 
                size="small" 
                @click="refreshPermissions"
                :loading="loading"
              >
                刷新
              </wd-button>
            </view>
          </view>

          <!-- 加载状态 -->
          <view v-if="loading" class="loading-state">
            <wd-loading />
            <text class="loading-text">加载权限数据中...</text>
          </view>

          <!-- 权限树 -->
          <view v-else-if="permissionTree.length > 0" class="permission-tree">
            <!-- 一级节点 -->
            <view v-for="node in permissionTree" :key="node.id" class="permission-node-container level-1">
              <!-- 一级节点内容 -->
              <view class="permission-node" @click="toggleExpand(node.id)">
                <view class="permission-node-info">
                  <!-- 展开/收起图标 -->
                  <view
                    class="expand-icon"
                    v-if="node.children && node.children.length > 0"
                    @click.stop="toggleExpand(node.id)"
                  >
                    <wd-icon
                      :name="expandedPermissions.includes(node.id) ? 'arrow-down' : 'arrow-right'"
                      size="14"
                      custom-class="expand-toggle-icon"
                      :class="{ rotated: expandedPermissions.includes(node.id) }"
                    />
                  </view>
                  <!-- 节点图标 -->
                  <wd-icon 
                    v-if="node.icon && node.icon.trim()" 
                    :name="node.icon" 
                    size="16" 
                    custom-class="permission-node-icon" 
                  />
                  <!-- 节点信息 -->
                  <view class="permission-main-info">
                    <view class="permission-title-row">
                      <view class="permission-title-group">
                        <text class="permission-node-title">{{ node.permission_name }}</text>
                        <text
                          class="permission-node-badge"
                          :class="getPermissionTypeClass(node.permission_type)"
                        >
                          {{ node.permission_type_desc }}
                        </text>
                        <text
                          class="permission-auth-status"
                          :class="{ authorized: node.is_authorized }"
                        >
                          {{ node.is_authorized ? '已授权' : '未授权' }}
                        </text>
                      </view>
                      <text class="permission-node-code">{{ node.permission_code }}</text>
                    </view>
                    <view class="permission-meta-row" v-if="node.authorized_date || node.status_desc">
                      <text class="permission-auth-date" v-if="node.authorized_date">
                        授权时间：{{ node.authorized_date }}
                      </text>
                      <text class="permission-status" v-if="node.status_desc">
                        状态：{{ node.status_desc }}
                      </text>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 子节点容器 -->
              <view
                v-if="node.children && node.children.length > 0"
                class="permission-children level-2"
                :class="{ expanded: expandedPermissions.includes(node.id) }"
              >
                <!-- 二级节点 -->
                <view
                  v-for="child in node.children"
                  :key="child.id"
                  class="permission-node-container level-2"
                >
                  <!-- 二级节点内容 -->
                  <view class="permission-node" @click="toggleExpand(child.id)">
                    <view class="permission-node-info">
                      <!-- 展开/收起图标 -->
                      <view
                        class="expand-icon"
                        v-if="child.children && child.children.length > 0"
                        @click.stop="toggleExpand(child.id)"
                      >
                        <wd-icon
                          :name="expandedPermissions.includes(child.id) ? 'arrow-down' : 'arrow-right'"
                          size="12"
                          custom-class="expand-toggle-icon"
                          :class="{ rotated: expandedPermissions.includes(child.id) }"
                        />
                      </view>
                      <!-- 节点信息 -->
                      <view class="permission-main-info">
                        <view class="permission-title-row">
                          <view class="permission-title-group">
                            <text class="permission-node-title">{{ child.permission_name }}</text>
                            <text
                              class="permission-node-badge"
                              :class="getPermissionTypeClass(child.permission_type)"
                            >
                              {{ child.permission_type_desc }}
                            </text>
                            <text
                              class="permission-auth-status"
                              :class="{ authorized: child.is_authorized }"
                            >
                              {{ child.is_authorized ? '已授权' : '未授权' }}
                            </text>
                          </view>
                          <text class="permission-node-code">{{ child.permission_code }}</text>
                        </view>
                        <view class="permission-meta-row" v-if="child.authorized_date || child.status_desc">
                          <text class="permission-auth-date" v-if="child.authorized_date">
                            授权时间：{{ child.authorized_date }}
                          </text>
                          <text class="permission-status" v-if="child.status_desc">
                            状态：{{ child.status_desc }}
                          </text>
                        </view>
                      </view>
                    </view>
                  </view>

                  <!-- 三级节点容器 -->
                  <view
                    v-if="child.children && child.children.length > 0"
                    class="permission-children level-3"
                    :class="{ expanded: expandedPermissions.includes(child.id) }"
                  >
                    <!-- 三级节点 -->
                    <view
                      v-for="grandChild in child.children"
                      :key="grandChild.id"
                      class="permission-node-container level-3"
                    >
                      <!-- 三级节点内容 -->
                      <view class="permission-node">
                        <view class="permission-node-info">
                          <!-- 节点图标 -->
                          <wd-icon 
                            v-if="grandChild.icon && grandChild.icon.trim()" 
                            :name="grandChild.icon" 
                            size="12" 
                            custom-class="permission-node-icon" 
                          />
                          <!-- 节点信息 -->
                          <view class="permission-main-info">
                            <view class="permission-title-row">
                              <view class="permission-title-group">
                                <text class="permission-node-title">{{ grandChild.permission_name }}</text>
                                <text
                                  class="permission-node-badge"
                                  :class="getPermissionTypeClass(grandChild.permission_type)"
                                >
                                  {{ grandChild.permission_type_desc }}
                                </text>
                                <text
                                  class="permission-auth-status"
                                  :class="{ authorized: grandChild.is_authorized }"
                                >
                                  {{ grandChild.is_authorized ? '已授权' : '未授权' }}
                                </text>
                              </view>
                              <text class="permission-node-code">{{ grandChild.permission_code }}</text>
                            </view>
                            <view class="permission-meta-row" v-if="grandChild.authorized_date || grandChild.status_desc">
                              <text class="permission-auth-date" v-if="grandChild.authorized_date">
                                授权时间：{{ grandChild.authorized_date }}
                              </text>
                              <text class="permission-status" v-if="grandChild.status_desc">
                                状态：{{ grandChild.status_desc }}
                              </text>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <wd-status-tip
            v-else-if="!loading"
            type="search"
            tip="该商户暂无权限数据"
          />
        </view>
      </view>

      <!-- 底部操作栏 -->
      <view class="bottom-actions" v-if="merchantInfo">
        <wd-button 
          type="primary" 
          size="large" 
          @click="goToEditPermission"
          :disabled="loading"
        >
          修改权限
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { 
  getMerchantAuthorizedPermissionTreeApi,
  type IMerchantAuthorizedPermissionTreeResponse,
  type IMerchantAuthorizedPermissionTreeNode
} from '@/api/sys/systemMerchantAuthorizedPermissionApi'
import { getSystemMerchantDetailApi } from '@/api/sys/systemMerchantApi'

defineOptions({
  name: 'ViewMerchantPermission',
})

// 权限节点数据类型
interface PermissionNode {
  id: string
  permission_name: string
  permission_code: string
  permission_type: number
  permission_type_desc: string
  parent_id?: string
  order_num: number
  is_authorized: boolean
  authorized_permission_id?: string
  authorized_date?: string
  status?: number
  status_desc?: string
  icon?: string
  children: PermissionNode[]
}

// 商户权限树响应类型
interface MerchantPermissionTreeResponse {
  merchant_id: number
  merchant_name: string
  tree: PermissionNode[]
}

// 页面参数
const merchantId = ref<number>()
const merchantName = ref<string>('')

// 页面状态
const loading = ref(false)
const permissionTree = ref<PermissionNode[]>([])
const expandedPermissions = ref<string[]>([])

// 商户信息
const merchantInfo = ref<{
  merchant_id: number
  merchant_name: string
  phone?: string
  email?: string
  address?: string
  category_name?: string
  merchant_code?: string
  avatar?: string
} | null>(null)

// 权限统计
const permissionStats = ref({
  total_count: 0,
  authorized_count: 0,
})

// 页面标题
const pageTitle = computed(() => {
  return '商户权限详情'
})

// 获取商户权限树
const fetchMerchantPermissionTree = async () => {
  if (!merchantId.value) {
    console.error('商户ID不能为空')
    return
  }

  loading.value = true
  try {
    const result = await getMerchantAuthorizedPermissionTreeApi(merchantId.value)

    if (result.code === 200 && result.data) {
      // 如果还没有商户基本信息，先设置基本信息
      if (!merchantInfo.value) {
        merchantInfo.value = {
          merchant_id: result.data.merchant_id,
          merchant_name: result.data.merchant_name,
        }
      }
      
      permissionTree.value = result.data.tree
      
      // 计算权限统计
      calculatePermissionStats(result.data.tree)
      
      console.log('获取商户权限树成功:', result.data)
    } else {
      throw new Error(result.message || '获取权限数据失败')
    }
  } catch (error) {
    console.error('获取商户权限树失败:', error)
    uni.showToast({
      title: '获取权限数据失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 计算权限统计
const calculatePermissionStats = (nodes: PermissionNode[]) => {
  let total = 0
  let authorized = 0

  const countNodes = (nodeList: PermissionNode[]) => {
    nodeList.forEach((node) => {
      total++
      if (node.is_authorized) {
        authorized++
      }
      if (node.children.length > 0) {
        countNodes(node.children)
      }
    })
  }

  countNodes(nodes)
  
  permissionStats.value = {
    total_count: total,
    authorized_count: authorized,
  }
}

// 切换节点展开状态
const toggleExpand = (nodeId: string) => {
  const index = expandedPermissions.value.indexOf(nodeId)
  if (index > -1) {
    expandedPermissions.value.splice(index, 1)
  } else {
    expandedPermissions.value.push(nodeId)
  }
}

// 全部展开
const expandAll = () => {
  const allNodeIds: string[] = []
  
  const collectNodeIds = (nodes: PermissionNode[]) => {
    nodes.forEach((node) => {
      allNodeIds.push(node.id)
      if (node.children.length > 0) {
        collectNodeIds(node.children)
      }
    })
  }
  
  collectNodeIds(permissionTree.value)
  expandedPermissions.value = allNodeIds
}

// 全部收起
const collapseAll = () => {
  expandedPermissions.value = []
}

// 刷新权限数据
const refreshPermissions = () => {
  Promise.all([
    fetchMerchantDetail(),
    fetchMerchantPermissionTree()
  ])
}

// 获取权限类型样式类
const getPermissionTypeClass = (type: number) => {
  switch (type) {
    case 1:
      return 'type-directory'
    case 2:
      return 'type-menu'
    case 3:
      return 'type-button'
    default:
      return ''
  }
}

// 前往修改权限页面
const goToEditPermission = () => {
  if (!merchantId.value) return
  
  uni.navigateTo({
    url: `/pages-sys/merchant/merchant-authorized-permission/edit-permission?merchantId=${merchantId.value}&merchantName=${encodeURIComponent(merchantName.value)}`,
  })
}

// 返回功能
const handleBack = () => {
  uni.navigateBack()
}

// 获取商户详细信息
const fetchMerchantDetail = async () => {
  if (!merchantId.value) return

  try {
    const result = await getSystemMerchantDetailApi(merchantId.value)
    
    if (result.code === 200 && result.data) {
      // 更新商户信息，包含详细字段
      merchantInfo.value = {
        merchant_id: result.data.id,
        merchant_name: result.data.merchant_name,
        phone: result.data.phone,
        email: result.data.email,
        address: result.data.address,
        merchant_code: result.data.merchant_code,
        category_name: result.data.category_name,
        avatar: result.data.avatar, // 添加头像字段
      }
      
      console.log('获取商户详情成功:', result.data)
    } else {
      throw new Error(result.message || '获取商户详情失败')
    }
  } catch (error) {
    console.error('获取商户详情失败:', error)
    // 如果获取详情失败，保持基本信息
    if (!merchantInfo.value) {
      merchantInfo.value = {
        merchant_id: merchantId.value,
        merchant_name: merchantName.value,
      }
    }
  }
}

// 页面初始化
onShow(async () => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}
  
  merchantId.value = options.merchantId ? parseInt(options.merchantId) : undefined
  merchantName.value = options.merchantName ? decodeURIComponent(options.merchantName) : ''
  
  console.log('页面参数:', { merchantId: merchantId.value, merchantName: merchantName.value })
  
  if (merchantId.value) {
    // 并行获取商户详情和权限数据
    await Promise.all([
      fetchMerchantDetail(),
      fetchMerchantPermissionTree()
    ])
  } else {
    uni.showToast({
      title: '缺少商户ID参数',
      icon: 'none',
    })
  }
})
</script>

<style lang="scss" scoped>
.view-permission-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 18px !important;
  font-weight: bold !important;
}

:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-layout {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  padding-bottom: 120px; // 进一步增加底部内边距，确保滚动到底部时内容不被遮挡
  -webkit-overflow-scrolling: touch; // iOS平滑滚动
  scroll-behavior: smooth; // 平滑滚动行为
}

// 商户信息区域
.merchant-info-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.merchant-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.merchant-avatar {
  margin-right: 15px;
  flex-shrink: 0;
}

.avatar-img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4285f4;
}

.merchant-info {
  flex: 1;
  min-width: 0;
}

.merchant-name {
  font-size: 18px;
  font-weight: 700;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.merchant-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 4px;
}

.merchant-category {
  font-size: 14px;
  color: #666;
  background-color: #f0f0f0;
  padding: 4px 10px;
  border-radius: 8px;
  font-weight: 500;
}

.permission-summary {
  background-color: #e8f4fd;
  border-radius: 8px;
  padding: 4px 10px;
  display: flex;
  align-items: center;
  border: 1px solid #64b5f6;
}

.permission-text {
  font-size: 12px;
  color: #1976d2;
  font-weight: 600;
}

.merchant-details {
  padding-left: 0;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.detail-item {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.detail-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  font-weight: 500;
}

.detail-value {
  font-size: 14px;
  color: #333;
  word-break: break-all;
  line-height: 1.4;
}

.detail-full {
  flex: 1;
}

// 权限树形区域
.permission-tree-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.tree-actions {
  display: flex;
  gap: 8px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 20px;
  gap: 16px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.permission-tree {
  padding: 0;
}

// 权限节点样式
.permission-node-container {
  margin-bottom: 2px;

  &.level-1 {
    background-color: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    margin-bottom: 4px;
    margin-left: 0;
    margin-right: 0;
  }

  &.level-2 {
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
  }

  &.level-3 {
    background-color: #fbfbfb;
    border-bottom: 1px solid #f0f0f0;
    margin: 1px 0;
  }
}

.permission-node {
  display: flex;
  flex-direction: column;
  padding: 10px 12px;
  transition: background-color 0.2s ease;
}

.permission-node-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.permission-main-info {
  flex: 1;
  min-width: 0;
}

.permission-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.permission-title-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
  flex-wrap: wrap;
}

.permission-meta-row {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 2px;
}

.expand-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  margin-right: 4px;
  margin-top: 0;

  &:hover {
    background-color: rgba(66, 133, 244, 0.1);
  }
}

:deep(.expand-toggle-icon) {
  color: #4285f4 !important;
  transition: transform 0.3s ease !important;
}

.permission-children {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
  transition:
    max-height 0.3s ease,
    opacity 0.3s ease;
  padding-left: 20px;
  border-left: 2px solid #e0e0e0;
  margin-left: 16px;

  &.expanded {
    max-height: 2000px; // 足够大的值来容纳所有内容
    opacity: 1;
  }

  &.level-3 {
    margin-top: 4px;
    padding-left: 16px;
  }
}

.permission-node-icon {
  color: #666;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  margin-top: 0;
}

.permission-node-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  margin-right: 4px;
}

.permission-node-code {
  font-size: 12px;
  color: #666;
  font-family: monospace;
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  flex-shrink: 0;
}

.permission-node-badge {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  white-space: nowrap;
  flex-shrink: 0;
  display: flex;
  align-items: center;

  &.type-directory {
    background-color: #fff3e0;
    color: #e65100;
  }

  &.type-menu {
    background-color: #e3f2fd;
    color: #1976d2;
  }

  &.type-button {
    background-color: #e8f5e8;
    color: #388e3c;
  }
}

.permission-auth-status {
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
  background-color: #ffebee;
  color: #f44336;
  flex-shrink: 0;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  border: 1px solid #ffcdd2;

  &.authorized {
    background-color: #e8f5e8;
    color: #4caf50;
    border-color: #c8e6c9;
  }
}

.permission-node-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding-left: 24px;
}

.permission-auth-date,
.permission-status {
  font-size: 12px;
  color: #999;
  background-color: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
}

.permission-auth-date {
  color: #666;
}

.permission-status {
  color: #888;
}

// 底部操作栏
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 12px 20px;
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
}
</style> 