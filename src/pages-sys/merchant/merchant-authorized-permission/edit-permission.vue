<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '修改商户权限',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="edit-permission-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      title="修改商户授权"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <view class="main-layout">
        <!-- 商户信息 -->
        <view class="merchant-info-section" v-if="merchantInfo">
          <!-- 商户头像和基本信息 -->
          <view class="merchant-header">
            <view class="merchant-avatar">
              <wd-img
                v-if="merchantInfo.avatar"
                :src="merchantInfo.avatar"
                :width="60"
                :height="60"
                round
                mode="aspectFill"
                custom-class="avatar-img"
              >
                <template #error>
                  <view class="avatar-placeholder">
                    <wd-icon name="store" size="30" color="#4285f4" />
          </view>
                </template>
                <template #loading>
                  <view class="avatar-placeholder">
                    <wd-icon name="store" size="30" color="#ddd" />
              </view>
                </template>
              </wd-img>
              <view v-else class="avatar-placeholder">
                <wd-icon name="store" size="30" color="#4285f4" />
              </view>
            </view>
            <view class="merchant-info">
              <text class="merchant-name">{{ merchantInfo.merchant_name }}</text>
              <text class="merchant-category" v-if="merchantInfo.category_name">
                {{ merchantInfo.category_name }}
              </text>
              </view>
            </view>
            
          <!-- 详细信息 -->
          <view class="merchant-details">
            <view class="detail-row">
              <view class="detail-item" v-if="merchantInfo.merchant_code">
                <text class="detail-label">商户编码</text>
                <text class="detail-value">{{ merchantInfo.merchant_code }}</text>
              </view>
              <view class="detail-item" v-if="merchantInfo.phone">
                <text class="detail-label">联系电话</text>
                <text class="detail-value">{{ merchantInfo.phone }}</text>
              </view>
            </view>
            
            <view class="detail-row" v-if="merchantInfo.address">
              <view class="detail-item detail-full">
                <text class="detail-label">经营地址</text>
                <text class="detail-value">{{ merchantInfo.address }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 权限编辑区域 -->
        <view class="permission-editor-section">
          <view class="section-header">
            <text class="section-title">权限配置</text>
            <view class="tree-actions">
              <wd-button
                type="success"
                size="small"
                @click="selectAllPermissions"
                :disabled="loading || saving"
              >
                全选
              </wd-button>
              <wd-button
                type="info"
                size="small"
                @click="clearAllPermissions"
                :disabled="loading || saving"
              >
                全不选
              </wd-button>
              <wd-button
                type="warning"
                size="small"
                @click="resetPermissions"
                :disabled="loading || saving"
              >
                重置
              </wd-button>
              <wd-button
                type="primary"
                size="small"
                @click="refreshTemplates"
                :loading="loading"
                :disabled="saving"
              >
                刷新
              </wd-button>
            </view>
          </view>

          <!-- 加载状态 -->
          <view v-if="loading" class="loading-state">
            <wd-loading />
            <text class="loading-text">加载权限模板中...</text>
          </view>

          <!-- 权限模板树 -->
          <view v-else-if="permissionTemplateTree.length > 0" class="permission-editor-tree">
            <!-- 一级节点 -->
            <view
              v-for="node in permissionTemplateTree"
              :key="node.id"
              class="permission-node-container level-1"
            >
              <!-- 一级节点内容 -->
              <view class="permission-node" @click="toggleExpand(node.id)">
                <view class="permission-node-info">
                  <view class="checkbox-container" @click.stop.prevent>
                    <wd-checkbox
                      v-model="node.checked"
                      :indeterminate="getIndeterminateState(node)"
                      @change="(checked) => handlePermissionCheck(node, checked)"
                      @click.stop.prevent
                      custom-class="permission-checkbox"
                      :disabled="saving"
                    />
                  </view>
                  <!-- 展开/收起图标 -->
                  <view
                    class="expand-icon"
                    v-if="node.children && node.children.length > 0"
                    @click.stop="toggleExpand(node.id)"
                  >
                    <wd-icon
                      :name="expandedPermissions.includes(node.id) ? 'arrow-down' : 'arrow-right'"
                      size="14"
                      custom-class="expand-toggle-icon"
                      :class="{ rotated: expandedPermissions.includes(node.id) }"
                    />
                  </view>
                  <!-- 节点图标 -->
                  <wd-icon
                    v-if="node.icon && node.icon.trim()"
                    :name="node.icon"
                    size="16"
                    custom-class="permission-node-icon"
                  />
                  <!-- 节点信息 -->
                  <view class="permission-main-info">
                    <view class="permission-title-row">
                      <view class="permission-title-group">
                        <text class="permission-node-title">{{ node.label }}</text>
                        <text
                          class="permission-node-badge"
                          :class="getPermissionTypeClass(node.permission_type)"
                        >
                          {{ getPermissionTypeText(node.permission_type) }}
                        </text>
                      </view>
                      <text class="permission-node-code">{{ node.permission_code }}</text>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 子节点容器 -->
              <view
                v-if="node.children && node.children.length > 0"
                class="permission-children level-2"
                :class="{ expanded: expandedPermissions.includes(node.id) }"
              >
                <!-- 二级节点 -->
                <view
                  v-for="child in node.children"
                  :key="child.id"
                  class="permission-node-container level-2"
                >
                  <!-- 二级节点内容 -->
                  <view class="permission-node" @click="toggleExpand(child.id)">
                    <view class="permission-node-info">
                      <view class="checkbox-container" @click.stop.prevent>
                        <wd-checkbox
                          v-model="child.checked"
                          :indeterminate="getIndeterminateState(child)"
                          @change="(checked) => handlePermissionCheck(child, checked)"
                          @click.stop.prevent
                          custom-class="permission-checkbox"
                          :disabled="saving"
                        />
                      </view>
                      <!-- 展开/收起图标 -->
                      <view
                        class="expand-icon"
                        v-if="child.children && child.children.length > 0"
                        @click.stop="toggleExpand(child.id)"
                      >
                        <wd-icon
                          :name="
                            expandedPermissions.includes(child.id) ? 'arrow-down' : 'arrow-right'
                          "
                          size="14"
                          custom-class="expand-toggle-icon"
                          :class="{ rotated: expandedPermissions.includes(child.id) }"
                        />
                      </view>
                      <!-- 节点图标 -->
                      <wd-icon
                        v-if="child.icon && child.icon.trim()"
                        :name="child.icon"
                        size="14"
                        custom-class="permission-node-icon"
                      />
                      <wd-icon v-else name="menu" size="14" custom-class="permission-node-icon" />
                      <!-- 节点信息 -->
                      <view class="permission-main-info">
                        <view class="permission-title-row">
                          <view class="permission-title-group">
                            <text class="permission-node-title">{{ child.label }}</text>
                            <text
                              class="permission-node-badge"
                              :class="getPermissionTypeClass(child.permission_type)"
                            >
                              {{ getPermissionTypeText(child.permission_type) }}
                            </text>
                          </view>
                          <text class="permission-node-code">{{ child.permission_code }}</text>
                        </view>
                      </view>
                    </view>
                  </view>

                  <!-- 三级节点容器 -->
                  <view
                    v-if="child.children && child.children.length > 0"
                    class="permission-children level-3"
                    :class="{ expanded: expandedPermissions.includes(child.id) }"
                  >
                    <!-- 三级节点 -->
                    <view
                      v-for="grandChild in child.children"
                      :key="grandChild.id"
                      class="permission-node-container level-3"
                    >
                      <!-- 三级节点内容 -->
                      <view class="permission-node">
                        <view class="permission-node-info">
                          <view class="checkbox-container" @click.stop.prevent>
                            <wd-checkbox
                              v-model="grandChild.checked"
                              @change="(checked) => handlePermissionCheck(grandChild, checked)"
                              @click.stop.prevent
                              custom-class="permission-checkbox"
                              :disabled="saving"
                            />
                          </view>
                          <!-- 节点图标 -->
                          <wd-icon
                            v-if="grandChild.icon && grandChild.icon.trim()"
                            :name="grandChild.icon"
                            size="12"
                            custom-class="permission-node-icon"
                          />
                          <!-- 节点信息 -->
                          <view class="permission-main-info">
                            <view class="permission-title-row">
                              <view class="permission-title-group">
                                <text class="permission-node-title">{{ grandChild.label }}</text>
                                <text
                                  class="permission-node-badge"
                                  :class="getPermissionTypeClass(grandChild.permission_type)"
                                >
                                  {{ getPermissionTypeText(grandChild.permission_type) }}
                                </text>
                              </view>
                              <text class="permission-node-code">
                                {{ grandChild.permission_code }}
                              </text>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <wd-status-tip v-else-if="!loading" type="search" tip="暂无权限模板数据" />
        </view>

        <!-- 备注输入 -->
        <view class="remark-section" v-if="!loading">
          <view class="section-header">
            <text class="section-title">备注说明</text>
          </view>
          <view class="remark-content">
            <wd-textarea
              v-model="remark"
              placeholder="请输入权限修改备注（可选）"
              :maxlength="200"
              :auto-height="true"
              show-word-limit
              :disabled="saving"
            />
          </view>
        </view>
      </view>

      <!-- 底部操作栏 -->
      <view class="bottom-actions" v-if="merchantInfo">
        <wd-button size="large" @click="handleCancel" :disabled="saving">取消</wd-button>
        <wd-button type="primary" size="large" @click="savePermissions" :loading="saving">
          保存权限
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  getMerchantAuthorizedPermissionTreeApi,
  getPermissionTemplateTreeForAuthorizeApi,
  batchAuthorizeMerchantPermissionsApi,
  revokeMerchantPermissionsApi,
  type IMerchantAuthorizedPermissionTreeResponse,
  type IMerchantAuthorizedPermissionTreeNode,
  type ISystemPermissionTemplateTreeNode,
  type IMerchantPermissionBatchAuthorizeRequest,
  type IMerchantPermissionRevokeRequest,
} from '@/api/sys/systemMerchantAuthorizedPermissionApi'
import { getSystemMerchantDetailApi } from '@/api/sys/systemMerchantApi'

defineOptions({
  name: 'EditMerchantPermission',
})

// 权限模板节点数据类型
interface PermissionTemplateNode {
  id: string
  label: string
  permission_code: string
  parent_id?: string
  permission_type: number
  visible: number
  icon?: string
  order_num: number
  checked: boolean
  children: PermissionTemplateNode[]
}

// 权限节点数据类型（当前权限）
interface PermissionNode {
  id: string
  permission_name: string
  permission_code: string
  permission_type: number
  permission_type_desc: string
  parent_id?: string
  order_num: number
  is_authorized: boolean
  authorized_permission_id?: string
  authorized_date?: string
  status?: number
  status_desc?: string
  children: PermissionNode[]
}

// 商户权限树响应类型
interface MerchantPermissionTreeResponse {
  merchant_id: number
  merchant_name: string
  tree: PermissionNode[]
}

// 页面参数
const merchantId = ref<number>()
const merchantName = ref<string>('')

// 页面状态
const loading = ref(false)
const saving = ref(false)
const permissionTemplateTree = ref<PermissionTemplateNode[]>([])
const expandedPermissions = ref<string[]>([])
const remark = ref<string>('')

// 商户信息
const merchantInfo = ref<{
  merchant_id: number
  merchant_name: string
  phone?: string
  email?: string
  address?: string
  merchant_code?: string
  category_name?: string
  avatar?: string
} | null>(null)

// 原始授权权限集合（用于重置）
const originalAuthorizedPermissions = ref<Set<string>>(new Set())

// 页面标题
const pageTitle = computed(() => {
  return '修改商户授权'
})

// 获取权限模板树
const fetchPermissionTemplateTree = async () => {
  loading.value = true
  try {
    const result = await getPermissionTemplateTreeForAuthorizeApi()

    if (result.code === 200 && result.data) {
      // 转换数据类型，添加checked属性
      permissionTemplateTree.value = convertToPermissionTemplateNodes(result.data)

      // 初始化展开状态
      initExpandedNodes(permissionTemplateTree.value)

      console.log('获取权限模板树成功:', result.data)
    } else {
      throw new Error(result.message || '获取权限模板失败')
    }
  } catch (error) {
    console.error('获取权限模板树失败:', error)
    uni.showToast({
      title: '获取权限模板失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 转换API返回的数据类型为本地类型
const convertToPermissionTemplateNodes = (
  apiNodes: ISystemPermissionTemplateTreeNode[],
): PermissionTemplateNode[] => {
  return apiNodes.map((node) => ({
    id: node.id,
    label: node.label,
    permission_code: node.permission_code,
    parent_id: node.parent_id,
    permission_type: node.permission_type,
    visible: node.visible,
    icon: node.icon,
    order_num: node.order_num,
    checked: false,
    children: convertToPermissionTemplateNodes(node.children),
  }))
}

// 获取商户详细信息
const fetchMerchantDetail = async () => {
  if (!merchantId.value) return

  try {
    const result = await getSystemMerchantDetailApi(merchantId.value)
    
    if (result.code === 200 && result.data) {
      // 更新商户信息，包含详细字段
      merchantInfo.value = {
        merchant_id: result.data.id,
        merchant_name: result.data.merchant_name,
        phone: result.data.phone,
        email: result.data.email,
        address: result.data.address,
        merchant_code: result.data.merchant_code,
        category_name: result.data.category_name,
        avatar: result.data.avatar, // 添加头像字段
      }
      
      console.log('获取商户详情成功:', result.data)
    } else {
      throw new Error(result.message || '获取商户详情失败')
    }
  } catch (error) {
    console.error('获取商户详情失败:', error)
    // 如果获取详情失败，保持基本信息
    if (!merchantInfo.value) {
      merchantInfo.value = {
        merchant_id: merchantId.value,
        merchant_name: merchantName.value,
      }
    }
  }
}

// 获取商户当前权限
const fetchMerchantCurrentPermissions = async () => {
  if (!merchantId.value) return

  try {
    const result = await getMerchantAuthorizedPermissionTreeApi(merchantId.value)

    if (result.code === 200 && result.data) {
      // 如果还没有商户基本信息，先设置基本信息
      if (!merchantInfo.value) {
        merchantInfo.value = {
          merchant_id: result.data.merchant_id,
          merchant_name: result.data.merchant_name,
        }
      }
      
      // 收集当前已授权的权限ID
      const authorizedIds = new Set<string>()
      const collectAuthorizedIds = (nodes: PermissionNode[]) => {
        nodes.forEach((node) => {
          if (node.is_authorized) {
            authorizedIds.add(node.id)
          }
          if (node.children.length > 0) {
            collectAuthorizedIds(node.children)
          }
        })
      }
      collectAuthorizedIds(result.data.tree)
      
      originalAuthorizedPermissions.value = new Set(authorizedIds)
      
      // 设置模板树的选中状态
      setTemplateTreeCheckedStatus(authorizedIds)
      
      console.log('获取商户当前权限成功:', result.data)
    } else {
      throw new Error(result.message || '获取商户权限失败')
    }
  } catch (error) {
    console.error('获取商户当前权限失败:', error)
    uni.showToast({
      title: '获取商户权限失败',
      icon: 'none',
    })
  }
}

// 设置模板树的选中状态
const setTemplateTreeCheckedStatus = (authorizedIds: Set<string>) => {
  const setCheckedStatus = (nodes: PermissionTemplateNode[]) => {
    nodes.forEach((node) => {
      node.checked = authorizedIds.has(node.id)
      if (node.children.length > 0) {
        setCheckedStatus(node.children)
      }
    })
  }
  setCheckedStatus(permissionTemplateTree.value)
}

// 初始化展开状态
const initExpandedNodes = (nodes: PermissionTemplateNode[]) => {
  // 移除默认展开第一层的逻辑，保持收起状态
  expandedPermissions.value = []
}

// 切换节点展开状态
const toggleExpand = (nodeId: string) => {
  const index = expandedPermissions.value.indexOf(nodeId)
  if (index > -1) {
    expandedPermissions.value.splice(index, 1)
  } else {
    expandedPermissions.value.push(nodeId)
  }
}

// 获取节点的半选状态
const getIndeterminateState = (node: PermissionTemplateNode) => {
  if (!node.children || node.children.length === 0) return false
  if (node.checked) return false

  const checkedChildren = node.children.filter((child) => child.checked)
  const hasCheckedGrandChildren = node.children.some((child) => getIndeterminateState(child))

  return checkedChildren.length > 0 || hasCheckedGrandChildren
}

// 权限类型相关函数
const getPermissionTypeText = (type: number) => {
  switch (type) {
    case 1:
      return '目录'
    case 2:
      return '菜单'
    case 3:
      return '按钮'
    default:
      return '未知'
  }
}

const getPermissionTypeClass = (type: number) => {
  switch (type) {
    case 1:
      return 'type-directory'
    case 2:
      return 'type-menu'
    case 3:
      return 'type-button'
    default:
      return ''
  }
}

// 递归获取所有子节点ID
const getAllChildrenIds = (node: PermissionTemplateNode): string[] => {
  const ids: string[] = []
  if (node.children && node.children.length > 0) {
    node.children.forEach((child) => {
      ids.push(child.id)
      ids.push(...getAllChildrenIds(child))
    })
  }
  return ids
}

// 查找父节点
const findParentNode = (
  targetId: string,
  nodes: PermissionTemplateNode[],
): PermissionTemplateNode | null => {
  for (const node of nodes) {
    if (node.children && node.children.some((child) => child.id === targetId)) {
      return node
    }
    if (node.children && node.children.length > 0) {
      const found = findParentNode(targetId, node.children)
      if (found) return found
    }
  }
  return null
}

// 处理权限选择
const handlePermissionCheck = (
  node: PermissionTemplateNode,
  checked: boolean | { value: boolean },
) => {
  const isChecked = typeof checked === 'boolean' ? checked : checked.value

  // 更新当前节点
  node.checked = isChecked

  // 如果选中父节点，自动选中所有子节点
  if (isChecked) {
    const selectChildren = (nodeItem: PermissionTemplateNode) => {
      nodeItem.checked = true
      nodeItem.children.forEach((child) => {
        selectChildren(child)
      })
    }
    selectChildren(node)
  } else {
    // 如果取消选中父节点，自动取消选中所有子节点
    const unselectChildren = (nodeItem: PermissionTemplateNode) => {
      nodeItem.checked = false
      nodeItem.children.forEach((child) => {
        unselectChildren(child)
      })
    }
    unselectChildren(node)
  }

  // 更新父节点状态
  updateParentCheckStatus(permissionTemplateTree.value, node)
}

// 更新父节点选中状态
const updateParentCheckStatus = (
  nodes: PermissionTemplateNode[],
  targetNode: PermissionTemplateNode,
) => {
  const parentNode = findParentNode(targetNode.id, nodes)
  if (parentNode) {
    // 如果所有子节点都选中，则选中父节点
    const allChildrenChecked = parentNode.children.every((child) => child.checked)
    parentNode.checked = allChildrenChecked

    // 递归更新上级父节点
    updateParentCheckStatus(nodes, parentNode)
  }
}

// 全选权限
const selectAllPermissions = () => {
  const selectAll = (nodes: PermissionTemplateNode[]) => {
    nodes.forEach((node) => {
      node.checked = true
      if (node.children.length > 0) {
        selectAll(node.children)
      }
    })
  }
  selectAll(permissionTemplateTree.value)
}

// 全不选权限
const clearAllPermissions = () => {
  const clearAll = (nodes: PermissionTemplateNode[]) => {
    nodes.forEach((node) => {
      node.checked = false
      if (node.children.length > 0) {
        clearAll(node.children)
      }
    })
  }
  clearAll(permissionTemplateTree.value)
}

// 重置权限选择
const resetPermissions = () => {
  // 恢复到原始状态
  const resetToOriginal = (nodes: PermissionTemplateNode[]) => {
    nodes.forEach((node) => {
      node.checked = originalAuthorizedPermissions.value.has(node.id)
      if (node.children.length > 0) {
        resetToOriginal(node.children)
      }
    })
  }
  resetToOriginal(permissionTemplateTree.value)
}

// 刷新权限模板
const refreshTemplates = async () => {
  await Promise.all([
    fetchPermissionTemplateTree(),
    fetchMerchantDetail(),
    fetchMerchantCurrentPermissions()
  ])
}

// 保存权限更改
const savePermissions = async () => {
  if (!merchantId.value) {
    uni.showToast({
      title: '缺少商户ID',
      icon: 'none',
    })
    return
  }

  // 收集当前选中的权限ID
  const currentSelectedIds = new Set<string>()
  const collectSelectedIds = (nodes: PermissionTemplateNode[]) => {
    nodes.forEach((node) => {
      if (node.checked) {
        currentSelectedIds.add(node.id)
      }
      if (node.children.length > 0) {
        collectSelectedIds(node.children)
      }
    })
  }
  collectSelectedIds(permissionTemplateTree.value)

  // 分析权限变化
  const toAuthorize: string[] = [] // 需要授权的权限
  const toRevoke: string[] = [] // 需要撤销的权限

  // 找出需要新授权的权限（当前选中但原来没有的）
  currentSelectedIds.forEach((id) => {
    if (!originalAuthorizedPermissions.value.has(id)) {
      toAuthorize.push(id)
    }
  })

  // 找出需要撤销的权限（原来有但当前没选中的）
  originalAuthorizedPermissions.value.forEach((id) => {
    if (!currentSelectedIds.has(id)) {
      toRevoke.push(id)
    }
  })

  console.log('权限变化分析:', {
    merchant_id: merchantId.value,
    currentSelectedIds: Array.from(currentSelectedIds),
    originalAuthorizedPermissions: Array.from(originalAuthorizedPermissions.value),
    toAuthorize,
    toRevoke,
    remark: remark.value,
  })

  // 如果没有变化，直接返回
  if (toAuthorize.length === 0 && toRevoke.length === 0) {
    uni.showToast({
      title: '权限配置无变化',
      icon: 'none',
    })
    return
  }

  // 构建确认信息
  let confirmContent = `确定要为商户"${merchantName.value}"保存权限配置吗？\n\n`
  confirmContent += `将设置 ${currentSelectedIds.size} 项权限为已授权状态\n`
  if (toAuthorize.length > 0) {
    confirmContent += `其中新增授权 ${toAuthorize.length} 项权限\n`
  }
  if (toRevoke.length > 0) {
    confirmContent += `撤销 ${toRevoke.length} 项权限\n`
  }

  // 确认操作
  const confirmResult = await new Promise<boolean>((resolve) => {
    uni.showModal({
      title: '确认保存',
      content: confirmContent,
      success: (res) => {
        resolve(res.confirm)
      },
    })
  })

  if (!confirmResult) return

  saving.value = true
  try {
    // 直接发送所有当前选中的权限进行覆盖，确保权限状态完全一致
    const authorizeRequest: IMerchantPermissionBatchAuthorizeRequest = {
      merchant_id: merchantId.value,
      permission_template_ids: Array.from(currentSelectedIds), // 发送所有当前选中的权限
      override_existing: true,
      remark: remark.value || undefined,
    }
    
    const result = await batchAuthorizeMerchantPermissionsApi(authorizeRequest)
    
    const allSuccess = result.code === 200

    if (allSuccess) {
      // 更新原始权限集合为当前选中的权限
      originalAuthorizedPermissions.value = new Set(currentSelectedIds)
      
      uni.showToast({
        title: '权限保存成功',
        icon: 'success',
      })

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      throw new Error('权限操作失败')
    }
  } catch (error) {
    console.error('保存权限失败:', error)
    uni.showToast({
      title: '权限保存失败',
      icon: 'none',
    })
  } finally {
    saving.value = false
  }
}

// 取消操作
const handleCancel = () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消权限修改吗？未保存的更改将丢失。',
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack()
      }
    },
  })
}

// 返回功能
const handleBack = () => {
  handleCancel()
}

// 页面初始化
onMounted(async () => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}

  merchantId.value = options.merchantId ? parseInt(options.merchantId) : undefined
  merchantName.value = options.merchantName ? decodeURIComponent(options.merchantName) : ''

  console.log('页面参数:', { merchantId: merchantId.value, merchantName: merchantName.value })

  if (merchantId.value) {
    // 并行获取权限模板、商户详情和当前权限
    await Promise.all([
      fetchPermissionTemplateTree(), 
      fetchMerchantDetail(),
      fetchMerchantCurrentPermissions()
    ])
  } else {
    uni.showToast({
      title: '缺少商户ID参数',
      icon: 'none',
    })
  }
})
</script>

<style lang="scss" scoped>
.edit-permission-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 18px !important;
  font-weight: bold !important;
}

:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-layout {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  padding-bottom: 120px;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

// 商户信息区域
.merchant-info-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.merchant-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.merchant-avatar {
  margin-right: 12px;
  flex-shrink: 0;
}

.avatar-img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4285f4;
}

.merchant-info {
  flex: 1;
  min-width: 0;
}

.merchant-name {
  font-size: 16px;
  font-weight: 700;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.merchant-category {
  font-size: 13px;
  color: #666;
  background-color: #f0f0f0;
  padding: 2px 8px;
  border-radius: 6px;
  display: inline-block;
}

.merchant-details {
  padding-left: 0;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.detail-item {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.detail-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
    font-weight: 500;
  }
  
.detail-value {
    font-size: 14px;
    color: #333;
    word-break: break-all;
    line-height: 1.4;
  }

.detail-full {
  flex: 1;
}

// 权限编辑区域
.permission-editor-section {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.tree-actions {
  display: flex;
  gap: 8px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 20px;
  gap: 16px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.permission-editor-tree {
  padding: 0;
}

// 权限节点样式
.permission-node-container {
  margin-bottom: 2px;

  &.level-1 {
    background-color: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    margin-bottom: 4px;
    margin-left: 0;
    margin-right: 0;
  }

  &.level-2 {
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
  }

  &.level-3 {
    background-color: #fbfbfb;
    border-bottom: 1px solid #f0f0f0;
    margin: 1px 0;
  }
}

.permission-node {
  display: flex;
  flex-direction: column;
  padding: 10px 12px;
  transition: background-color 0.2s ease;
}

.permission-node-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.permission-main-info {
  flex: 1;
  min-width: 0;
}

.permission-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.permission-title-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.permission-meta-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.checkbox-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 20px;
  margin-right: 8px;
  margin-top: 2px;
}

:deep(.permission-checkbox) {
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 0 !important;
}

.permission-node-icon {
  color: #666;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  margin-top: 0;
}

.permission-node-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.permission-node-code {
  font-size: 12px;
  color: #666;
  font-family: monospace;
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  flex-shrink: 0;
}

.expand-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  margin-right: 4px;
  margin-top: 0;

  &:hover {
    background-color: rgba(66, 133, 244, 0.1);
  }
}

:deep(.expand-toggle-icon) {
  color: #4285f4 !important;
  transition: transform 0.3s ease !important;
}

.permission-children {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.3s ease;
  padding-left: 20px;
  border-left: 2px solid #e0e0e0;
  margin-left: 16px;

  &.expanded {
    max-height: 2000px; // 足够大的值来容纳所有内容
    opacity: 1;
  }

  &.level-3 {
    margin-top: 4px;
    padding-left: 16px;
  }
}

.permission-node-badge {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  white-space: nowrap;
  flex-shrink: 0;
  display: flex;
  align-items: center;

  &.type-directory {
    background-color: #fff3e0;
    color: #e65100;
  }

  &.type-menu {
    background-color: #e3f2fd;
    color: #1976d2;
  }

  &.type-button {
    background-color: #e8f5e8;
    color: #388e3c;
  }
}

.permission-node-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding-left: 32px;
}

// 备注区域
.remark-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.remark-content {
  padding: 16px 0 0 0;
}

// 底部操作栏
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 12px 20px;
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

.bottom-actions .wd-button {
  min-width: 80px;
}
</style> 