<template>
  <view class="sidebar-container">
    <!-- 如果有子菜单，显示子菜单列表 -->
    <view v-if="sidebarMenus.length > 0" class="menu-list">
      <view
        v-for="item in sidebarMenus"
        :key="item.id"
        class="menu-item"
        :class="{ 'active': isCurrentPath(item.path) }"
        @click="handleMenuClick(item)"
      >
        <view class="menu-content">
          <view class="menu-icon">
            <!-- 使用wot组件图标 - 没有特殊前缀的图标 -->
            <wd-icon
              v-if="!item.icon?.startsWith('iconsys')"
              :name="item.icon || 'setting'"
              size="14px"
              :color="isCurrentPath(item.path) ? '#4285f4' : '#666'"
            />
            <!-- 使用自定义图标 - iconsys开头的图标 -->
            <view
              v-else
              class="iconfont-sys"
              :class="`${item.icon}`"
              :style="{
                fontSize: '14px',
                color: isCurrentPath(item.path) ? '#4285f4' : '#666',
              }"
            ></view>
          </view>
          <text class="menu-title" :class="{ 'active-text': isCurrentPath(item.path) }">
            {{ item.title }}
          </text>
        </view>

        <!-- 权限角标 -->
        <view v-if="!hasMenuAccess(item)" class="permission-badge">
          <wd-icon name="lock" size="12px" color="#ccc" />
        </view>
      </view>
    </view>

    <!-- 没有子菜单时显示提示 -->
    <view v-else class="empty-menu">
      <text class="empty-text">暂无可访问的功能模块</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { computed, onMounted, watch } from 'vue'
import { useMenuPermissionStore } from '../../../store/menuPermission'
import { useSystemAdminStore } from '../../../store/systemAdmin'

defineOptions({
  name: 'SysSidebar',
})

// 组件属性
const props = defineProps({
  /** 当前页面的完整路径，用于高亮显示 */
  currentPath: {
    type: String,
    required: true,
  },
  /** TabBar路径，用于获取对应的侧边栏菜单 */
  tabBarPath: {
    type: String,
    default: '/pages-sys/system/index', // 默认为系统管理
  },
})

// Store
const menuPermissionStore = useMenuPermissionStore()
const systemAdminStore = useSystemAdminStore()

// 获取侧边栏菜单数据
const sidebarMenus = computed(() => {
  console.log('=== SysSidebar 菜单计算 ===')
  console.log('当前页面路径:', props.currentPath)
  console.log('TabBar路径:', props.tabBarPath)
  console.log('原始菜单数据长度:', menuPermissionStore.rawMenus.length)

  const menus = menuPermissionStore.getSidebarMenus(props.tabBarPath)
  console.log('获取到的侧边栏菜单:', menus)

  const filteredMenus = menus.filter(menu => {
    const notHidden = !menu.hidden
    const hasAccess = hasMenuAccess(menu)
    console.log(`菜单 ${menu.title}: hidden=${menu.hidden}, hasAccess=${hasAccess}`)
    return notHidden && hasAccess
  })

  console.log('过滤后的菜单数量:', filteredMenus.length)
  console.log('=== SysSidebar 菜单计算结束 ===')

  return filteredMenus
})

// 检查是否是当前路径
const isCurrentPath = (path: string): boolean => {
  // 精确匹配当前路径
  return props.currentPath === path
}

// 检查菜单访问权限
const hasMenuAccess = (item: any): boolean => {
  // 如果菜单没有权限要求，直接允许访问
  if (!item.perms) return true

  // 检查用户是否有该菜单的权限
  return systemAdminStore.hasPermission(item.perms)
}

// 页面跳转状态
let isJumping = false

// 重置跳转状态
const resetJumpingState = () => {
  isJumping = false
  console.log('SysSidebar跳转状态已重置')
}

// 处理菜单点击
const handleMenuClick = (item: any) => {
  console.log('SysSidebar菜单点击:', item)

  // 检查权限
  if (!hasMenuAccess(item)) {
    uni.showToast({
      title: '没有访问权限',
      icon: 'none',
    })
    return
  }

  // 如果点击的是当前菜单，不处理
  if (isCurrentPath(item.path)) {
    console.log('点击当前菜单，不跳转')
    return
  }

  // 防止快速连续点击
  if (isJumping) {
    console.log('SysSidebar正在跳转中，忽略点击')
    return
  }

  // 使用 redirectTo 进行页面切换，避免页面栈累积
  isJumping = true
  console.log('SysSidebar开始跳转到:', item.path)

  uni.redirectTo({
    url: item.path,
    success: () => {
      console.log('SysSidebar跳转成功:', item.path)
      resetJumpingState()
    },
    fail: (err) => {
      console.log('redirectTo失败，尝试reLaunch:', err)
      // 如果 redirectTo 失败，使用 reLaunch 作为备选
      uni.reLaunch({
        url: item.path,
        success: () => {
          console.log('SysSidebar reLaunch跳转成功:', item.path)
          resetJumpingState()
        },
        fail: (launchErr) => {
          console.error('SysSidebar所有跳转方式都失败:', launchErr)
          resetJumpingState()
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          })
        }
      })
    }
  })
}

// 监听菜单数据变化
watch(
  () => menuPermissionStore.rawMenus.length,
  (newLength) => {
    console.log('SysSidebar监听到菜单数据变化，长度:', newLength)
  }
)

// 组件挂载时的初始化
onMounted(() => {
  console.log('=== SysSidebar组件挂载 ===')
  console.log('当前路径:', props.currentPath)
  console.log('TabBar路径:', props.tabBarPath)
  console.log('菜单数据长度:', menuPermissionStore.rawMenus.length)

  // 如果没有菜单数据，主动获取
  if (menuPermissionStore.rawMenus.length === 0) {
    console.log('SysSidebar菜单数据为空，主动获取...')
    menuPermissionStore.fetchUserMenus()
  }

  console.log('=== SysSidebar组件挂载完成 ===')
})
</script>

<style scoped lang="scss">
.sidebar-container {
  height: 100%;
  background-color: transparent;
}

.menu-list {
  padding: 8px 0;
}

.menu-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 8px;
  margin: 1px 0;
  transition: all 0.2s;

  &:hover {
    background-color: #f0f2f5;
  }

  &.active {
    background-color: #e6f3ff;
    border-left: 3px solid #4285f4;
    color: #4285f4;
  }
}

.menu-content {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.menu-icon {
  margin-right: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

.menu-title {
  font-size: 13px;
  color: #333;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &.active-text {
    color: #4285f4;
    font-weight: 500;
  }
}

.permission-badge {
  opacity: 0.5;
}

.empty-menu {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  padding: 20px;
}

.empty-text {
  color: #999;
  font-size: 12px;
  text-align: center;
  line-height: 1.4;
}
</style>
