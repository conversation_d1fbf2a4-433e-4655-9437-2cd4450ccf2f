<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '角色管理',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="role-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      title="角色管理"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <view class="main-layout">
        <!-- 角色管理页面内容 -->
        <view class="role-management">
          <!-- 操作栏 -->
          <view class="action-bar">
            <!-- 搜索条件栏 -->
            <view class="search-bar">
              <view class="search-form">
                <!-- 第一行搜索条件 -->
                <view class="search-row">
                  <view class="search-item">
                    <text class="search-label">角色名称：</text>
                    <wd-input
                      class="search-input-component"
                      placeholder="请输入角色名称"
                      v-model="searchParams.name"
                      clearable
                      @clear="handleSearch"
                    />
                  </view>
                </view>

                <!-- 第二行搜索条件 -->
                <view class="search-row">
                  <view class="search-item">
                    <text class="search-label">创建时间：</text>
                    <wd-datetime-picker
                      v-model="timePickerValues.created_start"
                      type="date"
                      placeholder="开始时间"
                      :default-value="getDefaultStartDate()"
                      :min-date="getMinDate()"
                      :max-date="getMaxDate()"
                      clearable
                      @confirm="handleSearch"
                      @clear="handleSearch"
                      size="small"
                      custom-class="datetime-picker-custom"
                      custom-style="width: 120px; height: 28px;"
                    />
                  </view>
                  <view class="search-item">
                    <text class="search-label">至：</text>
                    <wd-datetime-picker
                      v-model="timePickerValues.created_end"
                      type="date"
                      placeholder="结束时间"
                      :default-value="getDefaultEndDate()"
                      :min-date="getMinDate()"
                      :max-date="getMaxDate()"
                      clearable
                      @confirm="handleSearch"
                      @clear="handleSearch"
                      size="small"
                      custom-class="datetime-picker-custom"
                      custom-style="width: 120px; height: 28px;"
                    />
                  </view>
                </view>

                <!-- 按钮行 -->
                <view class="button-row">
                  <view class="left-buttons">
                    <wd-button type="success" size="small" @click="addRole">新增</wd-button>
                    <wd-button
                      type="info"
                      size="small"
                      icon="refresh"
                      @click="refreshData"
                    ></wd-button>
                  </view>
                  <view class="right-buttons">
                    <wd-button type="primary" size="small" @click="handleSearch">搜索</wd-button>
                    <wd-button plain size="small" @click="resetSearch">重置</wd-button>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 角色列表 -->
          <view class="role-list">
            <wd-table
              :data="roles"
              :stripe="true"
              :border="true"
              :height="540"
            >
              <!-- 角色名称列 -->
              <wd-table-col
                prop="name"
                label="角色名称"
                :width="160"
                sortable
                fixed
              ></wd-table-col>

              <!-- 角色描述列 -->
              <wd-table-col prop="description" label="角色描述" min-width="240"></wd-table-col>

              <!-- 用户数量列 -->
              <wd-table-col prop="user_count" label="用户数量" :width="100" align="center">
                <template #value="{ row }">
                  <text class="count-tag">{{ row.user_count }}</text>
                </template>
              </wd-table-col>

              <!-- 权限数量列 -->
              <wd-table-col prop="permission_count" label="权限数量" :width="100" align="center">
                <template #value="{ row }">
                  <text class="count-tag">{{ row.permission_count }}</text>
                </template>
              </wd-table-col>

              <!-- 操作列 -->
              <wd-table-col prop="actions" label="操作" :width="240" align="center">
                <template #value="{ row }">
                  <view class="action-buttons">
                    <wd-button type="primary" size="small" @click.stop="viewRole(row)">
                      详细
                    </wd-button>
                    <wd-button type="warning" size="small" @click.stop="editRole(row)">
                      编辑
                    </wd-button>
                    <wd-button
                      type="error"
                      size="small"
                      @click.stop="deleteRole(row)"
                      v-if="row.name !== '系统管理员'"
                    >
                      删除
                    </wd-button>
                  </view>
                </template>
              </wd-table-col>
            </wd-table>

            <!-- 分页组件 -->
            <view class="pagination-wrapper">
              <wd-pagination
                v-model="currentPage"
                :total="total"
                :page-size="pageSize"
                @change="handlePageChange"
                show-icon
                show-message
              />
            </view>

            <!-- 空状态 -->
            <wd-status-tip
              v-if="roles.length === 0 && !loading"
              type="search"
              tip="暂无角色数据"
            />

            <!-- 加载状态 -->
            <view v-if="loading" class="loading-state">
              <text class="loading-text">加载中...</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import {
  getSystemRolesApi,
  getSystemRoleDetailApi,
  createSystemRoleApi,
  updateSystemRoleApi,
  deleteSystemRoleApi,
  type ISysRolePageRequest,
  type ISysRoleListResponse,
  type ISysRoleDetailResponse,
  type ISysRoleCreateRequest,
  type ISysRoleUpdateRequest,
} from '@/api/sys/systemRoleApi'

defineOptions({
  name: 'RoleManagement',
})

// 数据状态
const roles = ref<ISysRoleListResponse[]>([])
const total = ref(0)
const loading = ref(false)

// 分页相关状态
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索参数
const searchParams = reactive<ISysRolePageRequest>({
  page: 1,
  page_size: 10,
  name: null,
  created_start: null,
  created_end: null,
})

// 时间选择器绑定的数据（时间戳类型）
const timePickerValues = reactive({
  created_start: null as number | null,
  created_end: null as number | null,
})

// 表单 ref
const roleFormRef = ref()

// 时间格式转换函数
const formatDateToString = (timestamp: number | null) => {
  if (!timestamp) return null
  return new Date(timestamp).toISOString().split('T')[0]
}

// 监听时间选择器变化，同步到搜索参数
watch(() => timePickerValues.created_start, (newVal) => {
  searchParams.created_start = formatDateToString(newVal)
})

watch(() => timePickerValues.created_end, (newVal) => {
  searchParams.created_end = formatDateToString(newVal)
})

// 时间选择器相关函数
const getMinDate = () => {
  // 最小日期：当前日期 - 2年
  const now = new Date()
  const minDate = new Date(now.getFullYear() - 2, now.getMonth(), now.getDate())
  return minDate.getTime()
}

const getMaxDate = () => {
  // 最大日期：当前日期
  const now = new Date()
  return now.getTime()
}

const getDefaultStartDate = () => {
  // 默认开始时间：当前日期 - 30天
  const now = new Date()
  const defaultStart = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
  return defaultStart.getTime()
}

const getDefaultEndDate = () => {
  // 默认结束时间：当前日期
  const now = new Date()
  return now.getTime()
}

// 加载角色列表
const loadRoles = async () => {
  try {
    loading.value = true

    const params: ISysRolePageRequest = {
      page: currentPage.value,
      page_size: pageSize.value,
      name: searchParams.name || null,
      created_start: searchParams.created_start,
      created_end: searchParams.created_end,
    }

    const response = await getSystemRolesApi(params)
    roles.value = response.data.items
    total.value = response.data.total
  } catch (error) {
    console.error('加载角色列表失败:', error)
    uni.showToast({
      title: '加载角色列表失败',
      icon: 'error',
    })
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  loadRoles()
}

// 重置搜索
const resetSearch = () => {
  searchParams.name = null
  searchParams.created_start = null
  searchParams.created_end = null
  timePickerValues.created_start = null
  timePickerValues.created_end = null
  currentPage.value = 1
  loadRoles()
}

// 处理分页变化
const handlePageChange = ({ value }: { value: number }) => {
  currentPage.value = value
  loadRoles()
}

// 刷新数据
const refreshData = () => {
  loadRoles()
  uni.showToast({
    title: '数据已刷新',
    icon: 'success',
  })
}

// 新增角色
const addRole = () => {
  uni.navigateTo({
    url: '/pages-sys/system/role/role-form?mode=add'
  })
}

// 编辑角色
const editRole = (role: ISysRoleListResponse) => {
  uni.navigateTo({
    url: `/pages-sys/system/role/role-form?mode=edit&roleId=${role.id}`
  })
}

// 查看角色详情
const viewRole = async (role: ISysRoleListResponse) => {
  uni.navigateTo({
    url: `/pages-sys/system/role/role-form?mode=view&roleId=${role.id}`
  })
}

// 删除角色
const deleteRole = (role: ISysRoleListResponse) => {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除角色 "${role.name}" 吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          await deleteSystemRoleApi(role.id)
          uni.showToast({
            title: '删除成功',
            icon: 'success',
          })
          loadRoles()
        } catch (error) {
          console.error('删除角色失败:', error)
          uni.showToast({
            title: '删除角色失败',
            icon: 'error',
          })
        }
      }
    },
  })
}

// 返回功能
const handleBack = () => {
  uni.navigateBack()
}

onMounted(() => {
  loadRoles()
})
</script>

<style lang="scss" scoped>
.role-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

// 标题文字设为白色
:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

// 自定义返回按钮样式
:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  background-color: transparent;
  overflow: hidden;
  position: relative;
}

.main-layout {
  flex: 1;
  background-color: white;
  padding: 20px;
}

.role-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.action-bar {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
  padding: 0 0 15px 0;
  border-bottom: 1px solid #eee;
}

.top-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-bar {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
}

.search-row {
  display: flex;
  gap: 20px;
  align-items: flex-end;
}

.search-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1px;
}

.search-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
  min-width: 45px;
  line-height: 28px;
}

.search-input-component {
  width: 120px;
  height: 28px;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  background-color: white !important;
  overflow: hidden;
}

:deep(.search-input-component .wd-input) {
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

:deep(.search-input-component .wd-input__inner) {
  padding: 6px 10px;
  font-size: 12px;
  height: 28px;
  box-sizing: border-box;
  border: none;
  background-color: transparent;
}

:deep(.search-input-component .wd-input:focus-within) {
  border-color: #4285f4;
}

:deep(.search-input-component .wd-input__inner:focus) {
  outline: none;
}


.picker-text {
  font-size: 12px;
}

.button-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.right-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.role-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
}

// 表格容器样式优化
:deep(.wd-table) {
  width: 100% !important;
  min-width: 100% !important;
}

:deep(.wd-table__wrapper) {
  width: 100% !important;
  overflow-x: auto;
}

// 表格行样式 - 使用更通用的选择器避免tr标签选择器
:deep(.wd-table .wd-table__body .wd-table__row) {
  transition: background-color 0.2s;
}

:deep(.wd-table .wd-table__body .wd-table__row:hover) {
  background-color: #f5f7fa !important;
}

.pagination-wrapper {
  padding: 15px 0;
}

.count-tag {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  white-space: nowrap;
}

.action-buttons {
  display: flex;
  gap: 6px;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;

  .wd-button {
    min-width: 60px;
    white-space: nowrap;
  }
}

.role-dialog {
  padding: 20px;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.dialog-title {
  font-size: 18px;
  font-weight: bold;
}

.dialog-content {
  margin-bottom: 20px;
}

.form-item {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
}

.loading-state {
  text-align: center;
  padding: 60px 20px;
}

.loading-text {
  color: #999;
  font-size: 14px;
}

.detail-dialog {
  padding: 20px;
}

.detail-item {
  margin-bottom: 10px;
}

.detail-label {
  font-weight: bold;
}

.detail-value {
  margin-left: 10px;
}

.permissions-list {
  margin-top: 10px;
}

.permission-item {
  margin-bottom: 5px;
}

.permission-name {
  margin-right: 10px;
}

.permission-type {
  color: #999;
}

.no-permissions {
  color: #999;
}

/* 时间选择器对齐样式 */
:deep(.datetime-picker-custom) {
  display: flex;
  align-items: center;
  height: 28px;
  border-radius: 4px;
  background-color: white;
}

:deep(.datetime-picker-custom .wd-datetime-picker__inner) {
  height: 28px;
  line-height: 28px;
  padding: 0 10px;
  font-size: 12px;
  border: none;
  background: transparent;
}
</style>
