<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '用户详情',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="user-detail-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      :title="pageTitle"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <view class="main-layout">
        <!-- 加载状态 -->
        <view v-if="loading" class="loading-state">
          <wd-loading />
          <text class="loading-text">加载用户详情中...</text>
        </view>

        <!-- 用户详情内容 -->
        <view v-else-if="userDetail" class="detail-content">
          <!-- 用户头像和基本信息 -->
          <view class="profile-section">
            <view class="avatar-container">
              <wd-img
                v-if="userDetail.avatar"
                :src="userDetail.avatar"
                :width="80"
                :height="80"
                round
                mode="aspectFill"
                custom-class="user-avatar"
                enable-preview
              >
                <template #error>
                  <view class="avatar-error">
                    <wd-icon name="user-circle" size="48" color="#ccc" />
                  </view>
                </template>
                <template #loading>
                  <view class="avatar-loading">
                    <wd-loading size="20" />
                  </view>
                </template>
              </wd-img>
              <view v-else class="default-avatar">
                <wd-icon name="user-circle" size="48" color="#ccc" />
              </view>
            </view>
            <view class="profile-info">
              <text class="username">{{ userDetail.username }}</text>
              <text class="real-name" v-if="userDetail.real_name">{{ userDetail.real_name }}</text>
              <view class="status-badges">
                <text
                  class="status-badge"
                  :class="{
                    'status-active': userDetail.status === 1,
                    'status-inactive': userDetail.status === 2,
                    'status-locked': userDetail.status === 3,
                  }"
                >
                  {{ getStatusText(userDetail.status) }}
                </text>
                <text class="gender-badge">{{ getGenderText(userDetail.gender) }}</text>
              </view>
            </view>
          </view>

          <!-- 基本信息 -->
          <view class="info-section">
            <view class="section-title">基本信息</view>
            <view class="info-list">
              <view class="info-item">
                <text class="info-label">用户名</text>
                <text class="info-value">{{ userDetail.username }}</text>
              </view>
              <view class="info-item" v-if="userDetail.real_name">
                <text class="info-label">真实姓名</text>
                <text class="info-value">{{ userDetail.real_name }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">性别</text>
                <text class="info-value">{{ getGenderText(userDetail.gender) }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">状态</text>
                <text
                  class="info-value status-text"
                  :class="{
                    'status-active': userDetail.status === 1,
                    'status-inactive': userDetail.status === 2,
                    'status-locked': userDetail.status === 3,
                  }"
                >
                  {{ getStatusText(userDetail.status) }}
                </text>
              </view>
            </view>
          </view>

          <!-- 联系信息 -->
          <view class="info-section">
            <view class="section-title">联系信息</view>
            <view class="info-list">
              <view class="info-item">
                <text class="info-label">手机号</text>
                <text class="info-value">{{ userDetail.phone || '-' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">邮箱</text>
                <text class="info-value">{{ userDetail.email || '-' }}</text>
              </view>
            </view>
          </view>

          <!-- 角色信息 -->
          <view class="info-section">
            <view class="section-title">角色信息</view>
            <view class="roles-container">
              <view v-if="userDetail.roles && userDetail.roles.length > 0" class="roles-list">
                <text v-for="role in userDetail.roles" :key="role.id" class="role-tag">
                  {{ role.name }}
                </text>
              </view>
              <text v-else class="no-roles">暂无角色分配</text>
            </view>
          </view>

          <!-- 账户信息 -->
          <view class="info-section">
            <view class="section-title">账户信息</view>
            <view class="info-list">
              <view class="info-item">
                <text class="info-label">创建时间</text>
                <text class="info-value time-text">{{ userDetail.created_date || '-' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">更新时间</text>
                <text class="info-value time-text">{{ userDetail.updated_date || '-' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">最后登录</text>
                <text class="info-value time-text">{{ userDetail.last_login_date || '-' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">最后登录IP</text>
                <text class="info-value">{{ userDetail.last_login_ip || '-' }}</text>
              </view>
            </view>
          </view>

          <!-- 备注信息 -->
          <view v-if="userDetail.remark" class="info-section">
            <view class="section-title">备注信息</view>
            <view class="remark-text">{{ userDetail.remark }}</view>
          </view>
        </view>

        <!-- 错误状态 -->
        <view v-else-if="!loading" class="error-state">
          <wd-status-tip type="error" tip="获取用户详情失败" />
          <wd-button type="primary" @click="fetchUserDetail">重试</wd-button>
        </view>
      </view>

      <!-- 底部操作栏 -->
      <view class="bottom-actions" v-if="userDetail && !loading">
        <wd-button size="large" @click="handleBack">返回</wd-button>
        <wd-button type="primary" size="large" @click="editUser">编辑用户</wd-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { getSystemUserDetailApi, type ISysUserDetailResponse } from '@/api/sys/systemUserApi'

defineOptions({
  name: 'UserDetail',
})

// 页面参数
const userId = ref<string>()
const userName = ref<string>('')

// 页面状态
const loading = ref(false)
const userDetail = ref<ISysUserDetailResponse | null>(null)

// 页面标题
const pageTitle = computed(() => {
  return userName.value ? `${userName.value} - 用户详情` : '用户详情'
})

// 获取用户详情
const fetchUserDetail = async () => {
  if (!userId.value) {
    console.error('用户ID不能为空')
    return
  }

  loading.value = true
  try {
    const result = await getSystemUserDetailApi(userId.value)

    if (result.code === 200 && result.data) {
      userDetail.value = result.data
      // 更新页面标题中的用户名
      if (result.data.username) {
        userName.value = result.data.username
      }
      console.log('获取用户详情成功:', result.data)
    } else {
      throw new Error(result.message || '获取用户详情失败')
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    uni.showToast({
      title: '获取用户详情失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 获取状态显示文本
const getStatusText = (status: number) => {
  switch (status) {
    case 1:
      return '启用'
    case 2:
      return '禁用'
    case 3:
      return '锁定'
    default:
      return '未知'
  }
}

// 获取性别显示文本
const getGenderText = (gender?: number) => {
  switch (gender) {
    case 1:
      return '男'
    case 2:
      return '女'
    case 3:
      return '未知'
    default:
      return '未设置'
  }
}

// 编辑用户
const editUser = () => {
  if (!userId.value) return

  uni.navigateTo({
    url: `/pages-sys/system/user/user-form?mode=edit&userId=${userId.value}`,
  })
}

// 返回功能
const handleBack = () => {
  uni.navigateBack()
}

// 页面初始化
onMounted(async () => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}

  userId.value = options.userId || undefined
  userName.value = options.userName ? decodeURIComponent(options.userName) : ''

  console.log('页面参数原始值:', options)
  console.log('解析后的页面参数:', { userId: userId.value, userName: userName.value })
  console.log('userId类型:', typeof userId.value)

  if (userId.value) {
    await fetchUserDetail()
  } else {
    console.error('缺少用户ID参数，options:', options)
    uni.showToast({
      title: '缺少用户ID参数',
      icon: 'none',
    })
  }
})

// 页面显示时刷新数据
onShow(async () => {
  // 如果是从编辑页面返回，刷新用户详情
  if (userId.value && userDetail.value) {
    await fetchUserDetail()
  }
})
</script>

<style lang="scss" scoped>
.user-detail-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 18px !important;
  font-weight: bold !important;
}

:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-layout {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  padding-bottom: 120px; // 为底部操作栏留出空间
  -webkit-overflow-scrolling: touch; // iOS平滑滚动
  scroll-behavior: smooth; // 平滑滚动行为
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 20px;
  gap: 16px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 20px;
  gap: 20px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

// 用户头像和基本信息
.profile-section {
  padding: 24px 20px;
  background-color: white;
  display: flex;
  align-items: center;
  gap: 20px;
}

.avatar-container {
  flex-shrink: 0;
}

:deep(.user-avatar) {
  border: 3px solid #e0e0e0;
}

.avatar-error,
.avatar-loading {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.default-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.profile-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.username {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.real-name {
  font-size: 16px;
  color: #666;
}

.status-badges {
  display: flex;
  gap: 8px;
  align-items: center;
}

.status-badge {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  &.status-active {
    background-color: #e8f5e8;
    color: #4caf50;
  }

  &.status-inactive {
    background-color: #ffebee;
    color: #f44336;
  }

  &.status-locked {
    background-color: #fff9c4;
    color: #f57f17;
  }
}

.gender-badge {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  background-color: #e3f2fd;
  color: #1976d2;
}

// 信息区域
.info-section {
  background-color: white;
  padding: 0;
  margin-bottom: 8px;
}

.section-title {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.info-list {
  padding: 20px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f8f8f8;

  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  min-width: 80px;
}

.info-value {
  font-size: 14px;
  color: #333;
  word-break: break-all;
  text-align: right;

  &.time-text {
    font-size: 12px;
    color: #999;
  }

  &.status-text {
    padding: 2px 8px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;

    &.status-active {
      background-color: #e8f5e8;
      color: #4caf50;
    }

    &.status-inactive {
      background-color: #ffebee;
      color: #f44336;
    }

    &.status-locked {
      background-color: #fff9c4;
      color: #f57f17;
    }
  }
}

// 角色信息
.roles-container {
  padding: 20px;
}

.roles-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.role-tag {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.no-roles {
  font-size: 14px;
  color: #999;
  font-style: italic;
}

// 备注信息
.remark-text {
  padding: 20px;
  font-size: 14px;
  color: #333;
  line-height: 1.6;
  background-color: #f8f9fa;
  margin: 0 20px 20px 20px;
  border-radius: 8px;
}

// 底部操作栏
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 12px 20px;
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

.bottom-actions .wd-button {
  min-width: 80px;
}
</style>
