<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '系统管理',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="system-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      title="系统管理"
      fixed
      placeholder
      safe-area-inset-top
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <!-- 圆角卡片包裹 -->
      <view class="card-wrapper">
        <!-- 卡片网格布局 -->
        <view class="cards-container">
          <view
            v-for="item in menuCards"
            :key="item.path"
            class="menu-item"
            @click="navigateToPage(item.path)"
          >
            <!-- 图标背景框 -->
            <view class="icon-box" :style="{ backgroundColor: item.bgColor }">
              <!-- 如果是自定义图标（以 iconsys- 开头），使用原来的方式 -->
              <text
                v-if="item.icon && item.icon.startsWith('iconsys-')"
                class="iconfont-sys"
                :class="item.icon"
              ></text>

              <!-- 如果是 Wot UI 图标，使用 wd-icon 组件 -->
              <wd-icon v-else-if="item.icon" :name="item.icon" size="18px" color="#ffffff" />

              <!-- 默认图标（如果没有指定图标） -->
              <text v-else class="iconfont-sys iconsys-gengduo"></text>
            </view>
            <!-- 标题在框外下方 -->
            <view class="item-title">{{ item.title }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部系统TabBar组件 -->
    <SysTabBar :current-page="'system'" />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useMenuPermissionStore } from '../../store/menuPermission'
import { getCurrentPagePath } from '../../utils'

defineOptions({
  name: 'SystemManagement',
})

const menuPermissionStore = useMenuPermissionStore()

// 系统管理子菜单数据 - 从store获取
const menuCards = computed(() => {
  // 动态获取当前页面路径
  const currentPath = getCurrentPagePath()

  console.log('当前页面路径:', currentPath)

  // 获取当前页面的子菜单
  const systemMenus = menuPermissionStore.getSidebarMenus(currentPath)

  // 预定义的背景色数组
  const bgColors = [
    '#4285f4', // 蓝色
    '#34a853', // 绿色
    '#fbbc04', // 黄色
    '#ea4335', // 红色
    '#9c27b0', // 紫色
    '#ff9800', // 橙色
    '#795548', // 棕色
    '#607d8b', // 蓝灰色
    '#e91e63', // 粉色
    '#00bcd4', // 青色
  ]

  return systemMenus.map((menu, index) => ({
    title: menu.title,
    description: `管理${menu.title}`,
    icon: menu.icon || 'iconsys-default',
    bgColor: bgColors[index % bgColors.length],
    path: menu.path,
  }))
})

// 页面跳转
const navigateToPage = (path: string) => {
  console.log('跳转到:', path)
  uni.navigateTo({
    url: path,
    fail: (err) => {
      console.error('跳转失败:', err)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none',
      })
    },
  })
}

// 组件挂载时检查菜单数据
onMounted(async () => {
  console.log('=== system/index.vue 挂载调试 ===')
  console.log('rawMenus长度:', menuPermissionStore.rawMenus.length)

  // 确保菜单数据已加载
  if (menuPermissionStore.rawMenus.length === 0) {
    console.log('菜单数据为空，主动加载...')
    await menuPermissionStore.fetchUserMenus()
  }

  // 动态获取当前页面路径
  const currentPath = getCurrentPagePath()

  // 获取当前页面的子菜单
  const systemMenus = menuPermissionStore.getSidebarMenus(currentPath)
  console.log('当前页面路径:', currentPath)
  console.log('当前页面子菜单:', systemMenus)
  console.log('生成的菜单卡片:', menuCards.value)

  console.log('=== system/index.vue 调试结束 ===')
})
</script>

<style lang="scss" scoped>
.system-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

// 标题文字设为白色
:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

.page-content {
  flex: 1;
  background-color: transparent;
  overflow-y: auto;
  position: relative;
}

.card-wrapper {
  margin: 20px;
  padding: 25px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

.cards-container {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 15px;
  max-width: 500px;
  margin: 0 auto;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.2s ease;

  &:hover .icon-box {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  &:active .icon-box {
    transform: scale(0.95);
  }
}

.icon-box {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  .iconfont-sys {
    font-size: 18px;
    color: white;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
  }

  :deep(.wd-icon) {
    font-size: 18px !important;
    color: white !important;
  }
}

.item-title {
  font-size: 10px;
  font-weight: 500;
  color: #333;
  text-align: center;
  line-height: 1.2;
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-desc {
  display: none;
}

// 响应式布局
@media (max-width: 400px) {
  .card-wrapper {
    margin: 15px;
    padding: 20px;
  }

  .cards-container {
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    max-width: 350px;
  }

  .icon-box {
    width: 40px;
    height: 40px;
    border-radius: 8px;

    .iconfont-sys {
      font-size: 18px;
    }

    :deep(.wd-icon) {
      font-size: 18px !important;
      color: white !important;
    }
  }

  .item-title {
    font-size: 9px;
    max-width: 50px;
  }
}

@media (min-width: 600px) {
  .card-wrapper {
    margin: 25px;
    padding: 30px;
  }

  .cards-container {
    grid-template-columns: repeat(6, 1fr);
    max-width: 700px;
  }

  .icon-box {
    width: 50px;
    height: 50px;
    border-radius: 12px;

    .iconfont-sys {
      font-size: 22px;
    }

    :deep(.wd-icon) {
      font-size: 22px !important;
      color: white !important;
    }
  }

  .item-title {
    font-size: 11px;
    max-width: 70px;
  }
}
</style>
