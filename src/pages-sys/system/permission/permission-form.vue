<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '权限表单',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="permission-form-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      :title="pageTitle"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <view class="main-layout">
        <view class="permission-form">
          <wd-form ref="formRef" :model="formData" :rules="formRules" errorType="message">
            <!-- 基本信息 -->
            <view class="form-section">
              <view class="section-title">基本信息</view>

              <wd-cell-group border>
                <!-- 菜单名称 -->
                <wd-input
                  label="菜单名称"
                  label-width="80px"
                  prop="menu_name"
                  v-model="formData.menu_name"
                  placeholder="请输入菜单名称"
                  :readonly="isViewMode"
                  :maxlength="50"
                  show-word-limit
                  clearable
                  :rules="formRules.menu_name"
                />

                <!-- 父级菜单 -->
                <wd-picker
                  label="父级菜单"
                  label-width="80px"
                  prop="parent_id"
                  v-model="formData.parent_id"
                  :columns="parentMenuOptions"
                  value-key="id"
                  label-key="menu_name"
                  placeholder="请选择父级菜单"
                  :disabled="isViewMode"
                  @confirm="onParentMenuChange"
                />

                <!-- 菜单类型 -->
                <wd-picker
                  label="菜单类型"
                  label-width="80px"
                  prop="menu_type"
                  v-model="formData.menu_type"
                  :columns="menuTypeOptions"
                  placeholder="请选择菜单类型"
                  :disabled="isViewMode"
                  :rules="formRules.menu_type"
                  @confirm="onMenuTypeChange"
                />

                <!-- 显示顺序 -->
                <wd-input
                  label="显示顺序"
                  label-width="80px"
                  prop="order_num"
                  v-model="formData.order_num"
                  type="number"
                  placeholder="请输入显示顺序"
                  :readonly="isViewMode"
                  clearable
                />

                <!-- 菜单图标 -->
                <wd-input
                  label="菜单图标"
                  label-width="80px"
                  prop="icon"
                  v-model="formData.icon"
                  placeholder="请输入图标名称"
                  :readonly="isViewMode"
                  clearable
                />
              </wd-cell-group>
            </view>

            <!-- 路由信息 -->
            <view class="form-section" v-if="showRouteFields">
              <view class="section-title">路由信息</view>

              <wd-cell-group border>
                <!-- 路由地址 -->
                <wd-input
                  label="路由地址"
                  label-width="80px"
                  prop="path"
                  v-model="formData.path"
                  placeholder="请输入路由地址"
                  :readonly="isViewMode"
                  clearable
                />

                <!-- 组件路径 -->
                <wd-input
                  v-if="formData.menu_type === 2"
                  label="组件路径"
                  label-width="80px"
                  prop="component"
                  v-model="formData.component"
                  placeholder="请输入组件路径"
                  :readonly="isViewMode"
                  clearable
                />

                <!-- 路由参数 -->
                <wd-input
                  label="路由参数"
                  label-width="80px"
                  prop="query"
                  v-model="formData.query"
                  placeholder="请输入路由参数"
                  :readonly="isViewMode"
                  clearable
                />
              </wd-cell-group>
            </view>

            <!-- 权限信息 -->
            <view class="form-section">
              <view class="section-title">权限信息</view>

              <wd-cell-group border>
                <!-- 权限标识 -->
                <wd-input
                  label="权限标识"
                  label-width="80px"
                  prop="perms"
                  v-model="formData.perms"
                  placeholder="请输入权限标识，如: sys:user:list"
                  :readonly="isViewMode"
                  clearable
                />
              </wd-cell-group>
            </view>

            <!-- 其他设置 -->
            <view class="form-section">
              <view class="section-title">其他设置</view>

              <wd-cell-group border>
                <!-- 是否外链 -->
                <wd-cell v-if="showRouteFields" title="是否外链">
                  <wd-switch
                    v-model="isFrameSwitch"
                    :disabled="isViewMode"
                    @change="onFrameChange"
                  />
                </wd-cell>

                <!-- 是否缓存 -->
                <wd-cell v-if="formData.menu_type === 2" title="是否缓存">
                  <wd-switch
                    v-model="isCacheSwitch"
                    :disabled="isViewMode"
                    @change="onCacheChange"
                  />
                </wd-cell>

                <!-- 是否显示 -->
                <wd-cell title="是否显示">
                  <wd-switch
                    v-model="visibleSwitch"
                    :disabled="isViewMode"
                    @change="onVisibleChange"
                  />
                </wd-cell>

                <!-- 菜单状态 -->
                <wd-cell title="菜单状态">
                  <wd-switch
                    v-model="statusSwitch"
                    :disabled="isViewMode"
                    @change="onStatusChange"
                  />
                </wd-cell>

                <!-- 备注 -->
                <wd-textarea
                  label="备注"
                  label-width="80px"
                  prop="remark"
                  v-model="formData.remark"
                  placeholder="请输入备注信息"
                  :readonly="isViewMode"
                  :maxlength="200"
                  show-word-limit
                  :rows="3"
                />
              </wd-cell-group>
            </view>
          </wd-form>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="form-actions" v-if="!isViewMode">
      <wd-button @click="handleCancel" size="large" custom-class="cancel-btn">取消</wd-button>
      <wd-button
        type="primary"
        @click="handleSubmit"
        size="large"
        :loading="submitting"
        custom-class="submit-btn"
      >
        {{ isEditMode ? '更新' : '创建' }}
      </wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
// @ts-ignore
import {
  createPermissionApi,
  updatePermissionApi,
  getPermissionDetailApi,
  getPermissionSelectItemsApi,
  type IPermissionCreateRequest,
  type IPermissionUpdateRequest,
  type IPermissionDetailResponse,
  type IPermissionSelectItem,
} from '../../../api/sys/systemPermissionApi'

defineOptions({
  name: 'PermissionForm',
})

// 页面参数
const pageParams = ref<{
  mode: 'create' | 'edit' | 'view'
  id?: string
  parent_id?: string
}>({
  mode: 'create',
})

// 页面模式
const isCreateMode = computed(() => pageParams.value.mode === 'create')
const isEditMode = computed(() => pageParams.value.mode === 'edit')
const isViewMode = computed(() => pageParams.value.mode === 'view')

// 页面标题
const pageTitle = computed(() => {
  if (isCreateMode.value) {
    return pageParams.value.parent_id ? '新增子权限' : '新增权限'
  }
  if (isEditMode.value) return '编辑权限'
  return '权限详情'
})

// 表单引用
const formRef = ref()
const submitting = ref(false)

// 表单数据
const formData = ref<IPermissionCreateRequest & { id?: string; parent_name?: string }>({
  menu_name: '',
  parent_id: '', // 改为空字符串，避免 null 值导致 picker 组件报错
  parent_name: '', // 添加父级菜单名称字段，用于查看模式显示
  order_num: 0,
  path: null,
  component: null,
  query: null,
  is_frame: 1,
  is_cache: 0,
  menu_type: 1,
  visible: 0,
  status: 0,
  perms: null,
  icon: null,
  remark: null,
})

// 父级菜单选项
const parentMenuOptions = ref<IPermissionSelectItem[]>([])

// 菜单类型选项
const menuTypeOptions = ref([
  { label: '目录', value: 1 },
  { label: '菜单', value: 2 },
  { label: '按钮', value: 3 },
])

// 计算属性：是否显示路由相关字段
const showRouteFields = computed(() => {
  return formData.value.menu_type === 1 || formData.value.menu_type === 2
})

// Switch开关状态
const isFrameSwitch = ref(false) // 是否外链：false=否，true=是
const isCacheSwitch = ref(true) // 是否缓存：true=缓存，false=不缓存
const visibleSwitch = ref(true) // 是否显示：true=显示，false=隐藏
const statusSwitch = ref(true) // 菜单状态：true=正常，false=停用

// 表单验证规则
const formRules = ref({
  menu_name: [
    { required: true, message: '菜单名称不能为空' },
    { required: true, min: 2, max: 50, message: '菜单名称长度在2到50个字符' },
  ],
  menu_type: [{ required: true, message: '请选择菜单类型' }],
  path: [
    {
      required: false,
      message: '路由地址不能为空',
      validator: (value: string) => {
        if (showRouteFields.value && !value) {
          return false
        }
        return true
      },
    },
  ],
  component: [
    {
      required: false,
      message: '组件路径不能为空',
      validator: (value: string) => {
        if (formData.value.menu_type === 2 && !value) {
          return false
        }
        return true
      },
    },
  ],
})

// 监听switch状态变化
watch(
  () => isFrameSwitch.value,
  (val) => {
    formData.value.is_frame = val ? 0 : 1
  },
)

watch(
  () => isCacheSwitch.value,
  (val) => {
    formData.value.is_cache = val ? 0 : 1
  },
)

watch(
  () => visibleSwitch.value,
  (val) => {
    formData.value.visible = val ? 0 : 1
  },
)

watch(
  () => statusSwitch.value,
  (val) => {
    formData.value.status = val ? 0 : 1
  },
)

// 监听order_num变化，确保始终是数字类型
watch(
  () => formData.value.order_num,
  (val) => {
    if (typeof val === 'string') {
      const numVal = val === '' ? 0 : Number(val)
      if (!isNaN(numVal)) {
        formData.value.order_num = numVal
      }
    }
  },
)

// 父级菜单变化
const onParentMenuChange = ({ value }: { value: string }) => {
  console.log('父级菜单变化:', value)
}

// 菜单类型变化
const onMenuTypeChange = ({ value }: { value: number }) => {
  console.log('菜单类型变化:', value)
  // 如果是按钮类型，清空路由相关字段
  if (value === 3) {
    formData.value.path = null
    formData.value.component = null
    formData.value.query = null
  }
}

// Switch变化事件
const onFrameChange = (value: boolean) => {
  formData.value.is_frame = value ? 0 : 1
}

const onCacheChange = (value: boolean) => {
  formData.value.is_cache = value ? 0 : 1
}

const onVisibleChange = (value: boolean) => {
  formData.value.visible = value ? 0 : 1
}

const onStatusChange = (value: boolean) => {
  formData.value.status = value ? 0 : 1
}

// 加载父级菜单选项
const loadParentMenuOptions = async () => {
  try {
    const result = await getPermissionSelectItemsApi()

    // 过滤掉按钮级别的菜单，只保留目录(1)和菜单(2)作为可选的父级菜单
    const filteredMenus = result.data.filter((menu: IPermissionSelectItem) => {
      return menu.menu_type === 1 || menu.menu_type === 2
    })

    parentMenuOptions.value = [
      { id: '', menu_name: '无上级菜单', parent_id: null, order_num: 0, menu_type: null },
      ...filteredMenus,
    ]

    console.log('加载父级菜单选项，已过滤按钮菜单:', parentMenuOptions.value)
  } catch (error) {
    console.error('加载父级菜单失败:', error)
    uni.showToast({
      title: '加载父级菜单失败',
      icon: 'none',
    })
  }
}

// 加载权限详情
const loadPermissionDetail = async () => {
  if (!pageParams.value.id) return

  try {
    uni.showLoading({ title: '加载权限详情...' })

    // 编辑模式和查看模式都需要加载父级菜单选项，因为都使用picker组件
    if (isEditMode.value || isViewMode.value) {
      await loadParentMenuOptions()
    }

    const result = await getPermissionDetailApi(pageParams.value.id)
    const detail = result.data

    console.log('权限详情数据:', detail)

    // 设置表单数据
    formData.value = {
      id: detail.id,
      menu_name: detail.menu_name || '',
      parent_id: detail.parent_id || '', // 确保不为 null，使用空字符串
      parent_name: detail.parent_name || '', // 保存父级菜单名称，用于查看模式显示
      order_num:
        typeof detail.order_num === 'string'
          ? Number(detail.order_num) || 0
          : detail.order_num || 0, // 确保是数字类型
      path: detail.path,
      component: detail.component,
      query: detail.query,
      is_frame: detail.is_frame !== undefined ? detail.is_frame : 1,
      is_cache: detail.is_cache !== undefined ? detail.is_cache : 0,
      menu_type: detail.menu_type || 1,
      visible: detail.visible !== undefined ? detail.visible : 0,
      status: detail.status !== undefined ? detail.status : 0,
      perms: detail.perms,
      icon: detail.icon,
      remark: detail.remark,
    }

    // 设置switch状态（注意：0表示是，1表示否）
    isFrameSwitch.value = detail.is_frame === 0
    isCacheSwitch.value = detail.is_cache === 0
    visibleSwitch.value = detail.visible === 0
    statusSwitch.value = detail.status === 0

    console.log('表单数据已设置:', formData.value)
    console.log('父级菜单ID:', detail.parent_id, '父级菜单名称:', detail.parent_name)
    if (isEditMode.value || isViewMode.value) {
      console.log('可用的父级菜单选项:', parentMenuOptions.value)
    }
    console.log('Switch状态已设置:', {
      isFrameSwitch: isFrameSwitch.value,
      isCacheSwitch: isCacheSwitch.value,
      visibleSwitch: visibleSwitch.value,
      statusSwitch: statusSwitch.value,
    })

    // 验证父级菜单是否在选项中（编辑模式和查看模式都需要，确保picker组件能正确显示）
    if ((isEditMode.value || isViewMode.value) && detail.parent_id) {
      const parentOption = parentMenuOptions.value.find((option) => option.id === detail.parent_id)
      if (!parentOption) {
        console.warn('警告：父级菜单不在可选项中', detail.parent_id, detail.parent_name)
        // 如果当前权限的父级菜单不在选项中，临时添加进去
        parentMenuOptions.value.push({
          id: detail.parent_id,
          menu_name: detail.parent_name || `父级菜单 (${detail.parent_id})`,
          parent_id: null,
          order_num: 0,
          menu_type: null,
        })
        console.log('已临时添加父级菜单选项:', parentMenuOptions.value)
      }
    }
  } catch (error) {
    console.error('加载权限详情失败:', error)
    uni.showToast({
      title: '加载权限详情失败',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    const { valid, errors } = await formRef.value.validate()

    if (!valid) {
      console.log('表单验证失败:', errors)
      uni.showToast({
        title: '请检查表单信息',
        icon: 'none',
      })
      return
    }

    submitting.value = true

    // 准备提交数据，处理空字符串转为 null
    const submitData = {
      ...formData.value,
      parent_id: formData.value.parent_id || null, // 空字符串转为 null
      order_num:
        typeof formData.value.order_num === 'string'
          ? formData.value.order_num === ''
            ? 0
            : Number(formData.value.order_num)
          : formData.value.order_num, // 确保 order_num 是数字类型
    }

    if (isEditMode.value) {
      // 更新权限
      await updatePermissionApi(pageParams.value.id!, submitData as IPermissionUpdateRequest)
      uni.showToast({
        title: '更新成功',
        icon: 'success',
      })
    } else {
      // 创建权限
      await createPermissionApi(submitData as IPermissionCreateRequest)
      uni.showToast({
        title: '创建成功',
        icon: 'success',
      })
    }

    // 延迟返回，让用户看到成功提示
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } catch (error) {
    console.error('提交失败:', error)
    uni.showToast({
      title: isEditMode.value ? '更新失败' : '创建失败',
      icon: 'none',
    })
  } finally {
    submitting.value = false
  }
}

// 取消操作
const handleCancel = () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消操作吗？未保存的内容将丢失。',
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack()
      }
    },
  })
}

// 返回功能
const handleBack = () => {
  // 如果是查看模式，直接返回
  if (isViewMode.value) {
    uni.navigateBack()
    return
  }

  // 编辑/新增模式调用取消逻辑
  handleCancel()
}

// 页面加载事件
onLoad((options) => {
  console.log('权限表单页面参数:', options)

  if (options) {
    pageParams.value.mode = (options.mode as 'create' | 'edit' | 'view') || 'create'
    pageParams.value.id = options.id
    pageParams.value.parent_id = options.parent_id
  }

  console.log('页面参数设置完成:', pageParams.value)

  // 如果是编辑或查看模式，加载详情
  if ((isEditMode.value || isViewMode.value) && pageParams.value.id) {
    loadPermissionDetail()
  }
})

onMounted(() => {
  console.log('权限表单页面挂载完成')
  console.log('页面模式:', pageParams.value.mode)
  console.log('权限ID:', pageParams.value.id)
  console.log('父级ID:', pageParams.value.parent_id)

  // 只在新增模式时加载父级菜单选项，编辑模式在 loadPermissionDetail 中已加载，查看模式不需要
  if (isCreateMode.value) {
    loadParentMenuOptions()
  }

  // 如果是新增子权限，预设父级菜单
  if (isCreateMode.value && pageParams.value.parent_id) {
    formData.value.parent_id = pageParams.value.parent_id || ''
    console.log('设置父级菜单ID:', pageParams.value.parent_id)
  }
})
</script>

<style lang="scss" scoped>
.permission-form-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding-bottom: 80px; /* 为固定按钮留出空间 */
}

.main-layout {
  padding: 8px;
}

.permission-form {
  // 简化表单样式，直接展示
}

.form-section {
  margin-bottom: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 15px 8px 15px;
  padding-bottom: 6px;
  border-bottom: 1px solid #eee;
}

.form-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 12px;
  padding: 12px;
  background-color: white;
  border-top: 1px solid #eee;
  z-index: 100;
  safe-area-inset-bottom: env(safe-area-inset-bottom);
}

:deep(.cancel-btn) {
  flex: 1;
  background-color: #f5f5f5 !important;
  color: #666 !important;
  border-color: #ddd !important;
}

:deep(.submit-btn) {
  flex: 1;
}

// Wot UI 表单项样式优化
:deep(.wd-cell-group) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.wd-cell) {
  background-color: white;
}

:deep(.wd-input) {
  background-color: transparent;
}

:deep(.wd-picker) {
  background-color: transparent;
}

:deep(.wd-textarea) {
  background-color: transparent;
}

// 查看模式下优化禁用组件的显示效果
:deep(.wd-picker.wd-picker--disabled) {
  opacity: 0.8 !important; // 提高透明度，让文字更清晰
}

:deep(.wd-switch.wd-switch--disabled) {
  opacity: 0.8 !important; // 提高透明度，让开关状态更清晰
}
</style>
