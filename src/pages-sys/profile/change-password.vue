<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '修改密码',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="change-password-container">
    <!-- 导航栏 -->
    <Navbar
      title="修改密码"
      :fixed="true"
      :placeholder="true"
      custom-class="custom-navbar"
      @back="handleBack"
    />

    <!-- 内容区域 -->
    <view class="page-content">
      <view class="main-layout">
        <!-- 说明提示 -->
        <view class="tips-section">
          <view class="tips-icon">
            <text class="iconfont-sys iconsys-info-circle-filled"></text>
          </view>
          <view class="tips-content">
            <view class="tips-title">安全提示</view>
            <view class="tips-text">
              为了保障账户安全，请定期更换密码。密码长度6-20位，建议包含数字、字母和特殊字符。
            </view>
          </view>
        </view>

        <!-- 密码表单 -->
        <view class="form-section">
          <wd-form ref="formRef" :model="passwordForm">
            <wd-cell-group border>
              <!-- 旧密码 -->
              <wd-input
                label="当前密码"
                label-width="80px"
                v-model="passwordForm.oldPassword"
                type="safe-password"
                show-password
                placeholder="请输入当前密码"
                :maxlength="20"
                clearable
                required
              />

              <!-- 新密码 -->
              <wd-input
                label="新密码"
                label-width="80px"
                v-model="passwordForm.newPassword"
                type="safe-password"
                show-password
                placeholder="请输入新密码(6-20位)"
                :maxlength="20"
                clearable
                required
              />

              <!-- 确认新密码 -->
              <wd-input
                label="确认密码"
                label-width="80px"
                v-model="passwordForm.confirmPassword"
                type="safe-password"
                show-password
                placeholder="请再次输入新密码"
                :maxlength="20"
                clearable
                required
              />
            </wd-cell-group>
          </wd-form>
        </view>

        <!-- 密码强度指示器 -->
        <view v-if="passwordForm.newPassword" class="password-strength-section">
          <view class="strength-title">密码强度</view>
          <view class="strength-indicator">
            <view class="strength-bars">
              <view
                v-for="i in 4"
                :key="i"
                class="strength-bar"
                :class="{
                  weak: passwordStrength >= 1 && i <= 1,
                  medium: passwordStrength >= 2 && i <= 2,
                  strong: passwordStrength >= 3 && i <= 3,
                  excellent: passwordStrength >= 4 && i <= 4,
                }"
              ></view>
            </view>
            <text class="strength-text">{{ getStrengthText(passwordStrength) }}</text>
          </view>

          <!-- 密码要求列表 -->
          <view class="password-requirements">
            <view
              v-for="requirement in passwordRequirements"
              :key="requirement.key"
              class="requirement-item"
              :class="{ met: requirement.met }"
            >
              <text
                class="requirement-icon iconfont-sys"
                :class="
                  requirement.met ? 'iconsys-check-circle-filled' : 'iconsys-close-circle-filled'
                "
              ></text>
              <text class="requirement-text">{{ requirement.text }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <wd-button
        type="primary"
        size="large"
        :loading="submitLoading"
        :disabled="!canSubmit"
        @click="handleSubmit"
        custom-class="submit-btn"
      >
        {{ submitLoading ? '修改中...' : '确认修改' }}
      </wd-button>
    </view>

    <!-- 成功提示弹窗 -->
    <wd-popup
      v-model="showSuccessPopup"
      position="center"
      custom-style="border-radius: 12px; width: 80%; max-width: 320px;"
    >
      <view class="success-popup">
        <view class="success-icon">
          <text class="iconfont-sys iconsys-check-circle-filled"></text>
        </view>
        <view class="success-title">密码修改成功</view>
        <view class="success-text">
          为了安全起见，系统将在3秒后自动退出登录，请使用新密码重新登录。
        </view>
        <view class="success-countdown">{{ countdown }}秒后自动退出</view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { useSystemAdminStore } from '@/store/systemAdmin'

// Store
const systemAdminStore = useSystemAdminStore()

// 表单引用
const formRef = ref()

// 表单数据
const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
})

// 状态管理
const submitLoading = ref(false)
const showSuccessPopup = ref(false)
const countdown = ref(3)

// 密码强度计算
const passwordStrength = computed(() => {
  const password = passwordForm.value.newPassword
  if (!password) return 0

  let strength = 0

  // 长度检查
  if (password.length >= 6) strength++
  if (password.length >= 8) strength++

  // 包含数字
  if (/\d/.test(password)) strength++

  // 包含小写字母
  if (/[a-z]/.test(password)) strength++

  // 包含大写字母
  if (/[A-Z]/.test(password)) strength++

  // 包含特殊字符
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) strength++

  return Math.min(strength, 4)
})

// 密码要求检查
const passwordRequirements = computed(() => {
  const password = passwordForm.value.newPassword

  return [
    {
      key: 'length',
      text: '密码长度6-20位',
      met: password.length >= 6 && password.length <= 20,
    },
    {
      key: 'number',
      text: '包含数字',
      met: /\d/.test(password),
    },
    {
      key: 'letter',
      text: '包含字母',
      met: /[a-zA-Z]/.test(password),
    },
    {
      key: 'match',
      text: '两次密码输入一致',
      met: password === passwordForm.value.confirmPassword && password !== '',
    },
  ]
})

// 是否可以提交
const canSubmit = computed(() => {
  const form = passwordForm.value
  return (
    form.oldPassword.trim() !== '' &&
    form.newPassword.trim() !== '' &&
    form.confirmPassword.trim() !== '' &&
    form.newPassword === form.confirmPassword &&
    form.newPassword.length >= 6 &&
    form.newPassword.length <= 20 &&
    !submitLoading.value
  )
})

// 获取强度文本
const getStrengthText = (strength: number) => {
  switch (strength) {
    case 0:
    case 1:
      return '弱'
    case 2:
      return '中等'
    case 3:
      return '强'
    case 4:
      return '非常强'
    default:
      return '弱'
  }
}

// 表单验证
const validateForm = () => {
  const form = passwordForm.value

  if (!form.oldPassword.trim()) {
    uni.showToast({
      title: '请输入当前密码',
      icon: 'none',
    })
    return false
  }

  if (!form.newPassword.trim()) {
    uni.showToast({
      title: '请输入新密码',
      icon: 'none',
    })
    return false
  }

  if (form.newPassword.length < 6 || form.newPassword.length > 20) {
    uni.showToast({
      title: '新密码长度必须在6-20位之间',
      icon: 'none',
    })
    return false
  }

  if (!form.confirmPassword.trim()) {
    uni.showToast({
      title: '请确认新密码',
      icon: 'none',
    })
    return false
  }

  if (form.newPassword !== form.confirmPassword) {
    uni.showToast({
      title: '两次输入的密码不一致',
      icon: 'none',
    })
    return false
  }

  if (form.oldPassword === form.newPassword) {
    uni.showToast({
      title: '新密码不能与当前密码相同',
      icon: 'none',
    })
    return false
  }

  return true
}

// 提交表单
const handleSubmit = async () => {
  if (!validateForm()) return

  submitLoading.value = true

  try {
    // 模拟API调用 - 实际项目中使用真实的changePasswordApi
    console.log('修改密码请求:', {
      old_password: passwordForm.value.oldPassword,
      new_password: passwordForm.value.newPassword,
    })

    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 1500))

    // 模拟成功响应
    const mockSuccess = true

    if (mockSuccess) {
      // 显示成功弹窗
      showSuccessPopup.value = true

      // 开始倒计时
      startCountdown()
    } else {
      throw new Error('修改密码失败')
    }

    // 真实API调用代码（注释掉以避免导入错误）
    /*
    const result = await changePasswordApi({
      old_password: passwordForm.value.oldPassword,
      new_password: passwordForm.value.newPassword,
    })
    
    if (result.code === 200) {
      showSuccessPopup.value = true
      startCountdown()
    } else {
      uni.showToast({
        title: result.message || '修改密码失败',
        icon: 'none',
      })
    }
    */
  } catch (error) {
    console.error('修改密码失败:', error)

    let errorMessage = '修改密码失败'
    if (error instanceof Error) {
      errorMessage = error.message
    }

    // 特殊处理一些常见错误
    if (errorMessage.includes('旧密码不正确') || errorMessage.includes('原密码错误')) {
      errorMessage = '当前密码不正确，请重新输入'
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000,
    })
  } finally {
    submitLoading.value = false
  }
}

// 开始倒计时
const startCountdown = () => {
  const timer = setInterval(() => {
    countdown.value--

    if (countdown.value <= 0) {
      clearInterval(timer)
      // 自动登出并跳转到登录页
      handleAutoLogout()
    }
  }, 1000)
}

// 自动登出
const handleAutoLogout = async () => {
  try {
    await systemAdminStore.logout()
  } catch (error) {
    console.error('登出失败:', error)
  }

  // 跳转到登录页
  uni.reLaunch({
    url: '/pages-sys/login/login',
  })
}

// 返回功能
const handleBack = () => {
  if (submitLoading.value) {
    uni.showToast({
      title: '正在修改密码，请稍候...',
      icon: 'none',
    })
    return
  }

  uni.navigateBack()
}

// 监听新密码变化，清空确认密码
watch(
  () => passwordForm.value.newPassword,
  () => {
    if (passwordForm.value.confirmPassword) {
      // 如果新密码变化，清空确认密码，让用户重新输入
      // passwordForm.value.confirmPassword = ''
    }
  },
)
</script>

<style lang="scss" scoped>
.change-password-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;

  .wd-navbar__title {
    color: white !important;
    font-size: 20px !important;
    font-weight: bold !important;
  }

  .wd-navbar__left .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;

    .wd-icon {
      font-size: 26px !important;
      color: white !important;
    }
  }
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.main-layout {
  padding: 20px 20px 120px 20px; // 底部留出按钮空间
}

// 提示区域
.tips-section {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
}

.tips-icon {
  flex-shrink: 0;

  .iconfont-sys {
    font-size: 20px;
    color: #856404;
  }
}

.tips-content {
  flex: 1;
}

.tips-title {
  font-size: 14px;
  font-weight: 600;
  color: #856404;
  margin-bottom: 4px;
}

.tips-text {
  font-size: 12px;
  color: #856404;
  line-height: 1.5;
}

// 表单区域
.form-section {
  background: white;
  border-radius: 12px;
  margin-bottom: 20px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

// Wot UI 表单项样式优化
:deep(.wd-cell-group) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.wd-cell) {
  background-color: white;
}

:deep(.wd-input) {
  background-color: transparent;
}

// 密码强度区域
.password-strength-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.strength-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.strength-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.strength-bars {
  display: flex;
  gap: 4px;
  flex: 1;
}

.strength-bar {
  height: 6px;
  border-radius: 3px;
  background-color: #e0e0e0;
  flex: 1;
  transition: background-color 0.3s;

  &.weak {
    background-color: #ff4757;
  }

  &.medium {
    background-color: #ffa502;
  }

  &.strong {
    background-color: #3742fa;
  }

  &.excellent {
    background-color: #2ed573;
  }
}

.strength-text {
  font-size: 12px;
  color: #666;
  min-width: 50px;
  text-align: right;
}

// 密码要求
.password-requirements {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.requirement-item {
  display: flex;
  align-items: center;
  gap: 8px;

  &.met {
    .requirement-icon {
      color: #2ed573;
    }

    .requirement-text {
      color: #2ed573;
    }
  }

  &:not(.met) {
    .requirement-icon {
      color: #ff4757;
    }

    .requirement-text {
      color: #666;
    }
  }
}

.requirement-icon {
  font-size: 14px;
}

.requirement-text {
  font-size: 12px;
}

// 底部操作
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: white;
  border-top: 1px solid #f0f0f0;
  z-index: 100;
}

:deep(.submit-btn) {
  width: 100%;
  height: 50px;
  border-radius: 12px;
  background: #4285f4;
  font-size: 16px;
  font-weight: 600;

  &.wd-button--disabled {
    background: #ccc;
  }

  &.wd-button--loading {
    background: #6fa8f5;
  }
}

// 成功弹窗
.success-popup {
  padding: 32px 24px;
  text-align: center;
}

.success-icon {
  margin-bottom: 16px;

  .iconfont-sys {
    font-size: 48px;
    color: #2ed573;
  }
}

.success-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.success-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16px;
}

.success-countdown {
  font-size: 16px;
  color: #4285f4;
  font-weight: 600;
}
</style> 