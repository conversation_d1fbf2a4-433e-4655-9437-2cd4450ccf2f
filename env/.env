VITE_APP_TITLE = 'invoice-book-uniapp'
VITE_APP_PORT = 9000

VITE_UNI_APPID = '__UNI__3D6B810'
VITE_WX_APPID = 'wx1c28b113b3d1f63a'

# h5部署网站的base，配置到 manifest.config.ts 里的 h5.router.base
VITE_APP_PUBLIC_BASE = http://************:9002
#VITE_APP_PUBLIC_BASE = https://oywm.top

#VITE_SERVER_BASEURL = 'https://oywm.top'
VITE_SERVER_BASEURL = 'http://localhost:9002'
VITE_UPLOAD_BASEURL = 'http://************:9002'
#VITE_UPLOAD_BASEURL = 'https://oywm.top'

# 有些同学可能需要在微信小程序里面根据 develop、trial、release 分别设置上传地址，参考代码如下。
# 下面的变量如果没有设置，会默认使用 VITE_SERVER_BASEURL or VITE_UPLOAD_BASEURL
#VITE_SERVER_BASEURL__WEIXIN_DEVELOP = 'https://oywm.top'
#VITE_SERVER_BASEURL__WEIXIN_DEVELOP = 'http://localhost:9002'
VITE_SERVER_BASEURL__WEIXIN_DEVELOP = 'http://************:9002'
#VITE_SERVER_BASEURL__WEIXIN_TRIAL = 'https://oywm.top'
VITE_SERVER_BASEURL__WEIXIN_TRIAL = 'http://************:9002'
#VITE_SERVER_BASEURL__WEIXIN_RELEASE = 'https://oywm.top'
VITE_SERVER_BASEURL__WEIXIN_RELEASE = 'http://************:9002'

VITE_UPLOAD_BASEURL__WEIXIN_DEVELOP = 'http://************:9002'
#VITE_UPLOAD_BASEURL__WEIXIN_TRIAL = 'https://oywm.top'
VITE_UPLOAD_BASEURL__WEIXIN_TRIAL = 'http://************:9002'
VITE_UPLOAD_BASEURL__WEIXIN_RELEASE = 'http://************:9002'

# h5是否需要配置代理
VITE_APP_PROXY = false
VITE_APP_PROXY_PREFIX = '/api'
