var __defProp=Object.defineProperty,__defProps=Object.defineProperties,__getOwnPropDescs=Object.getOwnPropertyDescriptors,__getOwnPropSymbols=Object.getOwnPropertySymbols,__hasOwnProp=Object.prototype.hasOwnProperty,__propIsEnum=Object.prototype.propertyIsEnumerable,__typeError=e=>{throw TypeError(e)},__pow=Math.pow,__defNormalProp=(e,t,a)=>t in e?__defProp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,__spreadValues=(e,t)=>{for(var a in t||(t={}))__hasOwnProp.call(t,a)&&__defNormalProp(e,a,t[a]);if(__getOwnPropSymbols)for(var a of __getOwnPropSymbols(t))__propIsEnum.call(t,a)&&__defNormalProp(e,a,t[a]);return e},__spreadProps=(e,t)=>__defProps(e,__getOwnPropDescs(t)),__accessCheck=(e,t,a)=>t.has(e)||__typeError("Cannot "+a),__privateGet=(e,t,a)=>(__accessCheck(e,t,"read from private field"),a?a.call(e):t.get(e)),__privateAdd=(e,t,a)=>t.has(e)?__typeError("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,a),__privateSet=(e,t,a,n)=>(__accessCheck(e,t,"write to private field"),n?n.call(e,a):t.set(e,a),a),__privateMethod=(e,t,a)=>(__accessCheck(e,t,"access private method"),a),__privateWrapper=(e,t,a,n)=>({set _(n){__privateSet(e,t,n,a)},get _(){return __privateGet(e,t,n)}}),__async=(e,t,a)=>new Promise(((n,o)=>{var r=e=>{try{s(a.next(e))}catch(t){o(t)}},l=e=>{try{s(a.throw(e))}catch(t){o(t)}},s=e=>e.done?n(e.value):Promise.resolve(e.value).then(r,l);s((a=a.apply(e,t)).next())}));if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((a=>t.resolve(e()).then((()=>a))),(a=>t.resolve(e()).then((()=>{throw a}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.ArrayBuffer,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.BigInt64Array,BigUint64Array=e.BigUint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";var t,a,n,o,r,l,s,i,c,u,d,p,m,v,f,h,g,y,_,w,b,k,x,C,V,E,N,S,B,z,$,P,I,T,A,D,M,O,F,j;function G(e,t,...a){uni.__log__&&uni.__log__(e,t,...a)}function L(e,t){return"string"==typeof e?t:e}const R=t=>(a,n=e.getCurrentInstance())=>{!e.isInSSRComponentSetup&&e.injectHook(t,a,n)},H=R("onShow"),q=R("onHide"),Q=R("onLaunch");class U{constructor(e){this._reject=null,this.promise=new Promise(((t,a)=>{e(t,a),this._reject=a}))}abort(e){this._reject&&this._reject(e)}then(e,t){return this.promise.then(e,t)}catch(e){return this.promise.catch(e)}}function W(){return K()+K()+K()+K()+K()+K()+K()+K()}function K(){return Math.floor(65536*(1+Math.random())).toString(16).substring(1)}function Y(e){return Number.isNaN(Number(e))?`${e}`:`${e}px`}function X(e){return"[object Object]"===Object.prototype.toString.call(e)||"object"==typeof e}function J(e){const t=Object.prototype.toString.call(e).match(/\[object (\w+)\]/);return t&&t.length?t[1].toLowerCase():""}const Z=e=>null!=e,ee=(e,t="value")=>{if(e<0)throw new Error(`${t} shouldn't be less than zero`)};function te(e,t,a){const n=(e<<16|t<<8|a).toString(16);return"#"+"0".repeat(Math.max(0,6-n.length))+n}function ae(e){const t=[];for(let a=1;a<7;a+=2)t.push(parseInt("0x"+e.slice(a,a+2),16));return t}const ne={id:1e3};function oe(e,t,a,n){return new Promise(((n,o)=>{let r=null;r=a?uni.createSelectorQuery().in(a):uni.createSelectorQuery();const l=e=>{t&&re(e)&&e.length>0||!t&&e?n(e):o(new Error("No nodes found"))};r[t?"selectAll":"select"](e).boundingClientRect(l).exec()}))}function re(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e)}function le(e){return"function"===J(e)||"asyncfunction"===J(e)}function se(e){return"string"===J(e)}function ie(e){return"number"===J(e)}function ce(e){return!(!X(e)||!Z(e))&&(le(e.then)&&le(e.catch))}function ue(e){return void 0===e}function de(e){if(re(e)){const t=e.filter((function(e){return null!=e&&""!==e})).map((function(e){return de(e)})).join(";");return t?t.endsWith(";")?t:t+";":""}if(se(e))return e?e.endsWith(";")?e:e+";":"";if(X(e)){const t=Object.keys(e).filter((function(t){return null!=e[t]&&""!==e[t]})).map((function(t){return[(a=t,a.replace(/[A-Z]/g,(function(e){return"-"+e})).toLowerCase()),e[t]].join(":");var a})).join(";");return t?t.endsWith(";")?t:t+";":""}return""}const pe=(e=1e3/30)=>new U((t=>{const a=setTimeout((()=>{clearTimeout(a),t(!0)}),e)}));function me(e,t=new Map){if(null===e||"object"!=typeof e)return e;if(he(e))return new Date(e.getTime());if(e instanceof RegExp)return new RegExp(e.source,e.flags);if(e instanceof Error){const t=new Error(e.message);return t.stack=e.stack,t}if(t.has(e))return t.get(e);const a=Array.isArray(e)?[]:{};t.set(e,a);for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(a[n]=me(e[n],t));return a}function ve(e,t){return Object.keys(t).forEach((a=>{const n=e[a],o=t[a];X(n)&&X(o)?ve(n,o):e[a]=o})),e}function fe(e,t,a={}){let n,o,r,l=null;const s=!!Z(a.leading)&&a.leading,i=!Z(a.trailing)||a.trailing;function c(){void 0!==n&&(r=e.apply(o,n),n=void 0)}function u(){l=setTimeout((()=>{l=null,i&&c()}),t)}return function(...e){return n=e,o=this,null===l?(s&&c(),u()):i&&(null!==l&&(clearTimeout(l),l=null),u()),r}}const he=e=>"[object Date]"===Object.prototype.toString.call(e)&&!Number.isNaN(e.getTime());function ge(e,t){const a=me(e);return Object.keys(a).forEach((e=>t(a[e],e)&&delete a[e])),a}const ye=[Number,String],_e=e=>({type:e,required:!0}),we=e=>({type:Boolean,default:e}),be=e=>({type:Number,default:e}),ke=e=>({type:ye,default:e}),xe=e=>({type:String,default:e}),Ce={customStyle:xe(""),customClass:xe("")},Ve=__spreadProps(__spreadValues({},Ce),{name:_e(String),color:String,size:ye,classPrefix:xe("wd-icon")}),Ee=(e,t)=>{const a=e.__vccOpts||e;for(const[n,o]of t)a[n]=o;return a},Ne=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-icon",options:{virtualHost:!0,addGlobalClass:!0,styleIsolation:"shared"}}),{props:Ve,emits:["click","touch"],setup(t,{emit:a}){const n=t,o=a,r=e.computed((()=>Z(n.name)&&n.name.includes("/"))),l=e.computed((()=>{const e=n.classPrefix;return`${e} ${n.customClass} ${r.value?"wd-icon--image":e+"-"+n.name}`})),s=e.computed((()=>{const e={};return n.color&&(e.color=n.color),n.size&&(e["font-size"]=Y(n.size)),`${de(e)} ${n.customStyle}`}));function i(e){o("click",e)}return(t,a)=>(e.openBlock(),e.createElementBlock("view",{onClick:i,class:e.normalizeClass(l.value),style:e.normalizeStyle(s.value)},[r.value?(e.openBlock(),e.createElementBlock("image",{key:0,class:"wd-icon__image",src:t.name},null,8,["src"])):e.createCommentVNode("",!0)],6))}})),[["__scopeId","data-v-bef80b7c"]]),Se=__spreadProps(__spreadValues({},Ce),{modelValue:ye,showZero:we(!1),bgColor:String,max:Number,isDot:Boolean,hidden:Boolean,type:xe(void 0),top:ye,right:ye}),Be=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-badge",options:{addGlobalClass:!0,virtualHost:!0,styleIsolation:"shared"}}),{props:Se,setup(t){const a=t,n=e.computed((()=>{const{modelValue:e,max:t,isDot:n}=a;if(n)return"";let o=e;return o&&t&&ie(o)&&!Number.isNaN(o)&&!Number.isNaN(t)&&(o=t<o?`${t}+`:o),o})),o=e.computed((()=>{const e={};return Z(a.bgColor)&&(e.backgroundColor=a.bgColor),Z(a.top)&&(e.top=Y(a.top)),Z(a.right)&&(e.right=Y(a.right)),de(e)})),r=e.computed((()=>!a.hidden&&(n.value||0===n.value&&a.showZero||a.isDot)));return(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["wd-badge",t.customClass]),style:e.normalizeStyle(t.customStyle)},[e.renderSlot(t.$slots,"default",{},void 0,!0),r.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["wd-badge__content","is-fixed",t.type?"wd-badge__content--"+t.type:"",t.isDot?"is-dot":""]),style:e.normalizeStyle(o.value)},e.toDisplayString(n.value),7)):e.createCommentVNode("",!0)],6))}})),[["__scopeId","data-v-019b04f1"]]);function ze(t){const a=e.inject(t,null);if(a){const t=e.getCurrentInstance(),{link:n,unlink:o,internalChildren:r}=a;n(t),e.onUnmounted((()=>o(t)));return{parent:a,index:e.computed((()=>r.indexOf(t)))}}return{parent:null,index:e.ref(-1)}}const $e=Symbol("wd-tabbar"),Pe=__spreadProps(__spreadValues({},Ce),{modelValue:ke(0),fixed:we(!1),bordered:we(!0),safeAreaInsetBottom:we(!1),shape:xe("default"),activeColor:String,inactiveColor:String,placeholder:we(!1),zIndex:be(99)}),Ie=__spreadProps(__spreadValues({},Ce),{title:String,name:ye,icon:String,value:{type:[Number,String,null],default:null},isDot:{type:Boolean,default:void 0},max:Number,badgeProps:Object}),Te=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-tabbar-item",options:{addGlobalClass:!0,virtualHost:!0,styleIsolation:"shared"}}),{props:Ie,setup(t){const a=t,{parent:n,index:o}=ze($e),r=e.computed((()=>{const e=ve(Z(a.badgeProps)?ge(a.badgeProps,ue):{},ge({max:a.max,isDot:a.isDot,modelValue:a.value},ue));return Z(e.max)||(e.max=99),e})),l=e.computed((()=>{const e={};return n&&(s.value&&n.props.activeColor&&(e.color=n.props.activeColor),!s.value&&n.props.inactiveColor&&(e.color=n.props.inactiveColor)),`${de(e)}`})),s=e.computed((()=>{const e=Z(a.name)?a.name:o.value;return!!n&&n.props.modelValue===e}));function i(){const e=Z(a.name)?a.name:o.value;n&&n.setChange({name:e})}return(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(`wd-tabbar-item ${t.customClass}`),style:e.normalizeStyle(t.customStyle),onClick:i},[e.createVNode(Be,e.normalizeProps(e.guardReactiveProps(r.value)),{default:e.withCtx((()=>[e.createElementVNode("view",{class:"wd-tabbar-item__body"},[e.renderSlot(t.$slots,"icon",{active:s.value},void 0,!0),!t.$slots.icon&&t.icon?(e.openBlock(),e.createBlock(Ne,{key:0,name:t.icon,"custom-style":l.value,"custom-class":"wd-tabbar-item__body-icon "+(s.value?"is-active":"is-inactive")},null,8,["name","custom-style","custom-class"])):e.createCommentVNode("",!0),t.title?(e.openBlock(),e.createElementBlock("text",{key:1,style:e.normalizeStyle(l.value),class:e.normalizeClass("wd-tabbar-item__body-title "+(s.value?"is-active":"is-inactive"))},e.toDisplayString(t.title),7)):e.createCommentVNode("",!0)])])),_:3},16)],6))}})),[["__scopeId","data-v-ee9e8b0e"]]);const Ae=(e,t)=>{const a=e.indexOf(t);return-1===a?e.findIndex((e=>void 0!==t.key&&null!==t.key&&e.type===t.type&&e.key===t.key)):a};function De(e,t,a){const n=e&&e.subTree&&e.subTree.children?function(e){const t=[],a=e=>{Array.isArray(e)&&e.forEach((e=>{var n,o;(o=e)&&!0===o.__v_isVNode&&(t.push(e),(null==(n=e.component)?void 0:n.subTree)&&(t.push(e.component.subTree),a(e.component.subTree.children)),e.children&&a(e.children))}))};return a(e),t}(e.subTree.children):[];a.sort(((e,t)=>Ae(n,e.vnode)-Ae(n,t.vnode)));const o=a.map((e=>e.proxy));t.sort(((e,t)=>o.indexOf(e)-o.indexOf(t)))}function Me(t){const a=e.reactive([]),n=e.reactive([]),o=e.getCurrentInstance();return{children:a,linkChildren:r=>{e.provide(t,Object.assign({link:e=>{e.proxy&&(n.push(e),a.push(e.proxy),De(o,a,n))},unlink:e=>{const t=n.indexOf(e);a.splice(t,1),n.splice(t,1)},children:a,internalChildren:n},r))}}}const Oe=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-tabbar",options:{addGlobalClass:!0,virtualHost:!0,styleIsolation:"shared"}}),{props:Pe,emits:["change","update:modelValue"],setup(t,{emit:a}){const n=t,o=a,r=e.ref(""),{proxy:l}=e.getCurrentInstance(),{linkChildren:s}=Me($e);s({props:n,setChange:function(e){let t=e.name;o("update:modelValue",t),o("change",{value:t})}});const i=e.computed((()=>{const e={};return Z(n.zIndex)&&(e["z-index"]=n.zIndex),`${de(e)}${n.customStyle}`}));function c(){n.fixed&&n.placeholder&&oe(".wd-tabbar",!1,l).then((e=>{r.value=Number(e.height)}))}return e.watch([()=>n.fixed,()=>n.placeholder],(()=>{c()}),{deep:!0,immediate:!1}),e.onMounted((()=>{n.fixed&&n.placeholder&&e.nextTick((()=>{c()}))})),(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass({"wd-tabbar__placeholder":t.fixed&&t.placeholder&&t.safeAreaInsetBottom&&"round"===t.shape}),style:e.normalizeStyle({height:e.unref(Y)(r.value)})},[e.createElementVNode("view",{class:e.normalizeClass(`wd-tabbar wd-tabbar--${t.shape} ${t.customClass} ${t.fixed?"is-fixed":""} ${t.safeAreaInsetBottom?"is-safe":""} ${t.bordered?"is-border":""}`),style:e.normalizeStyle(i.value)},[e.renderSlot(t.$slots,"default",{},void 0,!0)],6)],6))}})),[["__scopeId","data-v-eb3a4869"]]);
/*!
  * pinia v2.0.36
  * (c) 2023 Eduardo San Martin Morote
  * @license MIT
  */
let Fe;const je=e=>Fe=e,Ge=Symbol();function Le(e){return e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!=typeof e.toJSON}var Re,He;(He=Re||(Re={})).direct="direct",He.patchObject="patch object",He.patchFunction="patch function";const qe=()=>{};function Qe(t,a,n,o=qe){t.push(a);const r=()=>{const e=t.indexOf(a);e>-1&&(t.splice(e,1),o())};return!n&&e.getCurrentScope()&&e.onScopeDispose(r),r}function Ue(e,...t){e.slice().forEach((e=>{e(...t)}))}function We(t,a){t instanceof Map&&a instanceof Map&&a.forEach(((e,a)=>t.set(a,e))),t instanceof Set&&a instanceof Set&&a.forEach(t.add,t);for(const n in a){if(!a.hasOwnProperty(n))continue;const o=a[n],r=t[n];Le(r)&&Le(o)&&t.hasOwnProperty(n)&&!e.isRef(o)&&!e.isReactive(o)?t[n]=We(r,o):t[n]=o}return t}const Ke=Symbol();const{assign:Ye}=Object;function Xe(t,a,n={},o,r,l){let s;const i=Ye({actions:{}},n),c={deep:!0};let u,d,p,m=e.markRaw([]),v=e.markRaw([]);const f=o.state.value[t];let h;function g(a){let n;u=d=!1,"function"==typeof a?(a(o.state.value[t]),n={type:Re.patchFunction,storeId:t,events:p}):(We(o.state.value[t],a),n={type:Re.patchObject,payload:a,storeId:t,events:p});const r=h=Symbol();e.nextTick().then((()=>{h===r&&(u=!0)})),d=!0,Ue(m,n,o.state.value[t])}l||f||(o.state.value[t]={}),e.ref({});const y=l?function(){const{state:e}=n,t=e?e():{};this.$patch((e=>{Ye(e,t)}))}:qe;function _(e,a){return function(){je(o);const n=Array.from(arguments),r=[],l=[];let s;Ue(v,{args:n,name:e,store:b,after:function(e){r.push(e)},onError:function(e){l.push(e)}});try{s=a.apply(this&&this.$id===t?this:b,n)}catch(i){throw Ue(l,i),i}return s instanceof Promise?s.then((e=>(Ue(r,e),e))).catch((e=>(Ue(l,e),Promise.reject(e)))):(Ue(r,s),s)}}const w={_p:o,$id:t,$onAction:Qe.bind(null,v),$patch:g,$reset:y,$subscribe(a,n={}){const r=Qe(m,a,n.detached,(()=>l())),l=s.run((()=>e.watch((()=>o.state.value[t]),(e=>{("sync"===n.flush?d:u)&&a({storeId:t,type:Re.direct,events:p},e)}),Ye({},c,n))));return r},$dispose:function(){s.stop(),m=[],v=[],o._s.delete(t)}},b=e.reactive(w);o._s.set(t,b);const k=o._e.run((()=>(s=e.effectScope(),s.run((()=>a())))));for(const V in k){const a=k[V];if(e.isRef(a)&&(C=a,!e.isRef(C)||!C.effect)||e.isReactive(a))l||(!f||Le(x=a)&&x.hasOwnProperty(Ke)||(e.isRef(a)?a.value=f[V]:We(a,f[V])),o.state.value[t][V]=a);else if("function"==typeof a){const e=_(V,a);k[V]=e,i.actions[V]=a}}var x,C;return Ye(b,k),Ye(e.toRaw(b),k),Object.defineProperty(b,"$state",{get:()=>o.state.value[t],set:e=>{g((t=>{Ye(t,e)}))}}),o._p.forEach((e=>{Ye(b,s.run((()=>e({store:b,app:o._a,pinia:o,options:i}))))})),f&&l&&n.hydrate&&n.hydrate(b.$state,f),u=!0,d=!0,b}function Je(t,a,n){let o,r;const l="function"==typeof a;function s(t,n){const s=e.getCurrentInstance();(t=t||s&&e.inject(Ge,null))&&je(t),(t=Fe)._s.has(o)||(l?Xe(o,a,r,t):function(t,a,n){const{state:o,actions:r,getters:l}=a,s=n.state.value[t];let i;i=Xe(t,(function(){s||(n.state.value[t]=o?o():{});const a=e.toRefs(n.state.value[t]);return Ye(a,r,Object.keys(l||{}).reduce(((a,o)=>(a[o]=e.markRaw(e.computed((()=>{je(n);const e=n._s.get(t);return l[o].call(e,e)}))),a)),{}))}),a,n,0,!0)}(o,r,t));return t._s.get(o)}return"string"==typeof t?(o=t,r=l?n:a):(r=t,o=t.id),s.$id=o,s}const Ze=Je("globalRoleStore",(()=>{const t=e.ref("user");return{getRole:e.computed((()=>t.value)),setRole:e=>{t.value=e}}}));var et=(e=>(e.system="system",e.merchants="merchants",e.user="user",e))(et||{});const tt=e.defineComponent(__spreadProps(__spreadValues({},{name:"TabBar"}),{__name:"tab-bar",props:{currentPage:{type:String,default:"index"}},setup(t){const a=Ze(),n=t,o={user:[{name:"index",title:"主页",icon:"iconsys-shouye1",route:"/pages/user/index"},{name:"category",title:"分类",icon:"iconsys-fenlei",route:"/pages/user/category"},{name:"mine",title:"我的",icon:"iconsys-user",route:"/pages/user/mine"}],merchants:[{name:"index",title:"主页",icon:"iconsys-shouye1",route:"/pages/merchants/index"},{name:"statistics",title:"统计",icon:"iconsys-tongji",route:"/pages/merchants/statistics"},{name:"orders",title:"订单",icon:"iconsys-dingdanliebiao",route:"/pages/merchants/orders"},{name:"goods",title:"商品管理",icon:"goods",route:"/pages/merchants/goods",useDefaultIcon:!0},{name:"mine",title:"我的",icon:"iconsys-user",route:"/pages/merchants/mine"}],system:[{name:"dashboard",title:"仪表盘",icon:"iconsys-shuju",route:"/pages/sys/dashboard"},{name:"merchants",title:"商户管理",icon:"iconsys-tuandui",route:"/pages/sys/merchants"},{name:"goods",title:"商品管理",icon:"goods",route:"/pages/sys/goods",useDefaultIcon:!0},{name:"system",title:"系统管理",icon:"setting",route:"/pages/sys/system",useDefaultIcon:!0},{name:"mine",title:"我的",icon:"iconsys-user",route:"/pages/sys/mine"}]},r=e.computed((()=>o[a.getRole]||o.user)),l=e.computed((()=>{const e={};return r.value.forEach((t=>{e[t.name]=t.route})),e}));let s=!1;const i=({value:e})=>{var t;if(n.currentPage===e)return;if(s)return;const a=l.value[e];if(a){s=!0,G("log","at components/tab-bar/tab-bar.vue:186","切换到:",a);const e=getCurrentPages(),n=(null==(t=e[e.length-1])?void 0:t.route)||"";r.value.some((e=>n.includes(e.route.substring(1))||a.includes(n))),uni.redirectTo({url:a,success:()=>{G("log","at components/tab-bar/tab-bar.vue:202","redirectTo 跳转成功:",a),setTimeout((()=>{s=!1}),1)},fail:e=>{G("log","at components/tab-bar/tab-bar.vue:209","redirectTo 跳转失败，尝试 reLaunch:",e),uni.reLaunch({url:a,success:()=>{G("log","at components/tab-bar/tab-bar.vue:214","reLaunch 跳转成功:",a),setTimeout((()=>{s=!1}),10)},fail:e=>{G("error","at components/tab-bar/tab-bar.vue:221","页面跳转失败:",e),s=!1}})}})}};return e.onUnmounted((()=>{s=!1})),(a,n)=>{const o=L(e.resolveDynamicComponent("wd-icon"),Ne),l=L(e.resolveDynamicComponent("wd-tabbar-item"),Te),s=L(e.resolveDynamicComponent("wd-tabbar"),Oe);return e.openBlock(),e.createBlock(s,{"model-value":t.currentPage,onChange:i,fixed:"",placeholder:"",safeAreaInsetBottom:"",bordered:"","active-color":"#007aff","inactive-color":"#999999"},{default:e.withCtx((()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(r),(a=>(e.openBlock(),e.createBlock(l,{key:a.name,name:a.name,title:a.title},{icon:e.withCtx((()=>[a.useDefaultIcon?(e.openBlock(),e.createBlock(o,{key:0,name:a.icon,size:"20px",color:t.currentPage===a.name?"#007aff":"#999999"},null,8,["name","color"])):(e.openBlock(),e.createElementBlock("view",{key:1,class:e.normalizeClass(["iconfont-sys",`${a.icon}`]),style:e.normalizeStyle({fontSize:"20px",color:t.currentPage==a.name?"#007aff":"#999999"})},null,6))])),_:2},1032,["name","title"])))),128))])),_:1},8,["model-value"])}}})),at=Symbol("wd-tabs"),nt=__spreadProps(__spreadValues({},Ce),{modelValue:ke(0),slidableNum:be(6),mapNum:be(10),mapTitle:String,sticky:we(!1),offsetTop:be(0),swipeable:we(!1),autoLineWidth:we(!1),lineWidth:ye,lineHeight:ye,color:xe(""),inactiveColor:xe(""),animated:we(!1),duration:be(300),slidable:xe("auto")}),ot=__spreadProps(__spreadValues({},Ce),{name:ye,title:String,disabled:we(!1),lazy:we(!0),badgeProps:Object}),rt=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-tab",options:{addGlobalClass:!0,virtualHost:!0,styleIsolation:"shared"}}),{props:ot,setup(t){const a=t,{proxy:n}=e.getCurrentInstance(),{parent:o,index:r}=ze(at),l=e.computed((()=>!!Z(o)&&o.state.activeIndex===r.value)),s=e.ref(l.value),i=e.computed((()=>{const e={};return l.value||Z(o)&&o.props.animated||(e.display="none"),de(e)})),c=e.computed((()=>!a.lazy||s.value||l.value));return e.watch(l,(e=>{e&&(s.value=!0)})),e.watch((()=>a.name),(e=>{(!Z(e)||ie(e)||se(e))&&o&&function(e){const{name:t}=a;if(null==t||""===t)return;o&&o.children.forEach((t=>{t.$.uid!==e.$.uid&&t.name}))}(n)}),{deep:!0,immediate:!0}),(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(`wd-tab ${t.customClass}`),style:e.normalizeStyle(t.customStyle)},[c.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["wd-tab__body",{"wd-tab__body--inactive":!l.value}]),style:e.normalizeStyle(i.value)},[e.renderSlot(t.$slots,"default",{},void 0,!0)],6)):e.createCommentVNode("",!0)],6))}})),[["__scopeId","data-v-9d733961"]]),lt=__spreadProps(__spreadValues({},Ce),{customContainerClass:xe("")}),st=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-resize",options:{virtualHost:!0,addGlobalClass:!0,styleIsolation:"shared"}}),{props:lt,emits:["resize"],setup(t,{emit:a}){const n=t,o=a,r=e.ref(0),l=e.ref(0),s=e.ref(0),i=e.ref(0),c=e.ref(0),u=e.ref(0),d=e.ref(0),p=e.computed((()=>`${de({width:Y(u.value),height:Y(c.value)})}${n.customStyle}`));let m=()=>{};const{proxy:v}=e.getCurrentInstance(),f=e.ref(`resize${W()}`);function h({lastWidth:e,lastHeight:t}){r.value=1e5+t,l.value=3*c.value+t,s.value=1e5+e,i.value=3*u.value+e}return e.onMounted((()=>{uni.createSelectorQuery().in(v).select(`#${f.value}`).boundingClientRect().exec((([e])=>{let t=e.height,a=e.width;c.value=t,u.value=a,m=()=>{uni.createSelectorQuery().in(v).select(`#${f.value}`).boundingClientRect().exec((([e])=>{if(0==d.value++){const t={};["bottom","top","left","right","height","width"].forEach((a=>{t[a]=e[a]})),o("resize",t)}if(d.value<3)return;const n=e.height,r=e.width;c.value=n,u.value=r;const l=[];if(n!==t&&(t=n,l.push(1)),r!==a&&(a=r,l.push(1)),0!==l.length){const t={};["bottom","top","left","right","height","width"].forEach((a=>{t[a]=e[a]})),o("resize",t)}h({lastWidth:a,lastHeight:t})}))},h({lastWidth:a,lastHeight:t})}))})),(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(`wd-resize ${t.customClass}`),style:e.normalizeStyle(p.value)},[e.createElementVNode("view",{id:f.value,class:e.normalizeClass(`wd-resize__container ${t.customContainerClass}`)},[e.renderSlot(t.$slots,"default",{},void 0,!0),e.createElementVNode("scroll-view",{class:"wd-resize__wrapper","scroll-y":!0,"scroll-top":r.value,"scroll-x":!0,"scroll-left":s.value,onScroll:a[0]||(a[0]=(...t)=>e.unref(m)&&e.unref(m)(...t))},[e.createElementVNode("view",{class:"wd-resize__wrapper--placeholder",style:{height:"100000px",width:"100000px"}})],40,["scroll-top","scroll-left"]),e.createElementVNode("scroll-view",{class:"wd-resize__wrapper","scroll-y":!0,"scroll-top":l.value,"scroll-x":!0,"scroll-left":i.value,onScroll:a[1]||(a[1]=(...t)=>e.unref(m)&&e.unref(m)(...t))},[e.createElementVNode("view",{class:"wd-resize__wrapper--placeholder",style:{height:"250%",width:"250%"}})],40,["scroll-top","scroll-left"])],10,["id"])],6))}})),[["__scopeId","data-v-62bdf40e"]]),it=__spreadProps(__spreadValues({},Ce),{zIndex:be(1),offsetTop:be(0)}),ct=Symbol("wd-sticky-box"),ut=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-sticky",options:{addGlobalClass:!0,virtualHost:!0,styleIsolation:"shared"}}),{props:it,setup(t,{expose:a}){const n=t,o=e.ref(`wd-sticky${W()}`),r=e.ref([]),l=e.reactive({position:"absolute",boxLeaved:!1,top:0,height:0,width:0,state:""}),{parent:s}=ze(ct),{proxy:i}=e.getCurrentInstance(),c=e.computed((()=>{const e={"z-index":n.zIndex,height:Y(l.height),width:Y(l.width)};return l.boxLeaved||(e.position="relative"),`${de(e)}${n.customStyle}`})),u=e.computed((()=>{const e={"z-index":n.zIndex,height:Y(l.height),width:Y(l.width)};return l.boxLeaved||(e.position="relative"),`${de(e)}`})),d=e.computed((()=>de({position:l.position,top:Y(l.top)}))),p=e.computed((()=>0+n.offsetTop));function m(e){return __async(this,null,(function*(){l.width=e.width,l.height=e.height,yield pe(),function(){if(0===l.height&&0===l.width)return;const e=p.value+l.height;(function(){for(;0!==r.value.length;)r.value.pop().disconnect()})(),function(){const e=uni.createIntersectionObserver(i,{thresholds:[0,.5]});return r.value.push(e),e}().relativeToViewport({top:-e}).observe(`#${o.value}`,(e=>{v(e)})),oe(`#${o.value}`,!1,i).then((t=>{Number(t.bottom)<=e&&v({boundingClientRect:t})}))}(),s&&s.observerForChild&&s.observerForChild(i)}))}function v({boundingClientRect:e}){if(s&&s.boxStyle&&l.height>=s.boxStyle.height)return l.position="absolute",void(l.top=0);let t=e.top<=p.value;t=e.top<p.value,t?(l.state="sticky",l.boxLeaved=!1,l.position="fixed",l.top=p.value):(l.state="normal",l.boxLeaved=!1,l.position="absolute",l.top=0)}return a({setPosition:function(e,t,a){l.boxLeaved=e,l.position=t,l.top=a},stickyState:l,offsetTop:n.offsetTop}),(t,a)=>(e.openBlock(),e.createElementBlock("view",{style:e.normalizeStyle(`${c.value};display: inline-block;`)},[e.createElementVNode("view",{class:e.normalizeClass(`wd-sticky ${t.customClass}`),style:e.normalizeStyle(u.value),id:o.value},[e.createElementVNode("view",{class:"wd-sticky__container",style:e.normalizeStyle(d.value)},[e.createVNode(st,{onResize:m,"custom-style":"display: inline-block;"},{default:e.withCtx((()=>[e.renderSlot(t.$slots,"default",{},void 0,!0)])),_:3})],4)],14,["id"])],4))}})),[["__scopeId","data-v-715af4d8"]]),dt=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-sticky-box",options:{addGlobalClass:!0,styleIsolation:"shared"}}),{props:Ce,setup(t){const a=t,n=e.ref(`wd-sticky-box${W()}`),o=e.ref(new Map),r=e.reactive({height:0,width:0}),{proxy:l}=e.getCurrentInstance(),{children:s,linkChildren:i}=Me(ct);function c(e){r.width=e.width,r.height=e.height;const t=o.value;o.value=new Map;for(const[a]of t){u(s.find((e=>e.$.uid===a)))}t.forEach((e=>{e.disconnect()})),t.clear()}function u(e){!function(e){const t=o.value.get(e.$.uid);t&&(t.disconnect(),o.value.delete(e.$.uid))}(e);const t=function(e){const t=uni.createIntersectionObserver(l,{thresholds:[0,.5]});return o.value.set(e.$.uid,t),t}(e),a=e.$.exposed;let s=a.stickyState.height+a.offsetTop;r.height<=a.stickyState.height&&a.setPosition(!1,"absolute",0),t.relativeToViewport({top:-s}).observe(`#${n.value}`,(e=>{d(a,e)})),oe(`#${n.value}`,!1,l).then((e=>{Number(e.bottom)<=s&&d(a,{boundingClientRect:e})})).catch((e=>{}))}function d(e,{boundingClientRect:t}){let a=e.offsetTop;const n=e.stickyState.height+a;let o=t.bottom<=n;if(o=t.bottom<n,o)e.setPosition(!0,"absolute",t.height-e.stickyState.height);else if(t.top<=n&&!o){if("normal"===e.stickyState.state)return;e.setPosition(!1,"fixed",a)}}return i({boxStyle:r,observerForChild:u}),e.onBeforeMount((()=>{o.value=new Map})),(t,o)=>(e.openBlock(),e.createElementBlock("view",{style:{position:"relative"}},[e.createElementVNode("view",{class:e.normalizeClass(`wd-sticky-box ${a.customClass}`),style:e.normalizeStyle(t.customStyle),id:n.value},[e.createVNode(st,{onResize:c},{default:e.withCtx((()=>[e.renderSlot(t.$slots,"default",{},void 0,!0)])),_:3})],14,["id"])]))}})),[["__scopeId","data-v-d250ad24"]]);const pt=e.ref("zh-CN"),mt=e.reactive({"zh-CN":{calendar:{placeholder:"请选择",title:"选择日期",day:"日",week:"周",month:"月",confirm:"确定",startTime:"开始时间",endTime:"结束时间",to:"至",timeFormat:"YY年MM月DD日 HH:mm:ss",dateFormat:"YYYY年MM月DD日",weekFormat:(e,t)=>`${e} 第 ${t} 周`,startWeek:"开始周",endWeek:"结束周",startMonth:"开始月",endMonth:"结束月",monthFormat:"YYYY年MM月"},calendarView:{startTime:"开始",endTime:"结束",weeks:{sun:"日",mon:"一",tue:"二",wed:"三",thu:"四",fri:"五",sat:"六"},rangePrompt:e=>`选择天数不能超过${e}天`,rangePromptWeek:e=>`选择周数不能超过${e}周`,rangePromptMonth:e=>`选择月份不能超过${e}个月`,monthTitle:"YYYY年M月",yearTitle:"YYYY年",month:"M月",hour:e=>`${e}时`,minute:e=>`${e}分`,second:e=>`${e}秒`},collapse:{expand:"展开",retract:"收起"},colPicker:{title:"请选择",placeholder:"请选择",select:"请选择"},datetimePicker:{start:"开始时间",end:"结束时间",to:"至",placeholder:"请选择",confirm:"完成",cancel:"取消"},loadmore:{loading:"正在努力加载中...",finished:"已加载完毕",error:"加载失败",retry:"点击重试"},messageBox:{inputPlaceholder:"请输入",confirm:"确定",cancel:"取消",inputNoValidate:"输入的数据不合法"},numberKeyboard:{confirm:"完成"},pagination:{prev:"上一页",next:"下一页",page:e=>`当前页：${e}`,total:e=>`当前数据：${e}条`,size:e=>`分页大小：${e}`},picker:{cancel:"取消",done:"完成",placeholder:"请选择"},imgCropper:{confirm:"完成",cancel:"取消"},search:{search:"搜索",cancel:"取消"},steps:{wait:"未开始",finished:"已完成",process:"进行中",failed:"失败"},tabs:{all:"全部"},upload:{error:"上传失败"},input:{placeholder:"请输入..."},selectPicker:{title:"请选择",placeholder:"请选择",select:"请选择",confirm:"确认",filterPlaceholder:"搜索"},tag:{placeholder:"请输入",add:"新增标签"},textarea:{placeholder:"请输入..."},tableCol:{indexLabel:"序号"},signature:{confirmText:"确认",clearText:"清空",revokeText:"撤销",restoreText:"恢复"}}}),vt={messages:()=>mt[pt.value],use(e,t){pt.value=e,t&&this.add({[e]:t})},add(e={}){ve(mt,e)}},ft=e=>{const t=e?e.replace(/-(\w)/g,((e,t)=>t.toUpperCase()))+".":"";return{translate:(e,...a)=>{const n=((e,t)=>{const a=t.split(".");try{return a.reduce(((e,t)=>null!=e?e[t]:void 0),e)}catch(n){return}})(vt.messages(),t+e);return le(n)?n(...a):Z(n)?n:`${t}${e}`}}},ht=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-tabs",options:{addGlobalClass:!0,virtualHost:!0,styleIsolation:"shared"}}),{props:nt,emits:["change","disabled","click","update:modelValue"],setup(t,{expose:a,emit:n}){const o=".wd-tabs__nav-item",r=t,l=n,{translate:s}=ft("tabs"),i=e.reactive({activeIndex:0,lineStyle:"display:none;",useInnerLine:!1,inited:!1,animating:!1,mapShow:!1,scrollLeft:0}),{children:c,linkChildren:u}=Me(at);u({state:i,props:r});const{proxy:d}=e.getCurrentInstance(),p=function(){const t=e.ref(""),a=e.ref(0),n=e.ref(0),o=e.ref(0),r=e.ref(0),l=e.ref(0),s=e.ref(0);return{touchStart:function(e){const i=e.touches[0];t.value="",a.value=0,n.value=0,o.value=0,r.value=0,l.value=i.clientX,s.value=i.clientY},touchMove:function(e){const i=e.touches[0];a.value=i.clientX-l.value,n.value=i.clientY-s.value,o.value=Math.abs(a.value),r.value=Math.abs(n.value),t.value=o.value>r.value?"horizontal":o.value<r.value?"vertical":""},direction:t,deltaX:a,deltaY:n,offsetX:o,offsetY:r,startX:l,startY:s}}(),m=e.computed((()=>"always"===r.slidable||c.length>r.slidableNum)),v=e.computed((()=>r.animated?de({left:-100*i.activeIndex+"%","transition-duration":r.duration+"ms","-webkit-transition-duration":r.duration+"ms"}):"")),f=(e,t)=>Z(e.name)?e.name:t,h=(e=0,t=!1,a=!0)=>{0!==c.length&&(e=V(e),c[e].disabled||(i.activeIndex=e,a&&(_(!1===t),w()),function(){if(!i.inited)return;const e=f(c[i.activeIndex],i.activeIndex);e!==r.modelValue&&(l("change",{index:i.activeIndex,name:e}),l("update:modelValue",e))}()))},g=fe(h,100,{leading:!0});function y(){i.mapShow?(i.animating=!1,setTimeout((()=>{i.mapShow=!1}),300)):(i.mapShow=!0,setTimeout((()=>{i.animating=!0}),100))}function _(e=!0){return __async(this,null,(function*(){if(!i.inited)return;const{autoLineWidth:t,lineWidth:a,lineHeight:n}=r;try{const r={};if(Z(a))r.width=Y(a);else if(t){const e=yield oe(".wd-tabs__nav-item-text",!0,d),t=Number(e[i.activeIndex].width);r.width=Y(t)}Z(n)&&(r.height=Y(n),r.borderRadius=`calc(${Y(n)} / 2)`);const l=yield oe(o,!0,d),s=l[i.activeIndex];let c=l.slice(0,i.activeIndex).reduce(((e,t)=>e+Number(t.width)),0)+Number(s.width)/2;c&&(r.transform=`translateX(${c}px) translateX(-50%)`,e&&(r.transition="width 0.3s cubic-bezier(0.4, 0, 0.2, 1), transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);"),i.useInnerLine=!1,i.lineStyle=de(r))}catch(l){}}))}function w(){i.inited&&Promise.all([oe(o,!0,d),oe(".wd-tabs__nav-container",!1,d)]).then((([e,t])=>{const a=e[i.activeIndex],n=e.slice(0,i.activeIndex).reduce(((e,t)=>e+t.width),0)-(t.width-Number(a.width))/2;n===i.scrollLeft?i.scrollLeft=n+Math.random()/1e4:i.scrollLeft=n}))}function b(e){if(void 0===e)return;const{disabled:t}=c[e],a=f(c[e],e);t?l("disabled",{index:e,name:a}):(i.mapShow&&y(),g(e),l("click",{index:e,name:a}))}function k(e){r.swipeable&&p.touchStart(e)}function x(e){r.swipeable&&p.touchMove(e)}function C(){if(!r.swipeable)return;const{direction:e,deltaX:t,offsetX:a}=p;"horizontal"===e.value&&a.value>=50&&(t.value>0&&0!==i.activeIndex?g(i.activeIndex-1):t.value<0&&i.activeIndex!==c.length-1&&g(i.activeIndex+1))}function V(e){if(ie(e)&&e>=c.length&&(e=0),se(e)){const t=c.findIndex((t=>t.name===e));e=-1===t?0:t}return e}return e.watch((()=>r.modelValue),(e=>{!ie(e)&&se(e),""===e||Z(e)}),{immediate:!0,deep:!0}),e.watch((()=>r.modelValue),(e=>{const t=V(e);g(e,!1,t!==i.activeIndex)}),{immediate:!1,deep:!0}),e.watch((()=>c.length),(()=>{i.inited&&e.nextTick((()=>{g(r.modelValue)}))})),e.watch((()=>r.slidableNum),(e=>{ee(e,"slidableNum")})),e.watch((()=>r.mapNum),(e=>{ee(e,"mapNum")})),e.onMounted((()=>{i.inited=!0,e.nextTick((()=>{h(r.modelValue,!0),i.useInnerLine=!0}))})),a({setActive:g,scrollIntoView:w,updateLineStyle:_}),(t,a)=>{const n=L(e.resolveDynamicComponent("wd-badge"),Be);return t.sticky?(e.openBlock(),e.createBlock(dt,{key:0},{default:e.withCtx((()=>[e.createElementVNode("view",{class:e.normalizeClass(`wd-tabs ${t.customClass} ${m.value?"is-slide":""} ${t.mapNum<e.unref(c).length&&0!==t.mapNum?"is-map":""}`),style:e.normalizeStyle(t.customStyle)},[e.createVNode(ut,{"offset-top":t.offsetTop},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"wd-tabs__nav wd-tabs__nav--sticky"},[e.createElementVNode("view",{class:"wd-tabs__nav--wrap"},[e.createElementVNode("scroll-view",{"scroll-x":m.value,"scroll-with-animation":"","scroll-left":i.scrollLeft},[e.createElementVNode("view",{class:"wd-tabs__nav-container"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(c),((a,o)=>(e.openBlock(),e.createElementBlock("view",{onClick:e=>b(o),key:o,class:e.normalizeClass(`wd-tabs__nav-item  ${i.activeIndex===o?"is-active":""} ${a.disabled?"is-disabled":""}`),style:e.normalizeStyle(i.activeIndex===o?t.color?"color:"+t.color:"":t.inactiveColor?"color:"+t.inactiveColor:"")},[a.badgeProps?(e.openBlock(),e.createBlock(n,e.normalizeProps(e.mergeProps({key:0},a.badgeProps)),{default:e.withCtx((()=>[e.createElementVNode("text",{class:"wd-tabs__nav-item-text"},e.toDisplayString(a.title),1)])),_:2},1040)):(e.openBlock(),e.createElementBlock("text",{key:1,class:"wd-tabs__nav-item-text"},e.toDisplayString(a.title),1)),i.activeIndex===o&&i.useInnerLine?(e.openBlock(),e.createElementBlock("view",{key:2,class:"wd-tabs__line wd-tabs__line--inner"})):e.createCommentVNode("",!0)],14,["onClick"])))),128)),e.createElementVNode("view",{class:"wd-tabs__line",style:e.normalizeStyle(i.lineStyle)},null,4)])],8,["scroll-x","scroll-left"])]),t.mapNum<e.unref(c).length&&0!==t.mapNum?(e.openBlock(),e.createElementBlock("view",{key:0,class:"wd-tabs__map"},[e.createElementVNode("view",{class:e.normalizeClass("wd-tabs__map-btn  "+(i.animating?"is-open":"")),onClick:y},[e.createElementVNode("view",{class:e.normalizeClass("wd-tabs__map-arrow  "+(i.animating?"is-open":""))},[e.createVNode(Ne,{name:"arrow-down"})],2)],2),e.createElementVNode("view",{class:"wd-tabs__map-header",style:e.normalizeStyle(`${i.mapShow?"":"display:none;"}  ${i.animating?"opacity:1;":""}`)},e.toDisplayString(t.mapTitle||e.unref(s)("all")),5),e.createElementVNode("view",{class:e.normalizeClass("wd-tabs__map-body  "+(i.animating?"is-open":"")),style:e.normalizeStyle(i.mapShow?"":"display:none")},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(c),((a,n)=>(e.openBlock(),e.createElementBlock("view",{class:"wd-tabs__map-nav-item",key:n,onClick:e=>b(n)},[e.createElementVNode("view",{class:e.normalizeClass(`wd-tabs__map-nav-btn ${i.activeIndex===n?"is-active":""}  ${a.disabled?"is-disabled":""}`),style:e.normalizeStyle(i.activeIndex===n?t.color?"color:"+t.color+";border-color:"+t.color:"":t.inactiveColor?"color:"+t.inactiveColor:"")},e.toDisplayString(a.title),7)],8,["onClick"])))),128))],6)])):e.createCommentVNode("",!0)])])),_:1},8,["offset-top"]),e.createElementVNode("view",{class:"wd-tabs__container",onTouchstart:k,onTouchmove:x,onTouchend:C,onTouchcancel:C},[e.createElementVNode("view",{class:e.normalizeClass(["wd-tabs__body",t.animated?"is-animated":""]),style:e.normalizeStyle(v.value)},[e.renderSlot(t.$slots,"default",{},void 0,!0)],6)],32),e.createElementVNode("view",{class:"wd-tabs__mask",style:e.normalizeStyle(`${i.mapShow?"":"display:none;"} ${i.animating?"opacity:1;":""}`),onClick:y},null,4)],6)])),_:3})):(e.openBlock(),e.createElementBlock("view",{key:1,class:e.normalizeClass(`wd-tabs ${t.customClass} ${m.value?"is-slide":""} ${t.mapNum<e.unref(c).length&&0!==t.mapNum?"is-map":""}`)},[e.createElementVNode("view",{class:"wd-tabs__nav"},[e.createElementVNode("view",{class:"wd-tabs__nav--wrap"},[e.createElementVNode("scroll-view",{"scroll-x":m.value,"scroll-with-animation":"","scroll-left":i.scrollLeft},[e.createElementVNode("view",{class:"wd-tabs__nav-container"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(c),((a,o)=>(e.openBlock(),e.createElementBlock("view",{onClick:e=>b(o),key:o,class:e.normalizeClass(`wd-tabs__nav-item ${i.activeIndex===o?"is-active":""} ${a.disabled?"is-disabled":""}`),style:e.normalizeStyle(i.activeIndex===o?t.color?"color:"+t.color:"":t.inactiveColor?"color:"+t.inactiveColor:"")},[a.badgeProps?(e.openBlock(),e.createBlock(n,e.mergeProps({key:0,"custom-class":"wd-tabs__nav-item-badge"},a.badgeProps),{default:e.withCtx((()=>[e.createElementVNode("text",{class:"wd-tabs__nav-item-text"},e.toDisplayString(a.title),1)])),_:2},1040)):(e.openBlock(),e.createElementBlock("text",{key:1,class:"wd-tabs__nav-item-text"},e.toDisplayString(a.title),1)),i.activeIndex===o&&i.useInnerLine?(e.openBlock(),e.createElementBlock("view",{key:2,class:"wd-tabs__line wd-tabs__line--inner"})):e.createCommentVNode("",!0)],14,["onClick"])))),128)),e.createElementVNode("view",{class:"wd-tabs__line",style:e.normalizeStyle(i.lineStyle)},null,4)])],8,["scroll-x","scroll-left"])]),t.mapNum<e.unref(c).length&&0!==t.mapNum?(e.openBlock(),e.createElementBlock("view",{key:0,class:"wd-tabs__map"},[e.createElementVNode("view",{class:"wd-tabs__map-btn",onClick:y},[e.createElementVNode("view",{class:e.normalizeClass("wd-tabs__map-arrow "+(i.animating?"is-open":""))},[e.createVNode(Ne,{name:"arrow-down"})],2)]),e.createElementVNode("view",{class:"wd-tabs__map-header",style:e.normalizeStyle(`${i.mapShow?"":"display:none;"}  ${i.animating?"opacity:1;":""}`)},e.toDisplayString(t.mapTitle||e.unref(s)("all")),5),e.createElementVNode("view",{class:e.normalizeClass("wd-tabs__map-body "+(i.animating?"is-open":"")),style:e.normalizeStyle(i.mapShow?"":"display:none")},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(c),((t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"wd-tabs__map-nav-item",key:a,onClick:e=>b(a)},[e.createElementVNode("view",{class:e.normalizeClass(`wd-tabs__map-nav-btn ${i.activeIndex===a?"is-active":""}  ${t.disabled?"is-disabled":""}`)},e.toDisplayString(t.title),3)],8,["onClick"])))),128))],6)])):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"wd-tabs__container",onTouchstart:k,onTouchmove:x,onTouchend:C,onTouchcancel:C},[e.createElementVNode("view",{class:e.normalizeClass(["wd-tabs__body",t.animated?"is-animated":""]),style:e.normalizeStyle(v.value)},[e.renderSlot(t.$slots,"default",{},void 0,!0)],6)],32),e.createElementVNode("view",{class:"wd-tabs__mask",style:e.normalizeStyle(`${i.mapShow?"":"display:none;"}  ${i.animating?"opacity:1":""}`),onClick:y},null,4)],2))}}})),[["__scopeId","data-v-dbb5bc2a"]]);let gt=null;function yt(){return gt||(gt=function(){const t=e.ref(0),a=e.ref(0),n=e.ref(0),o=e.ref(!1),r=()=>{var e,r;try{const n=uni.getSystemInfoSync();G("log","at hooks/useSafeArea.ts:20","系统信息:",n);let l=0,s=0;l=(null==(e=n.safeAreaInsets)?void 0:e.top)?n.safeAreaInsets.top:n.statusBarHeight?n.statusBarHeight:44,(null==(r=n.safeAreaInsets)?void 0:r.bottom)&&(s=n.safeAreaInsets.bottom),t.value=l,a.value=s,G("log","at hooks/useSafeArea.ts:58","设置安全区域 - 顶部:",l,"底部:",s),o.value=!0}catch(l){G("error","at hooks/useSafeArea.ts:80","获取系统信息失败:",l),t.value=44,a.value=0,n.value=90,o.value=!0}};return o.value||r(),{safeAreaInsetsTop:e.readonly(t),safeAreaInsetsBottom:e.readonly(a),rightSafeArea:e.readonly(n),isInitialized:e.readonly(o),initSafeArea:r}}()),gt}const _t=[..."ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"],wt=e=>e.replace(/[+/]/g,(e=>"+"===e?"-":"_")).replace(/=+\$/m,""),bt="function"==typeof btoa?e=>btoa(e):e=>{if(e.charCodeAt(0)>255)throw new RangeError("The string contains invalid characters.");return((e,t=!1)=>{let a="";for(let n=0,o=e.length;n<o;n+=3){const[t,o,r]=[e[n],e[n+1],e[n+2]],l=t<<16|o<<8|r;a+=_t[l>>>18],a+=_t[l>>>12&63],a+=void 0!==o?_t[l>>>6&63]:"=",a+=void 0!==r?_t[63&l]:"="}return t?wt(a):a})(Uint8Array.from(e,(e=>e.charCodeAt(0))))};function kt(e,t=!1){const a=bt((e=>unescape(encodeURIComponent(e)))(e));return t?wt(a):a}const xt=__spreadProps(__spreadValues({},Ce),{type:xe("ring"),color:xe("#4D80F0"),size:ke("")}),Ct=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-loading",options:{virtualHost:!0,addGlobalClass:!0,styleIsolation:"shared"}}),{props:xt,setup(t){const a=ne.id++,n=ne.id++,o=ne.id++,r={outline:(e="#4D80F0")=>`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 42 42"><defs><linearGradient x1="100%" y1="0%" x2="0%" y2="0%" id="${a}"><stop stop-color="#FFF" offset="0%" stop-opacity="0"/><stop stop-color="#FFF" offset="100%"/></linearGradient></defs><g fill="none" fill-rule="evenodd"><path d="M21 1c11.046 0 20 8.954 20 20s-8.954 20-20 20S1 32.046 1 21 9.954 1 21 1zm0 7C13.82 8 8 13.82 8 21s5.82 13 13 13 13-5.82 13-13S28.18 8 21 8z" fill="${e}"/><path d="M4.599 21c0 9.044 7.332 16.376 16.376 16.376 9.045 0 16.376-7.332 16.376-16.376" stroke="url(#${a}) " stroke-width="3.5" stroke-linecap="round"/></g></svg>`,ring:(e="#4D80F0",t="#a6bff7")=>`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><linearGradient id="${n}" gradientUnits="userSpaceOnUse" x1="50" x2="50" y2="180"><stop offset="0" stop-color="${e}"></stop> <stop offset="1" stop-color="${t}"></stop></linearGradient> <path fill="url(#${n})" d="M20 100c0-44.1 35.9-80 80-80V0C44.8 0 0 44.8 0 100s44.8 100 100 100v-20c-44.1 0-80-35.9-80-80z"></path> <linearGradient id="${o}" gradientUnits="userSpaceOnUse" x1="150" y1="20" x2="150" y2="180"><stop offset="0" stop-color="#fff" stop-opacity="0"></stop> <stop offset="1" stop-color="${t}"></stop></linearGradient> <path fill="url(#${o})" d="M100 0v20c44.1 0 80 35.9 80 80s-35.9 80-80 80v20c55.2 0 100-44.8 100-100S155.2 0 100 0z"></path> <circle cx="100" cy="10" r="10" fill="${e}"></circle></svg>`},l=t,s=e.ref(""),i=e.ref(""),c=e.ref(null);e.watch((()=>l.size),(e=>{c.value=Y(e)}),{deep:!0,immediate:!0}),e.watch((()=>l.type),(()=>{d()}),{deep:!0,immediate:!0});const u=e.computed((()=>{const e={};return Z(c.value)&&(e.height=Y(c.value),e.width=Y(c.value)),`${de(e)} ${l.customStyle}`}));function d(){const{type:e,color:t}=l;let a=Z(e)?e:"ring";const n=`"data:image/svg+xml;base64,${kt("ring"===a?r[a](t,i.value):r[a](t))}"`;s.value=n}return e.onBeforeMount((()=>{i.value=((e,t,a=2)=>{const n=ae(e),o=ae(t),r=(o[0]-n[0])/a,l=(o[1]-n[1])/a,s=(o[2]-n[2])/a,i=[];for(let c=0;c<a;c++)i.push(te(parseInt(String(r*c+n[0])),parseInt(String(l*c+n[1])),parseInt(String(s*c+n[2]))));return i})(l.color,"#ffffff",2)[1],d()})),(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(`wd-loading ${l.customClass}`),style:e.normalizeStyle(u.value)},[e.createElementVNode("view",{class:"wd-loading__body"},[e.createElementVNode("view",{class:"wd-loading__svg",style:e.normalizeStyle(`background-image: url(${s.value});`)},null,4)])],6))}})),[["__scopeId","data-v-4224d8d3"]]),Vt=Ee(e.defineComponent({__name:"index",props:{goodsList:{},columnCount:{default:2},showPrice:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},hasMore:{type:Boolean,default:!0},loadingText:{default:"加载中..."},noMoreText:{default:"没有更多商品了"}},emits:["goods-click","like-click"],setup(t,{emit:a}){const n=t,o=a,r=e.computed((()=>{const e=Array.from({length:n.columnCount},(()=>[])),t=Array(n.columnCount).fill(0);return n.goodsList.forEach((a=>{const o=t.indexOf(Math.min(...t));e[o].push(a);const r=a.imageHeight+(n.showPrice?134:114);t[o]+=r})),e}));return(t,a)=>{const n=L(e.resolveDynamicComponent("wd-icon"),Ne),l=L(e.resolveDynamicComponent("wd-loading"),Ct);return e.openBlock(),e.createElementBlock("view",{class:"waterfall-goods"},[e.createElementVNode("view",{class:"waterfall-container"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(r.value,((a,r)=>(e.openBlock(),e.createElementBlock("view",{class:"waterfall-column",key:r},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a,(a=>(e.openBlock(),e.createElementBlock("view",{class:"goods-card",key:a.id,onClick:e=>(e=>{o("goods-click",e)})(a)},[e.createElementVNode("view",{class:"goods-image-container"},[a.image?(e.openBlock(),e.createElementBlock("image",{key:0,class:"goods-image",src:a.image,mode:"aspectFill",style:e.normalizeStyle({height:a.imageHeight+"px"})},null,12,["src"])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"goods-image-placeholder",style:e.normalizeStyle({height:a.imageHeight+"px",backgroundColor:a.color})},null,4))]),e.createElementVNode("view",{class:"goods-info"},[e.createElementVNode("text",{class:"goods-title"},e.toDisplayString(a.title),1),t.showPrice?(e.openBlock(),e.createElementBlock("view",{key:0,class:"goods-price"},[e.createElementVNode("text",{class:"current-price"},"¥"+e.toDisplayString(a.price),1),a.originalPrice?(e.openBlock(),e.createElementBlock("text",{key:0,class:"original-price"}," ¥"+e.toDisplayString(a.originalPrice),1)):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"goods-stats"},[e.createElementVNode("text",{class:"goods-sales"},"已售"+e.toDisplayString(a.sales)+"件",1),e.createElementVNode("view",{class:"goods-like",onClick:e.withModifiers((e=>(e=>{o("like-click",e)})(a)),["stop"])},[e.createVNode(n,{name:"like",color:a.isLiked?"#ff4757":"#ccc",size:"14px"},null,8,["color"])],8,["onClick"])])])],8,["onClick"])))),128))])))),128))]),t.loading||!t.hasMore?(e.openBlock(),e.createElementBlock("view",{key:0,class:"loading-container"},[t.loading?(e.openBlock(),e.createElementBlock("view",{key:0,class:"loading-item"},[e.createVNode(l,{size:"20px"}),e.createElementVNode("text",{class:"loading-text"},e.toDisplayString(t.loadingText),1)])):t.hasMore?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:1,class:"no-more-item"},[e.createElementVNode("text",{class:"no-more-text"},e.toDisplayString(t.noMoreText),1)]))])):e.createCommentVNode("",!0)])}}}),[["__scopeId","data-v-4cc41be9"]]),Et=Ee(e.defineComponent({__name:"index",props:{goodsList:{},showPrice:{type:Boolean,default:!0},loading:{type:Boolean,default:!1},hasMore:{type:Boolean,default:!0},loadingText:{default:"加载中..."},noMoreText:{default:"没有更多商品了"}},emits:["goods-click","like-click"],setup(t,{emit:a}){const n=a;return(t,a)=>{const o=L(e.resolveDynamicComponent("wd-icon"),Ne),r=L(e.resolveDynamicComponent("wd-loading"),Ct);return e.openBlock(),e.createElementBlock("view",{class:"goods-list"},[e.createElementVNode("view",{class:"goods-container"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.goodsList,(a=>(e.openBlock(),e.createElementBlock("view",{class:"goods-item",key:a.id,onClick:e=>(e=>{n("goods-click",e)})(a)},[e.createElementVNode("view",{class:"goods-image-container"},[a.image?(e.openBlock(),e.createElementBlock("image",{key:0,class:"goods-image",src:a.image,mode:"aspectFill"},null,8,["src"])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"goods-image-placeholder",style:e.normalizeStyle({backgroundColor:a.color})},null,4))]),e.createElementVNode("view",{class:"goods-info"},[e.createElementVNode("text",{class:"goods-title"},e.toDisplayString(a.title),1),t.showPrice?(e.openBlock(),e.createElementBlock("view",{key:0,class:"goods-price"},[e.createElementVNode("text",{class:"current-price"},"¥"+e.toDisplayString(a.price),1),a.originalPrice?(e.openBlock(),e.createElementBlock("text",{key:0,class:"original-price"},"¥"+e.toDisplayString(a.originalPrice),1)):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"goods-bottom"},[e.createElementVNode("text",{class:"goods-sales"},"已售"+e.toDisplayString(a.sales)+"件",1),e.createElementVNode("view",{class:"goods-like",onClick:e.withModifiers((e=>(e=>{n("like-click",e)})(a)),["stop"])},[e.createVNode(o,{name:"like",color:a.isLiked?"#ff4757":"#ccc",size:"16px"},null,8,["color"])],8,["onClick"])])])],8,["onClick"])))),128))]),t.loading||!t.hasMore?(e.openBlock(),e.createElementBlock("view",{key:0,class:"loading-container"},[t.loading?(e.openBlock(),e.createElementBlock("view",{key:0,class:"loading-item"},[e.createVNode(r,{size:"20px"}),e.createElementVNode("text",{class:"loading-text"},e.toDisplayString(t.loadingText),1)])):t.hasMore?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:1,class:"no-more-item"},[e.createElementVNode("text",{class:"no-more-text"},e.toDisplayString(t.noMoreText),1)]))])):e.createCommentVNode("",!0)])}}}),[["__scopeId","data-v-44cf208e"]]),Nt=Symbol("wd-cell-group");__spreadValues({},Ce);const St=Symbol("wd-form");__spreadValues({},Ce);const Bt=__spreadProps(__spreadValues({},Ce),{customInputClass:xe(""),customLabelClass:xe(""),placeholder:String,placeholderStyle:String,placeholderClass:xe(""),cursorSpacing:be(0),cursor:be(-1),selectionStart:be(-1),selectionEnd:be(-1),adjustPosition:we(!0),holdKeyboard:we(!1),confirmType:xe("done"),confirmHold:we(!1),focus:we(!1),type:xe("text"),maxlength:{type:Number,default:-1},disabled:we(!1),alwaysEmbed:we(!1),alignRight:we(!1),modelValue:ke(""),showPassword:we(!1),clearable:we(!1),readonly:we(!1),prefixIcon:String,suffixIcon:String,showWordLimit:we(!1),label:String,labelWidth:xe(""),size:String,error:we(!1),center:we(!1),noBorder:we(!1),required:we(!1),prop:String,rules:{type:Array,default:()=>[]},clearTrigger:xe("always"),focusWhenClear:we(!0),ignoreCompositionEvent:we(!0),inputmode:xe("text")}),zt=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-input",options:{virtualHost:!0,addGlobalClass:!0,styleIsolation:"shared"}}),{props:Bt,emits:["update:modelValue","clear","blur","focus","input","keyboardheightchange","confirm","clicksuffixicon","clickprefixicon","click"],setup(t,{emit:a}){const n=t,o=a,r=e.useSlots(),{translate:l}=ft("input"),s=e.ref(!1),i=e.ref(!1),c=e.ref(!1),u=e.ref(!1),d=e.ref(function(){const e=function(e){const{maxlength:t}=n;if(Z(t)&&-1!==t&&String(e).length>t)return e.toString().slice(0,t);return e}(n.modelValue);t=e,a=n.modelValue,((e,t)=>{if(e===t)return!0;if(!Array.isArray(e)||!Array.isArray(t))return!1;if(e.length!==t.length)return!1;for(let a=0;a<e.length;++a)if(e[a]!==t[a])return!1;return!0})(String(t),String(a))||o("update:modelValue",e);var t,a;return e}()),p=function(){const{parent:t,index:a}=ze(Nt);return{border:e.computed((()=>t&&t.props.border&&a.value))}}();e.watch((()=>n.focus),(e=>{c.value=e}),{immediate:!0,deep:!0}),e.watch((()=>n.modelValue),(e=>{d.value=Z(e)?String(e):""}));const{parent:m}=ze(St),v=e.computed((()=>Z(n.placeholder)?n.placeholder:l("placeholder"))),f=e.computed((()=>{const{disabled:e,readonly:t,clearable:a,clearTrigger:o}=n;return!(!a||t||e||!d.value||!("always"===o||"focus"===n.clearTrigger&&u.value))})),h=e.computed((()=>{const{disabled:e,readonly:t,maxlength:a,showWordLimit:o}=n;return Boolean(!e&&!t&&Z(a)&&a>-1&&o)})),g=e.computed((()=>m&&n.prop&&m.errorMessages&&m.errorMessages[n.prop]?m.errorMessages[n.prop]:"")),y=e.computed((()=>{let e=!1;if(m&&m.props.rules){const t=m.props.rules;for(const a in t)Object.prototype.hasOwnProperty.call(t,a)&&a===n.prop&&Array.isArray(t[a])&&(e=t[a].some((e=>e.required)))}return n.required||n.rules.some((e=>e.required))||e})),_=e.computed((()=>`wd-input  ${n.label||r.label?"is-cell":""} ${n.center?"is-center":""} ${p.border.value?"is-border":""} ${n.size?"is-"+n.size:""} ${n.error?"is-error":""} ${n.disabled?"is-disabled":""}  ${d.value&&String(d.value).length>0?"is-not-empty":""}  ${n.noBorder?"is-no-border":""} ${n.customClass}`)),w=e.computed((()=>`wd-input__label ${n.customLabelClass} ${y.value?"is-required":""}`)),b=e.computed((()=>`wd-input__placeholder  ${n.placeholderClass}`)),k=e.computed((()=>n.labelWidth?de({"min-width":n.labelWidth,"max-width":n.labelWidth}):""));function x(){s.value=!s.value}function C(){return __async(this,null,(function*(){i.value=!0,u.value=!1,d.value="",n.focusWhenClear&&(c.value=!1),yield pe(),n.focusWhenClear&&(c.value=!0,u.value=!0),o("update:modelValue",d.value),o("clear")}))}function V(){return __async(this,null,(function*(){yield pe(150),i.value?i.value=!1:(u.value=!1,o("blur",{value:d.value}))}))}function E({detail:e}){u.value=!0,o("focus",e)}function N({detail:e}){o("update:modelValue",d.value),o("input",e)}function S({detail:e}){o("keyboardheightchange",e)}function B({detail:e}){o("confirm",e)}function z(){o("clicksuffixicon")}function $(){o("clickprefixicon")}function P(e){o("click",e)}return(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(_.value),style:e.normalizeStyle(t.customStyle),onClick:P},[t.label||t.$slots.label?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(w.value),style:e.normalizeStyle(k.value)},[t.prefixIcon||t.$slots.prefix?(e.openBlock(),e.createElementBlock("view",{key:0,class:"wd-input__prefix"},[t.prefixIcon&&!t.$slots.prefix?(e.openBlock(),e.createBlock(Ne,{key:0,"custom-class":"wd-input__icon",name:t.prefixIcon,onClick:$},null,8,["name"])):e.renderSlot(t.$slots,"prefix",{key:1},void 0,!0)])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"wd-input__label-inner"},[t.label&&!t.$slots.label?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createTextVNode(e.toDisplayString(t.label),1)],64)):e.renderSlot(t.$slots,"label",{key:1},void 0,!0)])],6)):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"wd-input__body"},[e.createElementVNode("view",{class:"wd-input__value"},[!t.prefixIcon&&!t.$slots.prefix||t.label?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:"wd-input__prefix"},[t.prefixIcon&&!t.$slots.prefix?(e.openBlock(),e.createBlock(Ne,{key:0,"custom-class":"wd-input__icon",name:t.prefixIcon,onClick:$},null,8,["name"])):e.renderSlot(t.$slots,"prefix",{key:1},void 0,!0)])),e.withDirectives(e.createElementVNode("input",{class:e.normalizeClass(["wd-input__inner",t.prefixIcon?"wd-input__inner--prefix":"",h.value?"wd-input__inner--count":"",t.alignRight?"is-align-right":"",t.customInputClass]),type:t.type,password:t.showPassword&&!s.value,"onUpdate:modelValue":a[0]||(a[0]=e=>d.value=e),placeholder:v.value,disabled:t.disabled||t.readonly,maxlength:t.maxlength,focus:c.value,"confirm-type":t.confirmType,"confirm-hold":t.confirmHold,cursor:t.cursor,"cursor-spacing":t.cursorSpacing,"placeholder-style":t.placeholderStyle,"selection-start":t.selectionStart,"selection-end":t.selectionEnd,"adjust-position":t.adjustPosition,"hold-keyboard":t.holdKeyboard,"always-embed":t.alwaysEmbed,"placeholder-class":b.value,ignoreCompositionEvent:t.ignoreCompositionEvent,inputmode:t.inputmode,onInput:N,onFocus:E,onBlur:V,onConfirm:B,onKeyboardheightchange:S},null,42,["type","password","placeholder","disabled","maxlength","focus","confirm-type","confirm-hold","cursor","cursor-spacing","placeholder-style","selection-start","selection-end","adjust-position","hold-keyboard","always-embed","placeholder-class","ignoreCompositionEvent","inputmode"]),[[e.vModelDynamic,d.value]]),n.readonly?(e.openBlock(),e.createElementBlock("view",{key:1,class:"wd-input__readonly-mask"})):e.createCommentVNode("",!0),f.value||t.showPassword||t.suffixIcon||h.value||t.$slots.suffix?(e.openBlock(),e.createElementBlock("view",{key:2,class:"wd-input__suffix"},[f.value?(e.openBlock(),e.createBlock(Ne,{key:0,"custom-class":"wd-input__clear",name:"error-fill",onClick:C})):e.createCommentVNode("",!0),t.showPassword?(e.openBlock(),e.createBlock(Ne,{key:1,"custom-class":"wd-input__icon",name:s.value?"view":"eye-close",onClick:x},null,8,["name"])):e.createCommentVNode("",!0),h.value?(e.openBlock(),e.createElementBlock("view",{key:2,class:"wd-input__count"},[e.createElementVNode("text",{class:e.normalizeClass([d.value&&String(d.value).length>0?"wd-input__count-current":"",String(d.value).length>t.maxlength?"is-error":""])},e.toDisplayString(String(d.value).length),3),e.createTextVNode(" /"+e.toDisplayString(t.maxlength),1)])):e.createCommentVNode("",!0),t.suffixIcon&&!t.$slots.suffix?(e.openBlock(),e.createBlock(Ne,{key:3,"custom-class":"wd-input__icon",name:t.suffixIcon,onClick:z},null,8,["name"])):e.renderSlot(t.$slots,"suffix",{key:4},void 0,!0)])):e.createCommentVNode("",!0)]),g.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"wd-input__error-message"},e.toDisplayString(g.value),1)):e.createCommentVNode("",!0)])],6))}})),[["__scopeId","data-v-9e96ab80"]]),$t=__spreadProps(__spreadValues({},Ce),{show:we(!1),duration:{type:[Object,Number,Boolean],default:300},lazyRender:we(!1),name:[String,Array],destroy:we(!0),enterClass:xe(""),enterActiveClass:xe(""),enterToClass:xe(""),leaveClass:xe(""),leaveActiveClass:xe(""),leaveToClass:xe("")}),Pt=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-transition",options:{addGlobalClass:!0,virtualHost:!0,styleIsolation:"shared"}}),{props:$t,emits:["click","before-enter","enter","before-leave","leave","after-leave","after-enter"],setup(t,{emit:a}){const n=e=>{let t=`${o.enterClass} ${o.enterActiveClass}`,a=`${o.enterToClass} ${o.enterActiveClass}`,n=`${o.leaveClass} ${o.leaveActiveClass}`,r=`${o.leaveToClass} ${o.leaveActiveClass}`;if(Array.isArray(e))for(let o=0;o<e.length;o++)t=`wd-${e[o]}-enter wd-${e[o]}-enter-active ${t}`,a=`wd-${e[o]}-enter-to wd-${e[o]}-enter-active ${a}`,n=`wd-${e[o]}-leave wd-${e[o]}-leave-active ${n}`,r=`wd-${e[o]}-leave-to wd-${e[o]}-leave-active ${r}`;else e&&(t=`wd-${e}-enter wd-${e}-enter-active ${t}`,a=`wd-${e}-enter-to wd-${e}-enter-active ${a}`,n=`wd-${e}-leave wd-${e}-leave-active ${n}`,r=`wd-${e}-leave-to wd-${e}-leave-active ${r}`);return{enter:t,"enter-to":a,leave:n,"leave-to":r}},o=t,r=a,l=e.ref(!1),s=e.ref(!1),i=e.ref(""),c=e.ref(!1),u=e.ref(300),d=e.ref(""),p=e.ref(null),m=e.ref(null),v=e.ref(null),f=e.computed((()=>`-webkit-transition-duration:${u.value}ms;transition-duration:${u.value}ms;${s.value||!o.destroy?"":"display: none;"}${o.customStyle}`)),h=e.computed((()=>`wd-transition ${o.customClass}  ${d.value}`));function g(){r("click")}function y(){p.value=new U((e=>__async(null,null,(function*(){try{const t=n(o.name),a=X(o.duration)?o.duration.enter:o.duration;i.value="enter",r("before-enter"),m.value=pe(),yield m.value,r("enter"),d.value=t.enter,u.value=a,m.value=pe(),yield m.value,l.value=!0,s.value=!0,m.value=pe(),yield m.value,m.value=null,c.value=!1,d.value=t["enter-to"],e()}catch(t){}}))))}function _(){c.value||(c.value=!0,"leave"===i.value?r("after-leave"):"enter"===i.value&&r("after-enter"),!o.show&&s.value&&(s.value=!1))}return e.onBeforeMount((()=>{o.show&&y()})),e.watch((()=>o.show),(e=>{e?(ce(p.value)&&p.value.abort(),ce(m.value)&&m.value.abort(),ce(v.value)&&v.value.abort(),p.value=null,m.value=null,v.value=null,y()):function(){__async(this,null,(function*(){if(!p.value)return c.value=!1,_();try{if(yield p.value,!s.value)return;const e=n(o.name),t=X(o.duration)?o.duration.leave:o.duration;i.value="leave",r("before-leave"),u.value=t,v.value=pe(),yield v.value,r("leave"),d.value=e.leave,v.value=pe(),yield v.value,c.value=!1,d.value=e["leave-to"],v.value=function(e){return new U((t=>{const a=setTimeout((()=>{clearTimeout(a),t()}),e)}))}(u.value),yield v.value,v.value=null,_(),p.value=null}catch(e){}}))}()}),{deep:!0}),(t,a)=>!t.lazyRender||l.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(h.value),style:e.normalizeStyle(f.value),onTransitionend:_,onClick:g},[e.renderSlot(t.$slots,"default",{},void 0,!0)],38)):e.createCommentVNode("",!0)}})),[["__scopeId","data-v-1fc69211"]]),It=__spreadProps(__spreadValues({},Ce),{show:we(!1),duration:{type:[Object,Number,Boolean],default:300},lockScroll:we(!0),zIndex:be(10)}),Tt=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-overlay",options:{virtualHost:!0,addGlobalClass:!0,styleIsolation:"shared"}}),{props:It,emits:["click"],setup(t,{emit:a}){const n=a;function o(){n("click")}function r(){}return(t,a)=>(e.openBlock(),e.createBlock(Pt,{show:t.show,name:"fade","custom-class":"wd-overlay",duration:t.duration,"custom-style":`z-index: ${t.zIndex}; ${t.customStyle}`,onClick:o,onTouchmove:a[0]||(a[0]=e.withModifiers((e=>t.lockScroll?r:""),["stop","prevent"]))},{default:e.withCtx((()=>[e.renderSlot(t.$slots,"default",{},void 0,!0)])),_:3},8,["show","duration","custom-style"]))}})),[["__scopeId","data-v-3d3ab6e7"]]),At=__spreadProps(__spreadValues({},Ce),{transition:String,closable:we(!1),position:xe("center"),closeOnClickModal:we(!0),duration:{type:[Number,Boolean],default:300},modal:we(!0),zIndex:be(10),hideWhenClose:we(!0),modalStyle:xe(""),safeAreaInsetBottom:we(!1),modelValue:we(!1),lazyRender:we(!0),lockScroll:we(!0)}),Dt=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-popup",options:{virtualHost:!0,addGlobalClass:!0,styleIsolation:"shared"}}),{props:At,emits:["update:modelValue","before-enter","enter","before-leave","leave","after-leave","after-enter","click-modal","close"],setup(t,{emit:a}){const n=t,o=a,r=e.computed((()=>n.transition?n.transition:"center"===n.position?["zoom-in","fade"]:"left"===n.position?"slide-left":"right"===n.position?"slide-right":"bottom"===n.position?"slide-up":"top"===n.position?"slide-down":"slide-up")),l=e.ref(0),s=e.computed((()=>`z-index:${n.zIndex}; padding-bottom: ${l.value}px;${n.customStyle}`)),i=e.computed((()=>`wd-popup wd-popup--${n.position} ${n.transition||"center"!==n.position?"":"is-deep"} ${n.customClass||""}`));function c(){o("click-modal"),n.closeOnClickModal&&u()}function u(){o("close"),o("update:modelValue",!1)}function d(){}return e.onBeforeMount((()=>{if(n.safeAreaInsetBottom){const{safeArea:e,screenHeight:t,safeAreaInsets:a}=uni.getSystemInfoSync();l.value=e&&a?a.bottom:0}})),(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"wd-popup-wrapper"},[t.modal?(e.openBlock(),e.createBlock(Tt,{key:0,show:t.modelValue,"z-index":t.zIndex,"lock-scroll":t.lockScroll,duration:t.duration,"custom-style":t.modalStyle,onClick:c,onTouchmove:d},null,8,["show","z-index","lock-scroll","duration","custom-style"])):e.createCommentVNode("",!0),e.createVNode(Pt,{"lazy-render":t.lazyRender,"custom-class":i.value,"custom-style":s.value,duration:t.duration,show:t.modelValue,name:r.value,destroy:t.hideWhenClose,onBeforeEnter:a[0]||(a[0]=e=>o("before-enter")),onEnter:a[1]||(a[1]=e=>o("enter")),onAfterEnter:a[2]||(a[2]=e=>o("after-enter")),onBeforeLeave:a[3]||(a[3]=e=>o("before-leave")),onLeave:a[4]||(a[4]=e=>o("leave")),onAfterLeave:a[5]||(a[5]=e=>o("after-leave"))},{default:e.withCtx((()=>[e.renderSlot(t.$slots,"default",{},void 0,!0),t.closable?(e.openBlock(),e.createBlock(Ne,{key:0,"custom-class":"wd-popup__close",name:"add",onClick:u})):e.createCommentVNode("",!0)])),_:3},8,["lazy-render","custom-class","custom-style","duration","show","name","destroy"])]))}})),[["__scopeId","data-v-974776cf"]]),Mt=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"FilterPopup"}),{__name:"index",props:{visible:{type:Boolean,default:!1},viewMode:{default:"grid"},filterOptions:{},currentFilters:{},showViewModeSwitch:{type:Boolean,default:!0}},emits:["update:visible","update:viewMode","update:currentFilters","apply","reset"],setup(t,{emit:a}){const n=t,o=a,r=e.ref("60vh"),l=()=>{var e;const t=uni.getSystemInfoSync(),a=t.screenHeight,n=(null==(e=t.safeAreaInsets)?void 0:e.bottom)||0,o=n+80+80+60+20,l=a-o,s=Math.min(l,.65*a);r.value=`${Math.max(s,300)}px`,G("log","at components/filter-popup/index.vue:145","筛选弹窗系统信息:",{screenHeight:a,safeAreaBottom:n,tabBarHeight:80,buttonAreaHeight:80,reservedHeight:o,maxHeight:s,finalHeight:r.value,platform:t.platform})},s=e.ref(__spreadValues({},n.currentFilters));e.watch((()=>n.currentFilters),(e=>{s.value=__spreadValues({},e)}),{deep:!0}),e.watch((()=>n.visible),(e=>{e&&l()}));const i=e=>{o("update:visible",e)},c=()=>{o("update:visible",!1)},u=e=>{o("update:viewMode",e)},d=e=>{s.value.priceMin=e,o("update:currentFilters",__spreadValues({},s.value))},p=e=>{s.value.priceMax=e,o("update:currentFilters",__spreadValues({},s.value))},m=()=>{o("reset")},v=()=>{o("apply",__spreadValues({},s.value))};return l(),(t,a)=>{const n=L(e.resolveDynamicComponent("wd-input"),zt),l=L(e.resolveDynamicComponent("wd-popup"),Dt);return e.openBlock(),e.createBlock(l,{"model-value":t.visible,position:"bottom","safe-area-inset-bottom":!1,overlay:!0,"overlay-style":{backgroundColor:"rgba(0,0,0,0.4)"},"custom-style":`height: ${r.value}; max-height: ${r.value};`,"onUpdate:modelValue":i},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"filter-popup"},[e.createElementVNode("view",{class:"filter-header"},[e.createElementVNode("text",{class:"filter-title"},"全部筛选"),e.createElementVNode("view",{class:"filter-actions"},[t.showViewModeSwitch?(e.openBlock(),e.createElementBlock("view",{key:0,class:"view-mode-switch"},[e.createElementVNode("view",{class:e.normalizeClass(["mode-btn",{active:"list"===t.viewMode}]),onClick:a[0]||(a[0]=e=>u("list"))},[e.createElementVNode("text",{class:"iconfont-sys iconsys-liebiao"})],2),e.createElementVNode("view",{class:e.normalizeClass(["mode-btn",{active:"grid"===t.viewMode}]),onClick:a[1]||(a[1]=e=>u("grid"))},[e.createElementVNode("text",{class:"iconfont-sys iconsys-more-grid-big"})],2)])):e.createCommentVNode("",!0),e.createElementVNode("text",{class:"close-btn",onClick:c},"×")])]),e.createElementVNode("scroll-view",{class:"filter-content","scroll-y":""},[e.createElementVNode("view",{class:"filter-section"},[e.createElementVNode("text",{class:"section-title"},"价格区间"),e.createElementVNode("view",{class:"price-input-container"},[e.createElementVNode("view",{class:"price-input-group"},[e.createVNode(n,{"model-value":s.value.priceMin,type:"number",placeholder:"最低价",size:"large","onUpdate:modelValue":d},null,8,["model-value"]),e.createElementVNode("text",{class:"price-separator"},"-"),e.createVNode(n,{"model-value":s.value.priceMax,type:"number",placeholder:"最高价",size:"large","onUpdate:modelValue":p},null,8,["model-value"])])])]),e.createElementVNode("view",{class:"filter-section"},[e.createElementVNode("text",{class:"section-title"},"距离"),e.createElementVNode("view",{class:"option-card-grid"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.filterOptions.distance,(t=>(e.openBlock(),e.createElementBlock("view",{key:t.value,class:e.normalizeClass(["option-card",{active:s.value.distance===t.value}]),onClick:e=>{return a=t.value,s.value.distance=a,void o("update:currentFilters",__spreadValues({},s.value));var a}},[e.createElementVNode("text",{class:"card-text"},e.toDisplayString(t.label),1)],10,["onClick"])))),128))])]),e.createElementVNode("view",{class:"filter-section"},[e.createElementVNode("text",{class:"section-title"},"新鲜度"),e.createElementVNode("view",{class:"option-card-grid"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.filterOptions.freshness,(t=>(e.openBlock(),e.createElementBlock("view",{key:t.value,class:e.normalizeClass(["option-card",{active:s.value.freshness===t.value}]),onClick:e=>{return a=t.value,s.value.freshness=a,void o("update:currentFilters",__spreadValues({},s.value));var a}},[e.createElementVNode("text",{class:"card-text"},e.toDisplayString(t.label),1)],10,["onClick"])))),128))])]),e.createElementVNode("view",{class:"filter-buttons"},[e.createElementVNode("view",{class:"btn-clear",onClick:m},[e.createElementVNode("text",{class:"btn-text"},"清空选择")]),e.createElementVNode("view",{class:"btn-confirm",onClick:v},[e.createElementVNode("text",{class:"btn-text"},"确定")])]),e.createElementVNode("view",{class:"bottom-safe-area"})])])])),_:1},8,["model-value","overlay-style","custom-style"])}}})),[["__scopeId","data-v-73f2148f"]]),Ot=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"UserIndex"}),{__name:"index",setup(t){const{safeAreaInsetsTop:a,rightSafeArea:n}=yt(),o=e.ref("recommend"),r=[{label:"推荐",value:"recommend"},{label:"蔬菜",value:"vegetables"},{label:"水果",value:"fruits"},{label:"肉类",value:"meat"},{label:"海鲜",value:"seafood"},{label:"粮油",value:"grains"},{label:"零食",value:"snacks"},{label:"饮品",value:"drinks"}],l=e.ref(!1),s=e.ref("grid"),i={distance:[{label:"5km以内",value:"5km"},{label:"10km以内",value:"10km"},{label:"20km以内",value:"20km"},{label:"50km以内",value:"50km"},{label:"不限距离",value:"unlimited"}],freshness:[{label:"不限",value:"all"},{label:"当日新鲜",value:"today"},{label:"3日内",value:"three_days"},{label:"一周内",value:"week"}]},c=e.ref({priceMin:"",priceMax:"",distance:"5km",freshness:"all"});e.ref("comprehensive");const u=e.ref(0),d=e.ref(!1),p=e.ref(!0),m=e.ref(1),v=e.ref("65vh"),f=e.ref([]),h=e.ref([]),g=e.ref(!1),y=e.ref(!0),_=e.ref(1),w=(e,t,a,n=!1)=>__async(null,null,(function*(){return new Promise((o=>{setTimeout((()=>{const n=(t-1)*a,r=((e,t,a)=>{const n=["新鲜有机西红柿 自然成熟 酸甜可口","精选苹果 脆甜多汁 营养丰富","新鲜黄瓜 翠绿爽脆 无农药残留","优质香蕉 香甜软糯 进口品质","有机胡萝卜 营养丰富 口感清甜","新鲜草莓 酸甜可口 维C丰富","精选土豆 粉糯香甜 农家直供","新鲜橙子 汁多味甜 维生素丰富","有机白菜 嫩绿新鲜 无污染种植","优质猪肉 新鲜切割 肉质鲜美","深海带鱼 肉质鲜嫩 营养价值高","精选大米 颗粒饱满 香味浓郁","纯正花生油 压榨工艺 健康营养","休闲零食大礼包 多种口味 老少皆宜","纯净矿泉水 天然水源 口感甘甜"],o=["新鲜果蔬专营店","有机农场直供","绿色蔬菜基地","优质水果商行","农家直销店","生鲜超市","健康食品专营","进口水果店","有机食品专卖","新鲜肉类专营","海鲜水产直销","优质粮油店","休闲零食铺","天然饮品店","绿色食品超市"],r=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFEAA7","#DDA0DD","#98D8C8","#F7DC6F","#BB8FCE","#85C1E9","#F8C471","#82E0AA","#F1948A","#85C1E9","#D7BDE2","#A3E4D7","#F9E79F","#D5A6BD","#AED6F1","#A9DFBF"],l=["https://img.yzcdn.cn/vant/apple-1.jpg","https://img01.yzcdn.cn/vant/cat.jpeg","https://img.yzcdn.cn/vant/apple-2.jpg","https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg","https://img.yzcdn.cn/vant/ipad.jpeg","https://img.yzcdn.cn/vant/sand.jpg","https://fastly.jsdelivr.net/npm/@vant/assets/apple-1.jpeg","https://fastly.jsdelivr.net/npm/@vant/assets/apple-2.jpeg","https://img.yzcdn.cn/vant/leaf.jpg","https://img.yzcdn.cn/vant/tree.jpg"];return Array.from({length:a},((a,s)=>{const i=t+s,c=i%o.length,u=i%l.length,d=`${n[i%n.length]}`,p=[];d.includes("西红柿")||d.includes("黄瓜")||d.includes("胡萝卜")?(p.push("500g装"),p.push("有机认证")):d.includes("苹果")||d.includes("香蕉")||d.includes("橙子")?(p.push("1kg装"),p.push("进口品质")):d.includes("猪肉")||d.includes("带鱼")?(p.push("新鲜切割"),p.push("冷链配送")):d.includes("大米")||d.includes("花生油")?(p.push("5kg装"),p.push("品质保证")):(p.push("精装"),p.push("新鲜直供"));const m=["新鲜采摘，当日配送，保证品质和口感。","严格筛选，品质优良，营养丰富健康。","农场直供，绿色无污染，安全放心。","精心挑选，口感佳，营养价值高。","天然种植，无添加剂，健康美味。"];let v=m[i%m.length];return d.includes("有机")?v+=" 通过有机认证，种植过程无化学农药。":d.includes("进口")?v+=" 进口优质品种，品质保证。":d.includes("新鲜")&&(v+=" 当日采摘配送，保证新鲜度。"),{id:`${e}_${i}`,title:d,images:[l[u]],price:(50*Math.random()+5).toFixed(2),sales:Math.floor(5e3*Math.random()+100),shopName:o[c],merchantLogo:r[(c+5)%r.length],specs:p,description:v}}))})(e,n,a);o({data:r,hasMore:n+a<60,total:60})}),n?300:200)}))})),b=()=>{uni.navigateTo({url:"/pages/user/search"})},k=e=>{const t=e.detail.current;t>=0&&t<f.value.length?(u.value=t,G("log","at pages/user/index.vue:511","商品切换:",t),x()):G("log","at pages/user/index.vue:516","无效索引:",t)},x=()=>{if(d.value||!p.value)return;const e=u.value,t=f.value.length;e<0?u.value=0:e>=t?u.value=t-1:e>=t-5&&p.value&&(G("log","at pages/user/index.vue:542",`预加载触发: 当前索引=${e}, 总数=${t}`),C(o.value,m.value+1,!0))},C=(e,t=1,a=!1)=>__async(null,null,(function*(){if(!d.value){d.value=!0;try{G("log","at pages/user/index.vue:553",`开始加载数据: ${e}, 页码=${t}, 追加=${a}`);const n=yield w(e,t,8,a);setTimeout((()=>{a?(f.value=[...f.value,...n.data],G("log","at pages/user/index.vue:560",`预加载完成，第${t}页，新增${n.data.length}个商品，总数：${f.value.length}`)):(f.value=n.data,u.value=0,G("log","at pages/user/index.vue:568",`初始加载完成，第${t}页，加载${n.data.length}个商品`)),p.value=n.hasMore,m.value=t,d.value=!1}),100)}catch(n){G("error","at pages/user/index.vue:577","加载商品数据失败:",n),d.value=!1}}})),V=e=>__async(null,[e],(function*({name:e}){G("log","at pages/user/index.vue:584","分类切换:",e),"recommend"===e?(u.value=0,m.value=1,p.value=!0,yield C("comprehensive")):yield E(e)})),E=(e,t=1,a=!1,n)=>__async(null,null,(function*(){if(!g.value){g.value=!0;try{yield new Promise((e=>setTimeout(e,500)));const n=((e,t,a)=>{const n=["新鲜有机西红柿 自然成熟","精选苹果 脆甜多汁","新鲜黄瓜 翠绿爽脆","优质香蕉 香甜软糯","有机胡萝卜 营养丰富","新鲜草莓 酸甜可口","精选土豆 粉糯香甜","新鲜橙子 汁多味甜","有机白菜 嫩绿新鲜","优质猪肉 新鲜切割","深海带鱼 肉质鲜嫩","精选大米 颗粒饱满","纯正花生油 压榨工艺","休闲零食 多种口味","纯净矿泉水 天然水源"],o=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFEAA7","#DDA0DD","#98D8C8","#F7DC6F","#BB8FCE","#85C1E9","#F8C471","#82E0AA","#F1948A","#85C1E9","#D7BDE2"];return Array.from({length:a},((a,r)=>{const l=t+r,s=Math.floor(50*Math.random())+5,i=Math.random()>.3?s+Math.floor(20*Math.random())+5:null,c="sales"===e?Math.floor(5e3*Math.random())+1e3:Math.floor(2e3*Math.random())+10,u=Math.floor(100*Math.random())+150;return{id:`${e}_${l}`,title:n[l%n.length],price:s,originalPrice:i,color:o[l%o.length],sales:c,isLiked:Math.random()>.7,imageHeight:u}}))})(e,10*(t-1),10);a?h.value.push(...n):(h.value=n,_.value=1),_.value=t,y.value=h.value.length<50}catch(n){G("error","at pages/user/index.vue:653","加载瀑布流商品数据失败:",n)}finally{g.value=!1}}})),N=e=>{uni.navigateTo({url:`/pages/user/goods-detail?id=${e.id}`})},S=e=>{const t=h.value.findIndex((t=>t.id===e.id));-1!==t&&(h.value[t].isLiked=!h.value[t].isLiked,uni.showToast({title:h.value[t].isLiked?"已收藏":"已取消收藏",icon:"success",duration:1e3}))},B=()=>{!g.value&&y.value&&E(o.value,_.value+1,!0)},z=()=>{l.value=!0},$=()=>{G("log","at pages/user/index.vue:696","应用筛选条件:",c.value),l.value=!1,"recommend"===o.value?C("comprehensive"):E(o.value,1,!1)},P=()=>{c.value={priceMin:"",priceMax:"",distance:"5km",freshness:"all"}},I=e=>{uni.navigateTo({url:`/pages/user/goods-detail?id=${e.id}`})},T=e=>{const t=h.value.findIndex((t=>t.id===e.id));-1!==t&&(h.value[t].isLiked=!h.value[t].isLiked,uni.showToast({title:h.value[t].isLiked?"已收藏":"已取消收藏",icon:"success",duration:1e3}))};return e.onMounted((()=>__async(null,null,(function*(){G("log","at pages/user/index.vue:751","用户首页初始化"),u.value=0,yield C("comprehensive"),setTimeout((()=>{f.value.length>0&&(u.value=0)}),200)})))),(t,p)=>{const m=L(e.resolveDynamicComponent("wd-icon"),Ne),_=L(e.resolveDynamicComponent("wd-tab"),rt),w=L(e.resolveDynamicComponent("wd-tabs"),ht),x=tt,C=e.resolveComponent("layout-default-uni");return e.openBlock(),e.createBlock(C,null,{default:e.withCtx((()=>[e.createElementVNode("view",{class:"home-container"},[e.createElementVNode("view",{class:"header-container",style:e.normalizeStyle({paddingTop:e.unref(a)+"px"})},[e.createElementVNode("view",{class:"page-header",style:e.normalizeStyle({paddingRight:e.unref(n)+"px"})},[e.createElementVNode("view",{class:"location-info"},[e.createVNode(m,{name:"location",size:"16px",color:"#4c4c4c"}),e.createElementVNode("text",{class:"location-text"},"北京市朝阳区 · 三里屯商圈"),e.createVNode(m,{name:"arrow-right",size:"14px",color:"#4c4c4c"})])],4),e.createElementVNode("view",{class:"search-header"},[e.createElementVNode("view",{class:"search-container",onClick:b},[e.createElementVNode("view",{class:"search-input-fake"},[e.createElementVNode("text",{class:"search-placeholder"},"搜索商品、店铺")]),e.createElementVNode("view",{class:"search-button"},[e.createElementVNode("text",{class:"search-text"},"搜索")])]),e.createElementVNode("view",{class:"header-filter-button",onClick:z},[e.createVNode(m,{name:"filter",size:"18px",color:"#666"})])])],4),e.createElementVNode("view",{class:"filter-bar"},[e.createVNode(w,{modelValue:o.value,"onUpdate:modelValue":p[0]||(p[0]=e=>o.value=e),onChange:V,"line-width":"30px","line-height":"3px","auto-line-width":"",swipeable:"",animated:"",slidable:"always"},{default:e.withCtx((()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(r,(t=>e.createVNode(_,{key:t.value,name:t.value,title:t.label},{default:e.withCtx((()=>{return["recommend"===t.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"swiper-container"},[e.createElementVNode("swiper",{class:"goods-swiper",vertical:"",current:u.value,autoplay:!1,"indicator-dots":!1,style:e.normalizeStyle({height:v.value}),onChange:k,circular:!1},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(f.value,((t,a)=>(e.openBlock(),e.createElementBlock("swiper-item",{key:t.id,class:"swiper-item-container"},[e.createElementVNode("view",{class:"goods-card",onClick:e=>((e,t)=>{G("log","at pages/user/index.vue:619","商品点击:",e,t),uni.navigateTo({url:`/pages/user/goods-detail?id=${t.id}`})})(a,t)},[e.createElementVNode("view",{class:"goods-image-wrapper"},[e.createElementVNode("view",{class:"merchant-capsule"},[e.createElementVNode("view",{class:"merchant-logo",style:e.normalizeStyle({backgroundColor:t.merchantLogo})},null,4),e.createElementVNode("text",{class:"merchant-name"},e.toDisplayString(t.shopName),1)]),e.createElementVNode("image",{class:"goods-image",src:t.images[0],mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"goods-info-card"},[e.createElementVNode("view",{class:"goods-title"},e.toDisplayString(t.title),1),e.createElementVNode("view",{class:"goods-sales-info"},[e.createElementVNode("text",{class:"goods-sales"},"销量: "+e.toDisplayString(t.sales),1)])])]),e.createElementVNode("view",{class:"goods-details"},[e.createElementVNode("view",{class:"specs-section"},[e.createElementVNode("text",{class:"section-title"},"这里还未想好放什么，这里是这样浏览"),e.createElementVNode("text",{class:"section-title"},"还是像其他标签页那样的瀑布流或者普通列表"),e.createElementVNode("text",{class:"section-title"},"还需打磨"),e.createElementVNode("text",{class:"section-title"},"可以往下滑动")])])],8,["onClick"])])))),128)),d.value&&0===f.value.length?(e.openBlock(),e.createElementBlock("swiper-item",{key:0,class:"swiper-item-container loading-item"},[e.createElementVNode("view",{class:"loading-container"},[e.createElementVNode("view",{class:"loading-spinner"}),e.createElementVNode("text",{class:"loading-text"},"正在加载商品...")])])):e.createCommentVNode("",!0)],44,["current"])])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"category-container"},[e.createElementVNode("scroll-view",{class:"waterfall-scroll-container","scroll-y":"",onScrolltolower:B,"lower-threshold":200},[e.createElementVNode("view",{class:"waterfall-content"},["grid"===s.value?(e.openBlock(),e.createBlock(Vt,{key:0,"goods-list":h.value,loading:g.value,"has-more":y.value,"column-count":2,"show-price":!1,"loading-text":"加载中...","no-more-text":"没有更多商品了",onGoodsClick:N,onLikeClick:S},null,8,["goods-list","loading","has-more"])):"list"===s.value?(e.openBlock(),e.createBlock(Et,{key:1,"goods-list":(a=h.value,a.map((e=>({id:e.id,title:e.title,price:e.price,originalPrice:e.originalPrice,sales:e.sales,isLiked:e.isLiked,color:e.color})))),loading:g.value,"has-more":y.value,"show-price":!0,"loading-text":"加载中...","no-more-text":"没有更多商品了",onGoodsClick:I,onLikeClick:T},null,8,["goods-list","loading","has-more"])):e.createCommentVNode("",!0)])],32)]))];var a})),_:2},1032,["name","title"]))),64))])),_:1},8,["modelValue"])]),e.createVNode(x,{"current-page":"index"}),e.createVNode(Mt,{visible:l.value,"view-mode":s.value,"filter-options":i,"current-filters":c.value,"show-view-mode-switch":!0,"onUpdate:visible":p[1]||(p[1]=e=>l.value=e),"onUpdate:viewMode":p[2]||(p[2]=e=>s.value=e),"onUpdate:currentFilters":p[3]||(p[3]=e=>c.value=e),onApply:$,onReset:P},null,8,["visible","view-mode","current-filters"])])])),_:1})}}})),[["__scopeId","data-v-13b3f984"]]),Ft=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"Index"}),{__name:"index",setup(t){const{safeAreaInsetsTop:a}=yt(),n=e.computed((()=>({top:a.value})));return(t,a)=>{const o=tt,r=e.resolveComponent("layout-default-uni");return e.openBlock(),e.createBlock(r,null,{default:e.withCtx((()=>{var t;return[e.createElementVNode("view",{class:"home-container",style:e.normalizeStyle({paddingTop:(null==(t=e.unref(n))?void 0:t.top)+"px"})},[e.createElementVNode("view",{class:"content"},[e.createElementVNode("text",null,"首页内容")]),e.createVNode(o,{"current-page":"index"})],4)]})),_:1})}}})),[["__scopeId","data-v-02bfb35b"]]),jt=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"MerchantGoods"}),{__name:"goods",setup(t){const{safeAreaInsets:a}=uni.getSystemInfoSync();return(t,n)=>{const o=tt,r=e.resolveComponent("layout-default-uni");return e.openBlock(),e.createBlock(r,null,{default:e.withCtx((()=>{var t;return[e.createElementVNode("view",{class:"goods-container",style:e.normalizeStyle({paddingTop:(null==(t=e.unref(a))?void 0:t.top)+"px"})},[e.createElementVNode("view",{class:"content"},[e.createElementVNode("text",null,"商户用户 - 商品管理页面内容")]),e.createVNode(o,{"current-page":"goods"})],4)]})),_:1})}}})),[["__scopeId","data-v-d2d104ae"]]),Gt=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"MerchantIndex"}),{__name:"index",setup(t){const{safeAreaInsetsTop:a}=yt(),n=e.computed((()=>({top:a.value})));return e.ref("merchant"),(t,a)=>{const o=tt,r=e.resolveComponent("layout-default-uni");return e.openBlock(),e.createBlock(r,null,{default:e.withCtx((()=>{var t;return[e.createElementVNode("view",{class:"home-container",style:e.normalizeStyle({paddingTop:(null==(t=e.unref(n))?void 0:t.top)+"px"})},[e.createElementVNode("view",{class:"content"},[e.createElementVNode("text",null,"商户用户 - 主页内容")]),e.createVNode(o,{"current-page":"index"})],4)]})),_:1})}}})),[["__scopeId","data-v-a95ac01a"]]),Lt=__spreadProps(__spreadValues({},Ce),{plain:we(!1),round:we(!0),disabled:we(!1),hairline:we(!1),block:we(!1),type:xe("primary"),size:xe("medium"),icon:String,classPrefix:xe("wd-icon"),loading:we(!1),loadingColor:String,openType:String,hoverStopPropagation:Boolean,lang:String,sessionFrom:String,sendMessageTitle:String,sendMessagePath:String,sendMessageImg:String,appParameter:String,showMessageCard:Boolean,buttonId:String,scope:String}),Rt=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-button",options:{addGlobalClass:!0,virtualHost:!0,styleIsolation:"shared"}}),{props:Lt,emits:["click","getuserinfo","contact","getphonenumber","error","launchapp","opensetting","chooseavatar","agreeprivacyauthorization"],setup(t,{emit:a}){const n=t,o=a,r=e.ref(20),l=e.ref(70),s=e.ref(""),i=e.computed((()=>`background-image: url(${s.value});`));function c(e){n.disabled||n.loading||o("click",e)}function u(e){"phoneNumber"===n.scope?m(e):"userInfo"===n.scope&&d(e)}function d(e){o("getuserinfo",e.detail)}function p(e){o("contact",e.detail)}function m(e){o("getphonenumber",e.detail)}function v(e){o("error",e.detail)}function f(e){o("launchapp",e.detail)}function h(e){o("opensetting",e.detail)}function g(e){o("chooseavatar",e.detail)}function y(e){o("agreeprivacyauthorization",e.detail)}return e.watch((()=>n.loading),(()=>{!function(){const{loadingColor:e,type:t,plain:a}=n;let o=e;if(!o)switch(t){case"primary":o="#4D80F0";break;case"success":o="#34d19d";break;case"info":case"default":o="#333";break;case"warning":o="#f0883a";break;case"error":o="#fa4350"}const r=((e="#4D80F0",t=!0)=>`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 42 42"><defs><linearGradient x1="100%" y1="0%" x2="0%" y2="0%" id="a"><stop stop-color="${t?e:"#fff"}" offset="0%" stop-opacity="0"/><stop stop-color="${t?e:"#fff"}" offset="100%"/></linearGradient></defs><g fill="none" fill-rule="evenodd"><path d="M21 1c11.046 0 20 8.954 20 20s-8.954 20-20 20S1 32.046 1 21 9.954 1 21 1zm0 7C13.82 8 8 13.82 8 21s5.82 13 13 13 13-5.82 13-13S28.18 8 21 8z" fill="${t?"#fff":e}"/><path d="M4.599 21c0 9.044 7.332 16.376 16.376 16.376 9.045 0 16.376-7.332 16.376-16.376" stroke="url(#a)" stroke-width="3.5" stroke-linecap="round"/></g></svg>`)(o,!a);s.value=`"data:image/svg+xml;base64,${kt(r)}"`}()}),{deep:!0,immediate:!0}),(t,a)=>(e.openBlock(),e.createElementBlock("button",{id:t.buttonId,"hover-class":""+(t.disabled||t.loading?"":"wd-button--active"),style:e.normalizeStyle(t.customStyle),class:e.normalizeClass(["wd-button","is-"+t.type,"is-"+t.size,t.round?"is-round":"",t.hairline?"is-hairline":"",t.plain?"is-plain":"",t.disabled?"is-disabled":"",t.block?"is-block":"",t.loading?"is-loading":"",t.customClass]),"hover-start-time":r.value,"hover-stay-time":l.value,"open-type":t.disabled||t.loading?void 0:t.openType,"send-message-title":t.sendMessageTitle,"send-message-path":t.sendMessagePath,"send-message-img":t.sendMessageImg,"app-parameter":t.appParameter,"show-message-card":t.showMessageCard,"session-from":t.sessionFrom,lang:t.lang,"hover-stop-propagation":t.hoverStopPropagation,scope:t.scope,onClick:c,"on:getAuthorize":u,onGetuserinfo:d,onContact:p,onGetphonenumber:m,onError:v,onLaunchapp:f,onOpensetting:h,onChooseavatar:g,onAgreeprivacyauthorization:y},[e.createElementVNode("view",{class:"wd-button__content"},[t.loading?(e.openBlock(),e.createElementBlock("view",{key:0,class:"wd-button__loading"},[e.createElementVNode("view",{class:"wd-button__loading-svg",style:e.normalizeStyle(i.value)},null,4)])):t.icon?(e.openBlock(),e.createBlock(Ne,{key:1,"custom-class":"wd-button__icon",name:t.icon,classPrefix:t.classPrefix},null,8,["name","classPrefix"])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"wd-button__text"},[e.renderSlot(t.$slots,"default",{},void 0,!0)])])],46,["id","hover-class","hover-start-time","hover-stay-time","open-type","send-message-title","send-message-path","send-message-img","app-parameter","show-message-card","session-from","lang","hover-stop-propagation","scope"]))}})),[["__scopeId","data-v-7f9ef195"]]),Ht=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"MerchantMine"}),{__name:"mine",setup(t){const a=Ze(),n=e.computed((()=>l(a.getRole))),{safeAreaInsets:o}=uni.getSystemInfoSync(),r=e=>{a.setRole(e),uni.showToast({title:`已切换到${l(e)}`,icon:"success"})},l=e=>({user:"普通用户",merchants:"商户用户",system:"管理员"}[e]);return(t,l)=>{const s=L(e.resolveDynamicComponent("wd-button"),Rt),i=tt,c=e.resolveComponent("layout-default-uni");return e.openBlock(),e.createBlock(c,null,{default:e.withCtx((()=>{var t;return[e.createElementVNode("view",{class:"mine-container",style:e.normalizeStyle({paddingTop:(null==(t=e.unref(o))?void 0:t.top)+"px"})},[e.createElementVNode("view",{class:"content"},[e.createElementVNode("text",null,"商户用户 - 我的页面内容"),e.createElementVNode("view",{class:"role-switch-section"},[e.createElementVNode("text",{class:"section-title"},"角色切换"),e.createElementVNode("view",{class:"button-group"},[e.createVNode(s,{type:"user"===e.unref(a).getRole.valueOf()?"primary":"default",size:"small",onClick:l[0]||(l[0]=t=>r(e.unref(et).user))},{default:e.withCtx((()=>[e.createTextVNode(" 普通用户 ")])),_:1},8,["type"]),e.createVNode(s,{type:"merchants"===e.unref(a).getRole.valueOf()?"primary":"default",size:"small",onClick:l[1]||(l[1]=t=>r(e.unref(et).merchants))},{default:e.withCtx((()=>[e.createTextVNode(" 商户用户 ")])),_:1},8,["type"]),e.createVNode(s,{type:"system"===e.unref(a).getRole.valueOf()?"primary":"default",size:"small",onClick:l[2]||(l[2]=t=>r(e.unref(et).system))},{default:e.withCtx((()=>[e.createTextVNode(" 管理员 ")])),_:1},8,["type"])]),e.createElementVNode("text",{class:"current-role"},"当前角色："+e.toDisplayString(e.unref(n)),1)])]),e.createVNode(i,{"current-page":"mine"})],4)]})),_:1})}}})),[["__scopeId","data-v-7083820e"]]),qt=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"MerchantOrders"}),{__name:"orders",setup(t){const{safeAreaInsets:a}=uni.getSystemInfoSync();return(t,n)=>{const o=tt,r=e.resolveComponent("layout-default-uni");return e.openBlock(),e.createBlock(r,null,{default:e.withCtx((()=>{var t;return[e.createElementVNode("view",{class:"orders-container",style:e.normalizeStyle({paddingTop:(null==(t=e.unref(a))?void 0:t.top)+"px"})},[e.createElementVNode("view",{class:"content"},[e.createElementVNode("text",null,"商户用户 - 订单页面内容")]),e.createVNode(o,{"current-page":"orders"})],4)]})),_:1})}}})),[["__scopeId","data-v-ea2e9fc1"]]),Qt=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"MerchantStatistics"}),{__name:"statistics",setup(t){const{safeAreaInsets:a}=uni.getSystemInfoSync();return(t,n)=>{const o=tt,r=e.resolveComponent("layout-default-uni");return e.openBlock(),e.createBlock(r,null,{default:e.withCtx((()=>{var t;return[e.createElementVNode("view",{class:"statistics-container",style:e.normalizeStyle({paddingTop:(null==(t=e.unref(a))?void 0:t.top)+"px"})},[e.createElementVNode("view",{class:"content"},[e.createElementVNode("text",null,"商户用户 - 统计页面内容")]),e.createVNode(o,{"current-page":"statistics"})],4)]})),_:1})}}})),[["__scopeId","data-v-acb3a9ec"]]),Ut=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"SysDashboard"}),{__name:"dashboard",setup(t){const{safeAreaInsets:a}=uni.getSystemInfoSync();return(t,n)=>{const o=tt,r=e.resolveComponent("layout-default-uni");return e.openBlock(),e.createBlock(r,null,{default:e.withCtx((()=>{var t;return[e.createElementVNode("view",{class:"dashboard-container",style:e.normalizeStyle({paddingTop:(null==(t=e.unref(a))?void 0:t.top)+"px"})},[e.createElementVNode("view",{class:"content"},[e.createElementVNode("text",null,"系统管理员 - 仪表盘页面内容")]),e.createVNode(o,{"current-page":"dashboard"})],4)]})),_:1})}}})),[["__scopeId","data-v-38a7b8c4"]]),Wt=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"SysGoods"}),{__name:"goods",setup(t){const{safeAreaInsets:a}=uni.getSystemInfoSync();return(t,n)=>{const o=tt,r=e.resolveComponent("layout-default-uni");return e.openBlock(),e.createBlock(r,null,{default:e.withCtx((()=>{var t;return[e.createElementVNode("view",{class:"goods-container",style:e.normalizeStyle({paddingTop:(null==(t=e.unref(a))?void 0:t.top)+"px"})},[e.createElementVNode("view",{class:"content"},[e.createElementVNode("text",null,"系统管理员 - 商品管理页面内容")]),e.createVNode(o,{"current-page":"goods"})],4)]})),_:1})}}})),[["__scopeId","data-v-a2f8ef1e"]]),Kt=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"SysMerchants"}),{__name:"merchants",setup(t){const{safeAreaInsets:a}=uni.getSystemInfoSync();return(t,n)=>{const o=tt,r=e.resolveComponent("layout-default-uni");return e.openBlock(),e.createBlock(r,null,{default:e.withCtx((()=>{var t;return[e.createElementVNode("view",{class:"merchants-container",style:e.normalizeStyle({paddingTop:(null==(t=e.unref(a))?void 0:t.top)+"px"})},[e.createElementVNode("view",{class:"content"},[e.createElementVNode("text",null,"系统管理员 - 商户管理页面内容")]),e.createVNode(o,{"current-page":"merchants"})],4)]})),_:1})}}})),[["__scopeId","data-v-5f7683d6"]]),Yt=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"MerchantMine"}),{__name:"mine",setup(t){const a=Ze(),n=e.computed((()=>l(a.getRole))),{safeAreaInsets:o}=uni.getSystemInfoSync(),r=e=>{a.setRole(e),uni.showToast({title:`已切换到${l(e)}`,icon:"success"})},l=e=>({user:"普通用户",merchants:"商户用户",system:"管理员"}[e]);return(t,l)=>{const s=L(e.resolveDynamicComponent("wd-button"),Rt),i=tt,c=e.resolveComponent("layout-default-uni");return e.openBlock(),e.createBlock(c,null,{default:e.withCtx((()=>{var t;return[e.createElementVNode("view",{class:"mine-container",style:e.normalizeStyle({paddingTop:(null==(t=e.unref(o))?void 0:t.top)+"px"})},[e.createElementVNode("view",{class:"content"},[e.createElementVNode("text",null,"系统管理员类用户 - 我的页面内容"),e.createElementVNode("view",{class:"role-switch-section"},[e.createElementVNode("text",{class:"section-title"},"角色切换"),e.createElementVNode("view",{class:"button-group"},[e.createVNode(s,{type:"user"===e.unref(a).getRole.valueOf()?"primary":"default",size:"small",onClick:l[0]||(l[0]=t=>r(e.unref(et).user))},{default:e.withCtx((()=>[e.createTextVNode(" 普通用户 ")])),_:1},8,["type"]),e.createVNode(s,{type:"merchants"===e.unref(a).getRole.valueOf()?"primary":"default",size:"small",onClick:l[1]||(l[1]=t=>r(e.unref(et).merchants))},{default:e.withCtx((()=>[e.createTextVNode(" 商户用户 ")])),_:1},8,["type"]),e.createVNode(s,{type:"system"===e.unref(a).getRole.valueOf()?"primary":"default",size:"small",onClick:l[2]||(l[2]=t=>r(e.unref(et).system))},{default:e.withCtx((()=>[e.createTextVNode(" 管理员 ")])),_:1},8,["type"])]),e.createElementVNode("text",{class:"current-role"},"当前角色："+e.toDisplayString(e.unref(n)),1)])]),e.createVNode(i,{"current-page":"mine"})],4)]})),_:1})}}})),[["__scopeId","data-v-ea881b81"]]),Xt=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"SysSystem"}),{__name:"system",setup(t){const{safeAreaInsets:a}=uni.getSystemInfoSync();return(t,n)=>{const o=tt,r=e.resolveComponent("layout-default-uni");return e.openBlock(),e.createBlock(r,null,{default:e.withCtx((()=>{var t;return[e.createElementVNode("view",{class:"system-container",style:e.normalizeStyle({paddingTop:(null==(t=e.unref(a))?void 0:t.top)+"px"})},[e.createElementVNode("view",{class:"content"},[e.createElementVNode("text",null,"系统管理员 - 系统管理页面内容")]),e.createVNode(o,{"current-page":"system"})],4)]})),_:1})}}})),[["__scopeId","data-v-cca20d14"]]),Jt=__spreadProps(__spreadValues({},Ce),{customInputClass:xe(""),modelValue:xe(""),useSuffixSlot:we(!1),placeholder:String,cancelTxt:String,light:we(!1),hideCancel:we(!1),disabled:we(!1),maxlength:be(-1),placeholderLeft:we(!1),focus:we(!1),focusWhenClear:we(!1),placeholderStyle:String,placeholderClass:xe("")}),Zt=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-search",options:{virtualHost:!0,addGlobalClass:!0,styleIsolation:"shared"}}),{props:Jt,emits:["update:modelValue","change","clear","search","focus","blur","cancel"],setup(t,{emit:a}){const n=t,o=a,{translate:r}=ft("search"),l=e.ref(!1),s=e.ref(!1),i=e.ref(""),c=e.ref(!0),u=e.ref(!1);e.watch((()=>n.modelValue),(e=>{i.value=e,e&&(s.value=!0)}),{immediate:!0}),e.watch((()=>n.focus),(e=>{if(e){if(n.disabled)return;v()}})),e.onMounted((()=>{n.focus&&v()}));const d=e.computed((()=>`wd-search  ${n.light?"is-light":""}  ${n.hideCancel?"is-without-cancel":""} ${n.customClass}`)),p=e.computed((()=>de({display:""===i.value&&c.value?"flex":"none"})));function m(e){return __async(this,null,(function*(){s.value=e,yield pe(),l.value=e}))}function v(){return __async(this,null,(function*(){n.disabled||(yield pe(100),c.value=!1,m(!0))}))}function f(e){i.value=e.detail.value,o("update:modelValue",e.detail.value),o("change",{value:e.detail.value})}function h(){return __async(this,null,(function*(){i.value="",u.value=!0,n.focusWhenClear&&(l.value=!1),yield pe(100),n.focusWhenClear?(c.value=!1,m(!0)):(c.value=!0,m(!1)),o("change",{value:""}),o("update:modelValue",""),o("clear")}))}function g({detail:{value:e}}){o("search",{value:e})}function y(){u.value?u.value=!1:(c.value=!1,o("focus",{value:i.value}))}function _(){u.value||(c.value=!i.value,s.value=!c.value,l.value=!1,o("blur",{value:i.value}))}function w(){o("cancel",{value:i.value})}return(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(d.value),style:e.normalizeStyle(t.customStyle)},[e.createElementVNode("view",{class:"wd-search__block"},[e.renderSlot(t.$slots,"prefix",{},void 0,!0),e.createElementVNode("view",{class:"wd-search__field"},[t.placeholderLeft?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,style:e.normalizeStyle(p.value),class:"wd-search__cover",onClick:v},[e.createVNode(Ne,{name:"search","custom-class":"wd-search__search-icon"}),e.createElementVNode("text",{class:e.normalizeClass(`wd-search__placeholder-txt ${t.placeholderClass}`)},e.toDisplayString(t.placeholder||e.unref(r)("search")),3)],4)),s.value||i.value||t.placeholderLeft?(e.openBlock(),e.createBlock(Ne,{key:1,name:"search","custom-class":"wd-search__search-left-icon"})):e.createCommentVNode("",!0),s.value||i.value||t.placeholderLeft?e.withDirectives((e.openBlock(),e.createElementBlock("input",{key:2,placeholder:t.placeholder||e.unref(r)("search"),"placeholder-class":`wd-search__placeholder-txt ${t.placeholderClass}`,"placeholder-style":t.placeholderStyle,"confirm-type":"search","onUpdate:modelValue":a[0]||(a[0]=e=>i.value=e),class:e.normalizeClass(["wd-search__input",t.customInputClass]),onFocus:y,onInput:f,onBlur:_,onConfirm:g,disabled:t.disabled,maxlength:t.maxlength,focus:l.value},null,42,["placeholder","placeholder-class","placeholder-style","disabled","maxlength","focus"])),[[e.vModelText,i.value]]):e.createCommentVNode("",!0),i.value?(e.openBlock(),e.createBlock(Ne,{key:3,"custom-class":"wd-search__clear wd-search__clear-icon",name:"error-fill",onClick:h})):e.createCommentVNode("",!0)])]),t.hideCancel?e.createCommentVNode("",!0):e.renderSlot(t.$slots,"suffix",{key:0},(()=>[e.createElementVNode("view",{class:"wd-search__cancel",onClick:w},e.toDisplayString(t.cancelTxt||e.unref(r)("cancel")),1)]),!0)],6))}})),[["__scopeId","data-v-6bb79eb7"]]),ea=Symbol("wd-sidebar"),ta=__spreadProps(__spreadValues({},Ce),{modelValue:ke(0),beforeChange:Function}),aa=__spreadProps(__spreadValues({},Ce),{label:_e(String),value:_e([Number,String]),badge:{type:[String,Number,null],default:null},badgeProps:Object,icon:String,isDot:{type:Boolean,default:void 0},max:Number,disabled:we(!1)}),na=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-sidebar-item",options:{addGlobalClass:!0,virtualHost:!0,styleIsolation:"shared"}}),{props:aa,setup(t){const a=t,{parent:n}=ze(ea),o=e.computed((()=>{const e=ve(Z(a.badgeProps)?ge(a.badgeProps,ue):{},ge({max:a.max,isDot:a.isDot,modelValue:a.badge},ue));return Z(e.max)||(e.max=99),e})),r=e.computed((()=>{let e=!1;return n&&n.props.modelValue===a.value&&(e=!0),e})),l=e.computed((()=>{let e=!1;if(n){let t=n.children.findIndex((e=>e.value===n.props.modelValue));n.children.findIndex((e=>e.value===a.value))===t-1&&(e=!0)}return e})),s=e.computed((()=>{let e=!1;if(n){let t=n.children.findIndex((e=>e.value===n.props.modelValue));n.children.findIndex((e=>e.value===a.value))===t+1&&(e=!0)}return e}));function i(){a.disabled||n&&n.setChange(a.value,a.label)}return(t,a)=>(e.openBlock(),e.createElementBlock("view",{onClick:i,class:e.normalizeClass(`wd-sidebar-item ${r.value?"wd-sidebar-item--active":""} ${l.value?"wd-sidebar-item--prefix":""}  ${s.value?"wd-sidebar-item--suffix":""} ${t.disabled?"wd-sidebar-item--disabled":""} ${t.customClass}`),style:e.normalizeStyle(t.customStyle)},[e.renderSlot(t.$slots,"icon",{},void 0,!0),!t.$slots.icon&&t.icon?(e.openBlock(),e.createBlock(Ne,{key:0,"custom-class":"wd-sidebar-item__icon",name:t.icon},null,8,["name"])):e.createCommentVNode("",!0),e.createVNode(Be,e.mergeProps(o.value,{"custom-class":"wd-sidebar-item__badge"}),{default:e.withCtx((()=>[e.createTextVNode(e.toDisplayString(t.label),1)])),_:1},16)],6))}})),[["__scopeId","data-v-7ab7594e"]]),oa=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-sidebar",options:{addGlobalClass:!0,virtualHost:!0,styleIsolation:"shared"}}),{props:ta,emits:["change","update:modelValue"],setup(t,{emit:a}){const n=t,o=a,{linkChildren:r}=Me(ea);function l(e,t){o("update:modelValue",e),o("change",{value:e,label:t})}return r({props:n,setChange:function(e,t){le(n.beforeChange)?n.beforeChange({value:e,resolve:a=>{a&&l(e,t)}}):l(e,t)}}),(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(`wd-sidebar ${t.customClass}`),style:e.normalizeStyle(t.customStyle)},[e.renderSlot(t.$slots,"default",{},void 0,!0),e.createElementVNode("view",{class:"wd-sidebar__padding"})],6))}})),[["__scopeId","data-v-be74041e"]]),ra=__spreadProps(__spreadValues({},Ce),{customImage:xe(""),src:String,previewSrc:String,round:we(!1),mode:xe("scaleToFill"),lazyLoad:we(!1),width:ye,height:ye,radius:ye,enablePreview:we(!1),showMenuByLongpress:we(!1)}),la=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-img",options:{virtualHost:!0,addGlobalClass:!0,styleIsolation:"shared"}}),{props:ra,emits:["error","click","load"],setup(t,{emit:a}){const n=t,o=a,r=e.computed((()=>{const e={};return Z(n.height)&&(e.height=Y(n.height)),Z(n.width)&&(e.width=Y(n.width)),Z(n.radius)&&(e["border-radius"]=Y(n.radius),e.overflow="hidden"),`${de(e)}${n.customStyle}`})),l=e.computed((()=>`wd-img  ${n.round?"is-round":""} ${n.customClass}`)),s=e.ref("loading");function i(e){s.value="error",o("error",e)}function c(e){n.enablePreview&&n.src&&"success"==s.value&&uni.previewImage({urls:[n.previewSrc||n.src]}),o("click",e)}function u(e){s.value="success",o("load",e)}return(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(l.value),onClick:c,style:e.normalizeStyle(r.value)},[e.createElementVNode("image",{class:e.normalizeClass(`wd-img__image ${t.customImage}`),style:e.normalizeStyle("success"!==s.value?"width: 0;height: 0;":""),src:t.src,mode:t.mode,"show-menu-by-longpress":t.showMenuByLongpress,"lazy-load":t.lazyLoad,onLoad:u,onError:i},null,46,["src","mode","show-menu-by-longpress","lazy-load"]),"loading"===s.value?e.renderSlot(t.$slots,"loading",{key:0},void 0,!0):e.createCommentVNode("",!0),"error"===s.value?e.renderSlot(t.$slots,"error",{key:1},void 0,!0):e.createCommentVNode("",!0)],6))}})),[["__scopeId","data-v-aca9eb20"]]),sa=__spreadProps(__spreadValues({},Ce),{image:xe("network"),imageSize:{type:[String,Number,Object],default:""},tip:xe(""),imageMode:xe("aspectFill"),urlPrefix:xe("https://registry.npmmirror.com/wot-design-uni-assets/*/files/")}),ia=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-status-tip",options:{addGlobalClass:!0,virtualHost:!0,styleIsolation:"shared"}}),{props:sa,setup(t){const a=t,n=e.computed((()=>{let e="";return e=["search","network","content","collect","comment","halo","message"].includes(a.image)?`${a.urlPrefix}${a.image}.png`:a.image,e})),o=e.computed((()=>{let e={};return a.imageSize&&(X(a.imageSize)?(Z(a.imageSize.height)&&(e.height=Y(a.imageSize.height)),Z(a.imageSize.width)&&(e.width=Y(a.imageSize.width))):e={height:Y(a.imageSize),width:Y(a.imageSize)}),`${de(e)}`}));return(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(`wd-status-tip  ${t.customClass}`),style:e.normalizeStyle(t.customStyle)},[t.$slots.image?e.renderSlot(t.$slots,"image",{key:0},void 0,!0):n.value?(e.openBlock(),e.createBlock(la,{key:1,mode:t.imageMode,src:n.value,"custom-class":"wd-status-tip__image","custom-style":o.value},null,8,["mode","src","custom-style"])):e.createCommentVNode("",!0),t.tip?(e.openBlock(),e.createElementBlock("view",{key:2,class:"wd-status-tip__text"},e.toDisplayString(t.tip),1)):e.createCommentVNode("",!0)],6))}})),[["__scopeId","data-v-fff4de2e"]]),ca=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"UserCategory"}),{__name:"category",setup(t){const{safeAreaInsetsTop:a,rightSafeArea:n}=yt(),o=e.ref(0),r=e.ref(""),l=e.ref(""),s=e.ref([]),i=e.ref(!1),c=e.ref([{id:1,name:"水果",icon:"🍎",items:[{name:"苹果",image:"https://img.yzcdn.cn/vant/apple-1.jpg"},{name:"香蕉",image:"https://img.yzcdn.cn/vant/apple-2.jpg"},{name:"橙子",image:"https://img.yzcdn.cn/vant/apple-3.jpg"},{name:"葡萄",image:"https://img.yzcdn.cn/vant/apple-4.jpg"}]},{id:2,name:"蔬菜",icon:"🥬",items:[{name:"白菜",image:"https://img.yzcdn.cn/vant/apple-3.jpg"},{name:"萝卜",image:"https://img.yzcdn.cn/vant/apple-4.jpg"}]},{id:3,name:"肉类",icon:"🥩",items:[{name:"猪肉",image:"https://img.yzcdn.cn/vant/apple-1.jpg"},{name:"牛肉",image:"https://img.yzcdn.cn/vant/apple-2.jpg"},{name:"鸡肉",image:"https://img.yzcdn.cn/vant/apple-4.jpg"}]},{id:4,name:"海鲜",icon:"🦐",items:[{name:"虾",image:"https://img.yzcdn.cn/vant/apple-3.jpg"},{name:"蟹",image:"https://img.yzcdn.cn/vant/apple-4.jpg"}]},{id:5,name:"饮品",icon:"🥤",items:[{name:"可乐",image:"https://img.yzcdn.cn/vant/apple-1.jpg"},{name:"果汁",image:"https://img.yzcdn.cn/vant/apple-3.jpg"},{name:"咖啡",image:"https://img.yzcdn.cn/vant/apple-1.jpg"}]},{id:6,name:"零食",icon:"🍿",items:[{name:"薯片",image:"https://img.yzcdn.cn/vant/apple-3.jpg"},{name:"巧克力",image:"https://img.yzcdn.cn/vant/apple-1.jpg"}]},{id:7,name:"调料",icon:"🧂",items:[{name:"盐",image:"https://img.yzcdn.cn/vant/apple-1.jpg"},{name:"糖",image:"https://img.yzcdn.cn/vant/apple-2.jpg"},{name:"酱油",image:"https://img.yzcdn.cn/vant/apple-4.jpg"}]},{id:8,name:"粮油",icon:"🌾",items:[{name:"大米",image:"https://img.yzcdn.cn/vant/apple-3.jpg"},{name:"面粉",image:"https://img.yzcdn.cn/vant/apple-4.jpg"}]},{id:9,name:"乳制品",icon:"🥛",items:[{name:"牛奶",image:"https://img.yzcdn.cn/vant/apple-1.jpg"},{name:"酸奶",image:"https://img.yzcdn.cn/vant/apple-2.jpg"},{name:"奶酪",image:"https://img.yzcdn.cn/vant/apple-3.jpg"}]},{id:10,name:"冷冻食品",icon:"🧊",items:[{name:"冷冻饺子",image:"https://img.yzcdn.cn/vant/apple-3.jpg"},{name:"冰淇淋",image:"https://img.yzcdn.cn/vant/apple-4.jpg"}]},{id:11,name:"面包糕点",icon:"🍞",items:[{name:"吐司",image:"https://img.yzcdn.cn/vant/apple-1.jpg"},{name:"蛋糕",image:"https://img.yzcdn.cn/vant/apple-2.jpg"}]},{id:12,name:"茶叶",icon:"🍵",items:[{name:"绿茶",image:"https://img.yzcdn.cn/vant/apple-3.jpg"},{name:"红茶",image:"https://img.yzcdn.cn/vant/apple-4.jpg"},{name:"乌龙茶",image:"https://img.yzcdn.cn/vant/apple-1.jpg"}]},{id:13,name:"酒类",icon:"🍷",items:[{name:"白酒",image:"https://img.yzcdn.cn/vant/apple-2.jpg"},{name:"红酒",image:"https://img.yzcdn.cn/vant/apple-3.jpg"}]},{id:14,name:"保健品",icon:"💊",items:[{name:"维生素",image:"https://img.yzcdn.cn/vant/apple-4.jpg"},{name:"蛋白粉",image:"https://img.yzcdn.cn/vant/apple-1.jpg"},{name:"钙片",image:"https://img.yzcdn.cn/vant/apple-2.jpg"}]},{id:15,name:"日用品",icon:"🧴",items:[{name:"洗发水",image:"https://img.yzcdn.cn/vant/apple-3.jpg"},{name:"牙膏",image:"https://img.yzcdn.cn/vant/apple-4.jpg"}]},{id:16,name:"清洁用品",icon:"🧽",items:[{name:"洗衣液",image:"https://img.yzcdn.cn/vant/apple-1.jpg"},{name:"洗洁精",image:"https://img.yzcdn.cn/vant/apple-2.jpg"},{name:"消毒液",image:"https://img.yzcdn.cn/vant/apple-3.jpg"}]},{id:17,name:"厨具",icon:"🍳",items:[{name:"平底锅",image:"https://img.yzcdn.cn/vant/apple-4.jpg"},{name:"菜刀",image:"https://img.yzcdn.cn/vant/apple-1.jpg"}]},{id:18,name:"文具",icon:"✏️",items:[{name:"笔记本",image:"https://img.yzcdn.cn/vant/apple-2.jpg"},{name:"圆珠笔",image:"https://img.yzcdn.cn/vant/apple-3.jpg"},{name:"橡皮擦",image:"https://img.yzcdn.cn/vant/apple-4.jpg"}]},{id:19,name:"电子产品",icon:"📱",items:[{name:"充电器",image:"https://img.yzcdn.cn/vant/apple-1.jpg"},{name:"耳机",image:"https://img.yzcdn.cn/vant/apple-2.jpg"}]},{id:20,name:"服装",icon:"👕",items:[{name:"T恤",image:"https://img.yzcdn.cn/vant/apple-3.jpg"},{name:"牛仔裤",image:"https://img.yzcdn.cn/vant/apple-4.jpg"},{name:"运动鞋",image:"https://img.yzcdn.cn/vant/apple-1.jpg"}]},{id:21,name:"玩具",icon:"🧸",items:[{name:"积木",image:"https://img.yzcdn.cn/vant/apple-2.jpg"},{name:"娃娃",image:"https://img.yzcdn.cn/vant/apple-3.jpg"}]},{id:22,name:"运动用品",icon:"⚽",items:[{name:"篮球",image:"https://img.yzcdn.cn/vant/apple-4.jpg"},{name:"跑步机",image:"https://img.yzcdn.cn/vant/apple-1.jpg"},{name:"瑜伽垫",image:"https://img.yzcdn.cn/vant/apple-2.jpg"}]},{id:23,name:"宠物用品",icon:"🐕",items:[{name:"狗粮",image:"https://img.yzcdn.cn/vant/apple-3.jpg"},{name:"猫砂",image:"https://img.yzcdn.cn/vant/apple-4.jpg"}]},{id:24,name:"花卉园艺",icon:"🌸",items:[{name:"玫瑰花",image:"https://img.yzcdn.cn/vant/apple-1.jpg"},{name:"花盆",image:"https://img.yzcdn.cn/vant/apple-2.jpg"},{name:"肥料",image:"https://img.yzcdn.cn/vant/apple-3.jpg"}]},{id:25,name:"汽车用品",icon:"🚗",items:[{name:"机油",image:"https://img.yzcdn.cn/vant/apple-4.jpg"},{name:"车载充电器",image:"https://img.yzcdn.cn/vant/apple-1.jpg"}]}]);function u(){oe(".category",!0).then((e=>{re(e)&&(s.value=e.map((e=>e.top||0)),G("log","at pages/user/category.vue:362","分类位置信息:",s.value))}))}e.onMounted((()=>{setTimeout((()=>{u()}),500)}));let d=!1,p=null;function m(e){const t=e.value;o.value=t,d=!0,r.value=`category-${t}`,setTimeout((()=>{r.value="",setTimeout((()=>{u(),d=!1}),200)}),300)}function v(e){if(d)return;const{scrollTop:t}=e.detail;p&&clearTimeout(p),p=setTimeout((()=>{!function(e){if(0===s.value.length)return;let t=0;const a=150;for(let n=s.value.length-1;n>=0;n--){if(e>=s.value[n]-a){t=n;break}}o.value!==t&&(o.value=t,G("log","at pages/user/category.vue:437","切换到分类:",t,"滚动位置:",e,"分类顶部:",s.value[t]))}(t)}),30)}const f=()=>{G("log","at pages/user/category.vue:450","搜索框聚焦")},h=()=>{const e=""===l.value.trim();i.value=!e},g=t=>{""===(t.value||"").trim()?(i.value=!1,e.nextTick((()=>{setTimeout((()=>{u()}),200)}))):i.value=!0},y=()=>{l.value.trim()?(G("log","at pages/user/category.vue:491","搜索内容:",l.value),i.value=!0,_.value.length>0&&e.nextTick((()=>{setTimeout((()=>{u()}),200)}))):i.value=!1},_=e.computed((()=>{if(!l.value.trim())return[];const e=l.value.toLowerCase().trim();return c.value.map((t=>{const a=t.items.filter((t=>t.name.toLowerCase().includes(e)));return a.length>0?__spreadProps(__spreadValues({},t),{items:a}):t.name.toLowerCase().includes(e)?t:null})).filter(Boolean)})),w=e.computed((()=>i.value?_.value:c.value)),b=()=>{i.value=!1,l.value="",e.nextTick((()=>{setTimeout((()=>{u()}),200)}))};return(t,s)=>{const u=L(e.resolveDynamicComponent("wd-search"),Zt),d=L(e.resolveDynamicComponent("wd-sidebar-item"),na),p=L(e.resolveDynamicComponent("wd-sidebar"),oa),k=L(e.resolveDynamicComponent("wd-status-tip"),ia),x=L(e.resolveDynamicComponent("wd-img"),la),C=tt,V=e.resolveComponent("layout-default-uni");return e.openBlock(),e.createBlock(V,null,{default:e.withCtx((()=>[e.createElementVNode("view",{class:"category-container",style:e.normalizeStyle({paddingTop:e.unref(a)+"px"})},[e.createElementVNode("view",{class:"header",style:e.normalizeStyle({paddingRight:e.unref(n)+"px"})},[e.createVNode(u,{modelValue:e.unref(l),"onUpdate:modelValue":s[0]||(s[0]=t=>e.isRef(l)?l.value=t:null),placeholder:"搜索商品、类目","hide-cancel":"",onFocus:f,onSearch:y,onInput:h,onChange:g,onClear:b,"custom-class":"search-component"},null,8,["modelValue"])],4),e.createElementVNode("view",{class:"main-content"},[e.createElementVNode("view",{class:"sidebar-wrapper"},[e.createVNode(p,{modelValue:e.unref(o),"onUpdate:modelValue":s[1]||(s[1]=t=>e.isRef(o)?o.value=t:null),onChange:m,"custom-class":"sidebar-container"},{default:e.withCtx((()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(c),((t,a)=>(e.openBlock(),e.createBlock(d,{key:a,value:a,label:t.name},null,8,["value","label"])))),128))])),_:1},8,["modelValue"])]),e.createElementVNode("view",{class:"right-content"},[e.unref(i)&&0===e.unref(_).length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-state"},[e.createVNode(k,{image:"search",tip:"当前搜索无结果"})])):(e.openBlock(),e.createElementBlock("scroll-view",{key:1,"scroll-y":"",class:"right-scroll","scroll-into-view":e.unref(r),"scroll-with-animation":!0,onScroll:v},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(w),((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:a,id:`category-${a}`,class:e.normalizeClass(`category category-${a}`)},[e.createElementVNode("view",{class:"category-card"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("text",{class:"card-title"},e.toDisplayString(t.name),1),e.createElementVNode("view",{class:"arrow-icon"},"›")]),e.createElementVNode("view",{class:"items-grid"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.items,((t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"item-card",key:a,onClick:e=>(e=>{G("log","at pages/user/category.vue:506","点击商品:",e.name),uni.navigateTo({url:`/pages/goods/detail?name=${e.name}`})})(t)},[e.createElementVNode("view",{class:"item-image"},[e.createVNode(x,{src:t.image,mode:"aspectFill",width:60,height:60},null,8,["src"])]),e.createElementVNode("text",{class:"item-name"},e.toDisplayString(t.name),1)],8,["onClick"])))),128))])])],10,["id"])))),128))],40,["scroll-into-view"]))])]),e.createVNode(C,{"current-page":"category"})],4)])),_:1})}}})),[["__scopeId","data-v-099d7be2"]]),ua=__spreadProps(__spreadValues({},Ce),{current:be(0),direction:xe("horizontal"),minShowNum:be(2),indicatorPosition:xe("bottom"),showControls:we(!1),total:be(0),type:xe("dots")}),da=Ee(e.defineComponent({__name:"wd-swiper-nav",props:ua,emits:["change"],setup(t,{emit:a}){const n=a;function o(e){n("change",{dir:e,source:"nav"})}return(t,a)=>(e.openBlock(),e.createElementBlock(e.Fragment,null,[t.showControls?(e.openBlock(),e.createElementBlock("view",{key:0,class:"wd-swiper-nav__btn"},[e.createElementVNode("view",{class:"wd-swiper-nav__btn--prev",onClick:a[0]||(a[0]=e=>o("prev"))}),e.createElementVNode("view",{class:"wd-swiper-nav__btn--next",onClick:a[1]||(a[1]=e=>o("next"))})])):e.createCommentVNode("",!0),t.total>=t.minShowNum?(e.openBlock(),e.createElementBlock("view",{key:1,style:e.normalizeStyle(t.customStyle),class:e.normalizeClass(`wd-swiper-nav wd-swiper-nav--${t.direction} wd-swiper-nav--${t.type} wd-swiper-nav--${t.indicatorPosition} ${t.customClass}`)},["dots"===t.type||"dots-bar"===t.type?(e.openBlock(!0),e.createElementBlock(e.Fragment,{key:0},e.renderList(t.total,((a,n)=>(e.openBlock(),e.createElementBlock("view",{key:n,class:e.normalizeClass(`wd-swiper-nav__item--${t.type} ${t.current===n?"is-active":""} is-${t.direction}`)},null,2)))),128)):e.createCommentVNode("",!0),"fraction"===t.type?(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createTextVNode(e.toDisplayString(t.current+1)+"/"+e.toDisplayString(t.total),1)],64)):e.createCommentVNode("",!0)],6)):e.createCommentVNode("",!0)],64))}}),[["__scopeId","data-v-30cb510a"]]),pa=__spreadProps(__spreadValues({},Ce),{autoplay:we(!0),current:be(0),direction:xe("horizontal"),displayMultipleItems:be(1),duration:be(300),easingFunction:xe("default"),height:ke("192"),interval:be(5e3),list:{type:Array,default:()=>[]},loop:we(!0),videoLoop:we(!0),muted:we(!0),nextMargin:ke("0"),indicatorPosition:xe("bottom"),previousMargin:ke("0"),snapToEdge:we(!1),indicator:{type:[Boolean,Object],default:!0},imageMode:xe("aspectFill"),valueKey:xe("value"),textKey:xe("text"),autoplayVideo:we(!0),stopPreviousVideo:we(!0),stopAutoplayWhenVideoPlay:we(!1),adjustHeight:xe("highest"),adjustVerticalHeight:we(!1),customIndicatorClass:xe(""),customImageClass:xe(""),customPrevImageClass:xe(""),customNextImageClass:xe(""),customItemClass:xe(""),customPrevClass:xe(""),customNextClass:xe(""),customTextClass:xe(""),customTextStyle:xe("")}),ma=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-swiper",options:{addGlobalClass:!0,virtualHost:!0,styleIsolation:"shared"}}),{props:pa,emits:["click","change","animationfinish","update:current"],setup(t,{emit:a}){const n=t,o=a,r=e.ref(n.current),l=e.ref(n.current),s=(e,t=!1)=>{l.value=e,t&&(r.value=e),o("update:current",e)},i=e.ref(!1),{proxy:c}=e.getCurrentInstance(),u=e.ref(W());e.watch((()=>n.current),(e=>{e<0?n.loop?f():v():e>=n.list.length?n.loop?v():f():m(e)}));const d=e.computed((()=>{const{list:e,direction:t,indicatorPosition:a,indicator:o}=n,r={current:l.value||0,total:e.length||0,direction:t||"horizontal",indicatorPosition:a||"bottom"};return X(o)&&(r.type=o.type||"dots",r.minShowNum=o.minShowNum||2,r.showControls=o.showControls||!1),r})),p=e=>((e,t)=>{const a=e=>function(e){return/\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|video)/i.test(e)}(e);return X(e)?e.type&&["video","image"].includes(e.type)?e.type===t:a(e[n.valueKey]):a(e)})(e,"video");function m(e){e!==l.value&&s(e,!0)}function v(){m(0)}function f(){m(n.list.length-1)}function h(){n.stopAutoplayWhenVideoPlay&&(i.value=!0)}function g(){i.value=!1}function y(e,t,a){let o="";return function(e,t,a){return(e-1+a.length)%a.length===t}(e,t,a)&&(o=n.customPrevClass||n.customPrevImageClass),function(e,t,a){return(e+1+a.length)%a.length===t}(e,t,a)&&(o=n.customNextClass||n.customNextImageClass),o}function _(e){const{current:t,source:a}=e.detail,r=l.value;if(o("change",{current:t,source:a}),t!==l.value){s(t,"autoplay"===a||"touch"===a)}!function(e,t){(function(e){if(n.stopPreviousVideo){const t=n.list[e];if(Z(t)&&p(t)){uni.createVideoContext(`video-${e}-${u.value}`,c).pause()}}else n.stopAutoplayWhenVideoPlay&&g()})(e),function(e){if(n.autoplayVideo){const t=n.list[e];if(Z(t)&&p(t)){uni.createVideoContext(`video-${e}-${u.value}`,c).play()}}}(t)}(r,t)}function w(e){const{current:t,source:a}=e.detail;if(t!==l.value){s(t,"autoplay"===a||"touch"===a)}o("animationfinish",{current:t,source:a})}function b(e,t){o("click",{index:e,item:t})}function k({dir:e}){const{list:t,loop:a}=n,o=t.length;let r="next"===e?l.value+1:l.value-1;r=a?"next"===e?(l.value+1)%o:(l.value-1+o)%o:r<0||r>=o?l.value:r,r!==l.value&&m(r)}return(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(`wd-swiper ${t.customClass}`),style:e.normalizeStyle(t.customStyle)},[e.createElementVNode("swiper",{"adjust-height":t.adjustHeight,"adjust-vertical-height":t.adjustVerticalHeight,class:"wd-swiper__track",autoplay:t.autoplay&&!i.value,current:r.value,interval:t.interval,duration:t.duration,circular:t.loop,vertical:"vertical"==t.direction,"easing-function":t.easingFunction,"previous-margin":e.unref(Y)(t.previousMargin),"next-margin":e.unref(Y)(t.nextMargin),"snap-to-edge":t.snapToEdge,"display-multiple-items":t.displayMultipleItems,style:e.normalizeStyle({height:e.unref(Y)(t.height)}),onChange:_,onAnimationfinish:w},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.list,((a,n)=>(e.openBlock(),e.createElementBlock("swiper-item",{key:n,class:"wd-swiper__item"},[p(a)?(e.openBlock(),e.createElementBlock("video",{key:0,id:`video-${n}-${u.value}`,style:e.normalizeStyle({height:e.unref(Y)(t.height)}),src:e.unref(X)(a)?a[t.valueKey]:a,poster:e.unref(X)(a)?a.poster:"",class:e.normalizeClass(`wd-swiper__video ${t.customItemClass} ${y(l.value,n,t.list)}`),onPlay:h,onPause:g,"enable-progress-gesture":!1,loop:t.videoLoop,muted:t.muted,autoplay:t.autoplayVideo,objectFit:"cover",onClick:e=>b(n,a)},null,46,["id","src","poster","loop","muted","autoplay","onClick"])):(e.openBlock(),e.createElementBlock("image",{key:1,src:e.unref(X)(a)?a[t.valueKey]:a,class:e.normalizeClass(`wd-swiper__image ${t.customImageClass} ${t.customItemClass} ${y(l.value,n,t.list)}`),style:e.normalizeStyle({height:e.unref(Y)(t.height)}),mode:t.imageMode,onClick:e=>b(n,a)},null,14,["src","mode","onClick"])),e.unref(X)(a)&&a[t.textKey]?(e.openBlock(),e.createElementBlock("text",{key:2,class:e.normalizeClass(`wd-swiper__text ${t.customTextClass}`),style:e.normalizeStyle(t.customTextStyle)},e.toDisplayString(a[t.textKey]),7)):e.createCommentVNode("",!0)])))),128))],44,["adjust-height","adjust-vertical-height","autoplay","current","interval","duration","circular","vertical","easing-function","previous-margin","next-margin","snap-to-edge","display-multiple-items"]),t.indicator?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.renderSlot(t.$slots,"indicator",{current:l.value,total:t.list.length},void 0,!0),t.$slots.indicator?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(da,{key:0,"custom-class":t.customIndicatorClass,type:d.value.type,current:d.value.current,total:d.value.total,direction:d.value.direction,"indicator-position":d.value.indicatorPosition,"min-show-num":d.value.minShowNum,"show-controls":d.value.showControls,onChange:k},null,8,["custom-class","type","current","total","direction","indicator-position","min-show-num","show-controls"]))],64)):e.createCommentVNode("",!0)],6))}})),[["__scopeId","data-v-3e5c0fb1"]]),va=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"GoodsDetail"}),{__name:"goods-detail",setup(t){var a,n,o;const{safeAreaInsetsTop:r,safeAreaInsetsBottom:l,rightSafeArea:s}=yt(),i=e.getCurrentInstance(),c=e.ref((null==(o=null==(n=null==(a=null==i?void 0:i.proxy)?void 0:a.$route)?void 0:n.query)?void 0:o.id)||"1"),u=e.ref({logo:"https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop&crop=center",name:"阳光果园"}),d=e.ref({avatar:"https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop&crop=center",name:"阳光果园旗舰店",followers:2580}),p=e.ref(["https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1619546813926-a78fa6372cd2?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1567306226416-28f0efdc88ce?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1570913149827-d2ac84ab3f9a?w=800&h=600&fit=crop"]),m=e.ref(0),v=e.ref({type:"fraction"}),f=e.ref({title:"价格如果不显示 有点奇怪 新疆阿克苏冰糖心苹果 5斤装 新鲜红富士苹果",price:"29.90",sales:8520,stock:999,monthlySales:1280,description:"新疆阿克苏冰糖心苹果，生长在天山脚下，日照充足，昼夜温差大，果肉脆甜多汁，糖心明显。每一颗都经过精心挑选，个大饱满，口感香甜，营养丰富。现摘现发，新鲜直达。"}),h=e.ref(["https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1619546813926-a78fa6372cd2?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1567306226416-28f0efdc88ce?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1570913149827-d2ac84ab3f9a?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1576179635662-9d1983e97e1e?w=800&h=600&fit=crop"]),g=e.ref(!1),y=e.ref({}),_=e.ref(1),w=e.ref([{name:"重量",options:[{label:"3斤装",value:"3jin"},{label:"5斤装",value:"5jin"},{label:"10斤装",value:"10jin"},{label:"20斤装",value:"20jin"}]},{name:"规格",options:[{label:"精选果",value:"select"},{label:"特级果",value:"premium"},{label:"礼盒装",value:"giftbox"}]}]),b=e.ref(!1),k=e.ref(!1),x=e.ref([{id:"1",name:"新疆香梨 5斤装 清甜多汁",price:"35.80",image:"https://images.unsplash.com/photo-1568702846914-96b305d2aaeb?w=200&h=200&fit=crop"},{id:"2",name:"山东烟台红富士苹果 10斤",price:"45.90",image:"https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=200&h=200&fit=crop"},{id:"3",name:"陕西洛川苹果 脆甜可口",price:"32.50",image:"https://images.unsplash.com/photo-1619546813926-a78fa6372cd2?w=200&h=200&fit=crop"},{id:"4",name:"进口蛇果 新鲜甜脆",price:"68.00",image:"https://images.unsplash.com/photo-1567306226416-28f0efdc88ce?w=200&h=200&fit=crop"}]),C=()=>{uni.navigateBack()},V=e=>{m.value=e.current},E=()=>{uni.previewImage({current:m.value,urls:p.value})},N=()=>{g.value=!0},S=()=>{g.value=!0},B=()=>{_.value<f.value.stock&&_.value++},z=()=>{_.value>1&&_.value--},$=()=>{uni.showToast({title:"跳转至店铺页面",icon:"none"})},P=()=>{uni.showToast({title:"跳转至购物车页面",icon:"none"})},I=()=>{b.value=!b.value,uni.showToast({title:b.value?"已收藏":"已取消收藏",icon:"success"})},T=()=>{k.value=!k.value,uni.showToast({title:k.value?"已关注店铺":"已取消关注",icon:"success"})},A=()=>{uni.showToast({title:"跳转至店铺页面",icon:"none"})},D=()=>{uni.showToast({title:"查看店铺全部商品",icon:"none"})},M=()=>{uni.showToast({title:"分享商品",icon:"none"})};return e.onMounted((()=>{G("log","at pages/user/goods-detail.vue:498","商品ID:",c.value)})),(t,a)=>{const n=L(e.resolveDynamicComponent("wd-icon"),Ne),o=L(e.resolveDynamicComponent("wd-swiper"),ma),i=L(e.resolveDynamicComponent("wd-popup"),Dt),c=e.resolveComponent("layout-default-uni");return e.openBlock(),e.createBlock(c,null,{default:e.withCtx((()=>[e.createElementVNode("view",{class:"goods-detail-container"},[e.createElementVNode("view",{class:"custom-navbar",style:e.normalizeStyle({paddingTop:e.unref(r)+"px"})},[e.createElementVNode("view",{class:"navbar-content",style:e.normalizeStyle({paddingRight:e.unref(s)+"px"})},[e.createElementVNode("view",{class:"navbar-left",onClick:C},[e.createVNode(n,{name:"arrow-left",size:"24px",color:"#333"})]),e.createElementVNode("view",{class:"navbar-center"},[e.createElementVNode("view",{class:"merchant-info"},[e.createElementVNode("image",{class:"merchant-logo",src:u.value.logo,mode:"aspectFill"},null,8,["src"]),e.createElementVNode("text",{class:"merchant-name"},e.toDisplayString(u.value.name),1)])])],4)],4),e.createElementVNode("view",{class:"goods-swiper-container"},[e.createVNode(o,{list:p.value,autoplay:!0,interval:4e3,duration:300,loop:!0,current:m.value,indicator:v.value,"indicator-position":"bottom-right",onClick:E,onChange:V,height:"400px"},null,8,["list","current","indicator"])]),e.createElementVNode("view",{class:"goods-info-card"},[e.createElementVNode("view",{class:"price-section"},[e.createElementVNode("view",{class:"price-main"},[e.createElementVNode("view",{class:"current-price-row"},[e.createElementVNode("text",{class:"price-symbol"},"¥"),e.createElementVNode("text",{class:"current-price"},e.toDisplayString(f.value.price),1)])]),e.createElementVNode("view",{class:"collect-btn",onClick:I},[e.createVNode(n,{name:b.value?"star-on":"star",color:b.value?"#ffca28":"#999",size:"28px"},null,8,["name","color"]),e.createElementVNode("text",{class:e.normalizeClass(["collect-text",{collected:b.value}])},"收藏",2)])]),e.createElementVNode("view",{class:"title-section"},[e.createElementVNode("text",{class:"goods-title"},e.toDisplayString(f.value.title),1)]),e.createElementVNode("view",{class:"sales-info-section"},[e.createElementVNode("view",{class:"info-item"},[e.createVNode(n,{name:"location",size:"14px",color:"#999"}),e.createElementVNode("text",{class:"info-text"},"深圳市")]),e.createElementVNode("view",{class:"info-item"},[e.createElementVNode("text",{class:"info-label"},"销量"),e.createElementVNode("text",{class:"info-value"},e.toDisplayString(f.value.sales)+"件",1)]),e.createElementVNode("view",{class:"info-item"},[e.createElementVNode("text",{class:"info-label"},"库存"),e.createElementVNode("text",{class:"info-value"},e.toDisplayString(f.value.stock)+"件",1)]),e.createElementVNode("view",{class:"info-item"},[e.createElementVNode("text",{class:"info-label"},"月销"),e.createElementVNode("text",{class:"info-value"},e.toDisplayString(f.value.monthlySales)+"件",1)])])]),e.createElementVNode("view",{class:"shop-info-card"},[e.createElementVNode("view",{class:"shop-header"},[e.createElementVNode("view",{class:"shop-basic"},[e.createElementVNode("image",{class:"shop-avatar",src:d.value.avatar,mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"shop-detail"},[e.createElementVNode("text",{class:"shop-name"},e.toDisplayString(d.value.name),1),e.createElementVNode("text",{class:"shop-followers"},e.toDisplayString(d.value.followers)+"人关注",1)])]),e.createElementVNode("view",{class:"shop-actions"},[e.createElementVNode("view",{class:"action-btn follow-btn",onClick:T},[e.createElementVNode("text",{class:"btn-text"},e.toDisplayString(k.value?"已关注":"关注"),1)]),e.createElementVNode("view",{class:"action-btn enter-btn",onClick:A},[e.createElementVNode("text",{class:"btn-text"},"进店逛逛")])])]),e.createElementVNode("view",{class:"shop-recommend-section"},[e.createElementVNode("view",{class:"recommend-header"},[e.createElementVNode("text",{class:"recommend-title"},"店铺推荐"),e.createElementVNode("view",{class:"view-all",onClick:D},[e.createElementVNode("text",{class:"view-all-text"},"查看全部"),e.createVNode(n,{name:"arrow-right",size:"14px",color:"#999"})])]),e.createElementVNode("scroll-view",{class:"recommend-scroll","scroll-x":!0,"show-scrollbar":!1},[e.createElementVNode("view",{class:"recommend-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(x.value,((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:a,class:"recommend-item",onClick:e=>(e=>{uni.showToast({title:`查看商品：${e.name}`,icon:"none"})})(t)},[e.createElementVNode("image",{class:"recommend-image",src:t.image,mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"recommend-info"},[e.createElementVNode("text",{class:"recommend-name"},e.toDisplayString(t.name),1),e.createElementVNode("view",{class:"recommend-price"},[e.createElementVNode("text",{class:"price-symbol"},"¥"),e.createElementVNode("text",{class:"price-value"},e.toDisplayString(t.price),1)])])],8,["onClick"])))),128))])])])]),e.createElementVNode("view",{class:"goods-detail-section"},[e.createElementVNode("view",{class:"section-title"},[e.createElementVNode("text",{class:"title-text"},"商品详情")]),e.createElementVNode("view",{class:"description-content"},[e.createElementVNode("text",{class:"description-text"},e.toDisplayString(f.value.description),1)]),e.createElementVNode("view",{class:"detail-images"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(h.value,((t,a)=>(e.openBlock(),e.createElementBlock("image",{key:a,class:"detail-image",src:t,mode:"widthFix",onClick:e=>(e=>{uni.previewImage({current:e,urls:h.value})})(a)},null,8,["src","onClick"])))),128))])]),e.createElementVNode("view",{class:"bottom-action-bar",style:e.normalizeStyle({paddingBottom:e.unref(l)+"px"})},[e.createElementVNode("view",{class:"action-bar-content"},[e.createElementVNode("view",{class:"icon-group"},[e.createElementVNode("view",{class:"action-icon",onClick:M},[e.createVNode(n,{name:"share",size:"20px",color:"#666"}),e.createElementVNode("text",{class:"icon-text"},"分享")]),e.createElementVNode("view",{class:"action-icon",onClick:$},[e.createVNode(n,{name:"shop",size:"20px",color:"#666"}),e.createElementVNode("text",{class:"icon-text"},"店铺")]),e.createElementVNode("view",{class:"action-icon",onClick:P},[e.createVNode(n,{name:"cart",size:"20px",color:"#666"}),e.createElementVNode("text",{class:"icon-text"},"购物车")])]),e.createElementVNode("view",{class:"button-group"},[e.createElementVNode("view",{class:"button-container"},[e.createElementVNode("view",{class:"cart-btn",onClick:N},[e.createElementVNode("text",{class:"btn-text"},"加入购物车")]),e.createElementVNode("view",{class:"buy-btn",onClick:S},[e.createElementVNode("text",{class:"btn-text"},"立即购买")])])])])],4),e.createVNode(i,{modelValue:g.value,"onUpdate:modelValue":a[0]||(a[0]=e=>g.value=e),position:"bottom",closable:"","custom-style":"border-radius: 20rpx 20rpx 0 0;"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"specs-popup"},[e.createElementVNode("view",{class:"popup-goods-info"},[e.createElementVNode("image",{class:"popup-goods-image",src:p.value[0],mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"popup-goods-detail"},[e.createElementVNode("view",{class:"popup-price"},[e.createElementVNode("text",{class:"popup-price-symbol"},"¥"),e.createElementVNode("text",{class:"popup-price-value"},e.toDisplayString(f.value.price),1)]),e.createElementVNode("text",{class:"popup-stock"},"库存 "+e.toDisplayString(f.value.stock)+" 件",1)])]),e.createElementVNode("view",{class:"specs-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(w.value,((t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"spec-group",key:a},[e.createElementVNode("text",{class:"spec-group-title"},e.toDisplayString(t.name),1),e.createElementVNode("view",{class:"spec-options"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.options,(a=>(e.openBlock(),e.createElementBlock("text",{key:a.value,class:e.normalizeClass(["spec-option",{active:y.value[t.name]===a.value}]),onClick:e=>{return n=t.name,o=a.value,void(y.value[n]=o);var n,o}},e.toDisplayString(a.label),11,["onClick"])))),128))])])))),128))]),e.createElementVNode("view",{class:"quantity-section"},[e.createElementVNode("text",{class:"quantity-title"},"购买数量"),e.createElementVNode("view",{class:"quantity-controls"},[e.createElementVNode("view",{class:e.normalizeClass(["quantity-btn",{disabled:_.value<=1}]),onClick:z},[e.createVNode(n,{name:"minus",size:"14px"})],2),e.createElementVNode("text",{class:"quantity-value"},e.toDisplayString(_.value),1),e.createElementVNode("view",{class:e.normalizeClass(["quantity-btn",{disabled:_.value>=f.value.stock}]),onClick:B},[e.createVNode(n,{name:"add",size:"14px"})],2)])])])])),_:1},8,["modelValue"])])])),_:1})}}})),[["__scopeId","data-v-be1d326f"]]),fa=__spreadProps(__spreadValues({},Ce),{text:{type:[String,Array],default:""},type:xe("warning"),scrollable:we(!0),delay:be(1),speed:be(50),closable:we(!1),wrapable:we(!1),prefix:String,color:String,backgroundColor:String,direction:xe("horizontal")}),ha=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-notice-bar",options:{virtualHost:!0,addGlobalClass:!0,styleIsolation:"shared"}}),{props:fa,emits:["close","next","click"],setup(t,{expose:a,emit:n}){const o=t,r=n,l=e.ref(0),s=e.ref(!0),i=e.ref(0),c=e.computed((()=>Array.isArray(o.text)?o.text:[o.text])),u=e.computed((()=>c.value[i.value])),d=e.ref(0),p=e.ref(null),m=e.ref(null),v=e.computed((()=>"horizontal"===o.direction)),f=e.computed((()=>"vertical"===o.direction)),h=e.reactive({transitionProperty:"unset",transitionDelay:"unset",transitionDuration:"unset",transform:"none",transitionTimingFunction:"linear"}),g=e.computed((()=>de(h))),y=e.computed((()=>{const e={};return Z(o.color)&&(e.color=o.color),Z(o.backgroundColor)&&(e.background=o.backgroundColor),`${de(e)}${o.customStyle}`})),_=e.computed((()=>{const{type:e,wrapable:t,scrollable:a}=o;let n=[];return e&&n.push(`is-${e}`),v.value?!t&&!a&&n.push("wd-notice-bar--ellipse"):n.push("wd-notice-bar--ellipse"),t&&!a&&n.push("wd-notice-bar--wrap"),n.join(" ")})),{proxy:w}=e.getCurrentInstance();function b(){x(),k()}function k(){e.nextTick((()=>function(){return __async(this,null,(function*(){const[e,t]=yield E();e.width&&t.width&&t.height&&(p.value=e,m.value=t,l.value=e.width,v.value?o.scrollable&&V({duration:t.width/o.speed,delay:o.delay,translate:-t.width}):c.value.length>1&&N(t.height))}))}()))}function x(){h.transitionProperty="unset",h.transitionDelay="unset",h.transitionDuration="unset",h.transform="none",h.transitionTimingFunction="linear",i.value=0,d.value=0}function C(){s.value=!1,r("close")}function V({duration:e,delay:t,translate:a}){h.transitionProperty="all",h.transitionDelay=`${t}s`,h.transitionDuration=`${e}s`,h.transform=`${"vertical"===o.direction?"translateY":"translateX"}(${a}px)`,h.transitionTimingFunction="linear"}function E(){return Promise.all([oe(".wd-notice-bar__wrap",!1,w),oe(".wd-notice-bar__content",!1,w)])}function N(e){return __async(this,null,(function*(){const t=-e/(c.value.length+1)*(i.value+1);V({duration:e/(c.value.length+1)/o.speed,delay:o.delay,translate:t})}))}function S(){v.value?V({duration:0,delay:0,translate:l.value+1}):++d.value>=c.value.length&&(d.value=0,V({duration:0,delay:0,translate:0}));const t=setTimeout((()=>{i.value>=c.value.length-1?i.value=0:i.value++,r("next",i.value),e.nextTick((()=>__async(null,null,(function*(){try{const[e,t]=yield E();p.value=e,m.value=t,l.value=e.width||0}catch(e){}m.value&&m.value.width&&m.value.height&&(v.value?V({duration:(l.value+m.value.width)/o.speed,delay:o.delay,translate:-m.value.width}):N(m.value.height))})))),clearTimeout(t)}),20)}function B(){const e=re(o.text)?{index:i.value,text:o.text[i.value]}:{index:0,text:o.text};r("click",e)}return e.watch((()=>o.text),(()=>{b()}),{deep:!0}),e.onMounted((()=>{k();const e=getCurrentPages(),t=e[e.length-1].$getAppWebview();t.addEventListener("hide",(()=>{x()})),t.addEventListener("show",(()=>{k()}))})),e.onActivated((()=>{k()})),e.onDeactivated((()=>{x()})),a({reset:b}),(t,a)=>s.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(`wd-notice-bar ${t.customClass} ${_.value}`),style:e.normalizeStyle(y.value)},[t.prefix?(e.openBlock(),e.createBlock(Ne,{key:0,"custom-class":"wd-notice-bar__prefix",name:t.prefix},null,8,["name"])):e.renderSlot(t.$slots,"prefix",{key:1},void 0,!0),e.createElementVNode("view",{class:"wd-notice-bar__wrap"},[e.createElementVNode("view",{class:"wd-notice-bar__content",style:e.normalizeStyle(g.value),onTransitionend:S,onClick:B},[f.value?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(c.value,(t=>(e.openBlock(),e.createElementBlock("view",{key:t},e.toDisplayString(t),1)))),128)),c.value.length>1?(e.openBlock(),e.createElementBlock("view",{key:0},e.toDisplayString(c.value[0]),1)):e.createCommentVNode("",!0)],64)):e.renderSlot(t.$slots,"default",{key:1},(()=>[e.createTextVNode(e.toDisplayString(u.value),1)]),!0)],36)]),t.closable?(e.openBlock(),e.createBlock(Ne,{key:2,"custom-class":"wd-notice-bar__suffix",name:"close-bold",onClick:C})):e.renderSlot(t.$slots,"suffix",{key:3},void 0,!0)],6)):e.createCommentVNode("",!0)}})),[["__scopeId","data-v-3b72a7ed"]]),ga="app".startsWith("mp-"),ya="app".startsWith("mp-weixin"),_a=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"UserMine"}),{__name:"mine",setup(t){const a=Ze(),{safeAreaInsetsTop:n,rightSafeArea:o}=yt(),r=e.computed((()=>{const e={paddingRight:"20px"};return ga&&o.value>0&&(e.paddingRight=o.value+"px"),e})),l=e.computed((()=>g(a.getRole))),s=e.ref(["测试测试测试测试测试测试测试测试测试","测试测试测试测试测试测试测试测试测试","测试测试测试测试测试测试测试测试测试","客服热线：400-123-4567，为您提供贴心服务！"]),i=e.ref([{id:1,title:"时尚简约连衣裙 春夏新款 舒适透气",price:299,originalPrice:399,color:"#FF6B6B",sales:1234,isLiked:!1,imageHeight:180},{id:2,title:"高品质真皮手提包 商务休闲两用",price:599,originalPrice:799,color:"#4ECDC4",sales:567,isLiked:!0,imageHeight:220},{id:3,title:"运动休闲鞋 透气舒适 多色可选",price:199,originalPrice:null,color:"#45B7D1",sales:890,isLiked:!1,imageHeight:160},{id:4,title:"智能手表 健康监测 长续航",price:899,originalPrice:1299,color:"#96CEB4",sales:345,isLiked:!1,imageHeight:200},{id:5,title:"无线蓝牙耳机 降噪音质好",price:399,originalPrice:599,color:"#FFEAA7",sales:678,isLiked:!0,imageHeight:170},{id:6,title:"护肤套装 补水保湿 温和不刺激",price:259,originalPrice:359,color:"#DDA0DD",sales:1567,isLiked:!1,imageHeight:190}]),c=e.ref(!1),u=e.ref(!0),d=e.ref(1),p=()=>__async(null,null,(function*(){if(c.value||!u.value)return;c.value=!0,yield new Promise((e=>setTimeout(e,10)));const e=((e,t)=>{const a=["时尚简约连衣裙 春夏新款 舒适透气","高品质真皮手提包 商务休闲两用","运动休闲鞋 透气舒适 多色可选","智能手表 健康监测 长续航","无线蓝牙耳机 降噪音质好","护肤套装 补水保湿 温和不刺激","时尚太阳镜 防紫外线 潮流款式","舒适家居服 纯棉材质 亲肤柔软","多功能背包 大容量 商务休闲","精美首饰盒 收纳整理 送礼佳品","健康养生茶 天然草本 清香怡人","智能音响 高音质 语音控制","创意台灯 护眼设计 简约美观","优质床品 四件套 舒适睡眠","厨房用品 不锈钢材质 实用耐用"],n=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFEAA7","#DDA0DD","#98D8C8","#F7DC6F","#BB8FCE","#85C1E9","#F8C471","#82E0AA","#F1948A","#85C1E9","#D7BDE2"],o=[];for(let r=0;r<t;r++){const t=e+r,l=Math.floor(Math.random()*a.length),s=Math.floor(800*Math.random())+50,i=Math.random()>.3?s+Math.floor(200*Math.random())+50:null,c=Math.floor(2e3*Math.random())+10,u=Math.floor(100*Math.random())+150,d=Math.floor(Math.random()*n.length);o.push({id:t,title:a[l],price:s,originalPrice:i,color:n[d],sales:c,isLiked:Math.random()>.7,imageHeight:u})}return o})(i.value.length+1,10);i.value.push(...e),d.value++,i.value.length>=100&&(u.value=!1),c.value=!1})),m=()=>{p()};e.onMounted((()=>{p()}));const v=e=>{uni.navigateTo({url:`/pages/goods/detail?id=${e.id}`})},f=e=>{const t=i.value.findIndex((t=>t.id===e.id));-1!==t&&(i.value[t].isLiked=!i.value[t].isLiked,uni.showToast({title:i.value[t].isLiked?"已收藏":"已取消收藏",icon:"success",duration:1e3}))},h=e=>{a.setRole(e),uni.showToast({title:`已切换到${g(e)}`,icon:"success"})},g=e=>({user:"普通用户",merchants:"商户用户",system:"管理员"}[e]),y=()=>{uni.navigateTo({url:"/pages/user/favorite-shops"})},_=()=>{uni.navigateTo({url:"/pages/user/favorite-goods"})},w=()=>{uni.navigateTo({url:"/pages/user/history"})},b=()=>{uni.navigateTo({url:"/pages/user/feedback"})},k=()=>{uni.navigateTo({url:"/pages/user/customer-service"})},x=()=>{uni.navigateTo({url:"/pages/user/settings"})},C=()=>{uni.navigateTo({url:"/pages/goods/recommend"})},V=()=>{uni.navigateTo({url:"/pages/user/help-center"})};return(t,o)=>{const d=L(e.resolveDynamicComponent("wd-notice-bar"),ha),p=L(e.resolveDynamicComponent("wd-icon"),Ne),g=L(e.resolveDynamicComponent("wd-img"),la),E=L(e.resolveDynamicComponent("wd-badge"),Be),N=L(e.resolveDynamicComponent("wd-button"),Rt),S=tt,B=e.resolveComponent("layout-default-uni");return e.openBlock(),e.createBlock(B,null,{default:e.withCtx((()=>[e.createElementVNode("scroll-view",{class:"mine-container","scroll-y":"",onScrolltolower:m,"lower-threshold":200},[e.createElementVNode("view",{class:"header-background",style:e.normalizeStyle({paddingTop:e.unref(n)+"px"})},[e.createElementVNode("view",{class:"announcement-section",style:e.normalizeStyle(r.value)},[e.createVNode(d,{text:s.value,prefix:"notification",scrollable:!0,delay:2,speed:60,color:"#ffffff","background-color":"rgba(255, 255, 255, 0.2)","custom-class":"announcement-notice"},null,8,["text"])],4),e.createElementVNode("view",{class:"user-card"},[e.createElementVNode("view",{class:"arrow-icon"},[e.createVNode(p,{name:"arrow-right",size:"14px",color:"#999"})]),e.createElementVNode("view",{class:"avatar-container"},[e.createVNode(g,{class:"user-avatar",src:"/static/images/default-avatar.png",mode:"aspectFill",round:"",width:52,height:52})]),e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("view",{class:"user-name-section"},[e.createElementVNode("text",{class:"user-name"},"用户昵称")])]),e.createElementVNode("view",{class:"collection-stats"},[e.createElementVNode("view",{class:"stat-item",onClick:y},[e.createVNode(E,{modelValue:"12",type:"primary"},{default:e.withCtx((()=>[e.createVNode(p,{name:"shop",color:"#1890ff",size:"18px"})])),_:1}),e.createElementVNode("text",{class:"stat-label"},"收藏店铺")]),e.createElementVNode("view",{class:"stat-item",onClick:_},[e.createVNode(E,{modelValue:"28",type:"success"},{default:e.withCtx((()=>[e.createVNode(p,{name:"goods",color:"#52c41a"})])),_:1}),e.createElementVNode("text",{class:"stat-label"},"收藏商品")]),e.createElementVNode("view",{class:"stat-item",onClick:w},[e.createVNode(E,{modelValue:"156",type:"warning"},{default:e.withCtx((()=>[e.createVNode(p,{name:"time",color:"#faad14",size:"18px"})])),_:1}),e.createElementVNode("text",{class:"stat-label"},"历史浏览")])])]),e.createElementVNode("view",{class:"function-grid-container"},[e.createElementVNode("view",{class:"function-grid-item orders-item"},[e.createElementVNode("view",{class:"function-icon-wrapper"},[e.createVNode(p,{name:"list"})]),e.createElementVNode("text",{class:"function-text"},"我的订单")]),e.createElementVNode("view",{class:"function-grid-item feedback-item",onClick:b},[e.createElementVNode("view",{class:"function-icon-wrapper"},[e.createElementVNode("view",{class:"iconfont-sys iconsys-fankui_2",style:{fontSize:"20px",color:"#1890ff"}})]),e.createElementVNode("text",{class:"function-text"},"反馈")]),e.createElementVNode("view",{class:"function-grid-item help-center-item",onClick:V},[e.createElementVNode("view",{class:"function-icon-wrapper"},[e.createVNode(p,{name:"service"})]),e.createElementVNode("text",{class:"function-text"},"帮助中心")]),e.createElementVNode("view",{class:"function-grid-item customer-service-item",onClick:k},[e.createElementVNode("view",{class:"function-icon-wrapper"},[e.createVNode(p,{name:"chat"})]),e.createElementVNode("text",{class:"function-text"},"智能客服")]),e.createElementVNode("view",{class:"function-grid-item settings-item",onClick:x},[e.createElementVNode("view",{class:"function-icon-wrapper"},[e.createVNode(p,{name:"setting"})]),e.createElementVNode("text",{class:"function-text"},"设置")])])],4),e.createElementVNode("view",{class:"recommend-section"},[e.createElementVNode("view",{class:"section-header"},[e.createElementVNode("text",{class:"section-title"},"猜你喜欢"),e.createElementVNode("text",{class:"section-more",onClick:C},"更多")]),e.createVNode(Vt,{"goods-list":i.value,loading:c.value,"has-more":u.value,"column-count":2,"show-price":!1,"loading-text":"加载中...","no-more-text":"没有更多商品了",onGoodsClick:v,onLikeClick:f},null,8,["goods-list","loading","has-more"])]),e.createElementVNode("view",{class:"function-cards"},[e.createElementVNode("view",{class:"role-switch-section"},[e.createElementVNode("text",{class:"section-title"},"开发测试 - 角色切换"),e.createElementVNode("view",{class:"button-group"},[e.createVNode(N,{type:"user"===e.unref(a).getRole.valueOf()?"primary":"default",size:"small",onClick:o[0]||(o[0]=t=>h(e.unref(et).user))},{default:e.withCtx((()=>[e.createTextVNode(" 普通用户 ")])),_:1},8,["type"]),e.createVNode(N,{type:"merchants"===e.unref(a).getRole.valueOf()?"primary":"default",size:"small",onClick:o[1]||(o[1]=t=>h(e.unref(et).merchants))},{default:e.withCtx((()=>[e.createTextVNode(" 商户用户 ")])),_:1},8,["type"]),e.createVNode(N,{type:"system"===e.unref(a).getRole.valueOf()?"primary":"default",size:"small",onClick:o[2]||(o[2]=t=>h(e.unref(et).system))},{default:e.withCtx((()=>[e.createTextVNode(" 管理员 ")])),_:1},8,["type"])]),e.createElementVNode("text",{class:"current-role"},"当前角色："+e.toDisplayString(l.value),1)])]),e.createVNode(S,{"current-page":"mine"})],32)])),_:1})}}})),[["__scopeId","data-v-63e2c3ad"]]);let wa=[];const ba=__spreadProps(__spreadValues({},Ce),{customArrow:xe(""),customPop:xe(""),visibleArrow:we(!0),content:[String,Object],placement:xe("bottom"),offset:be(0),useContentSlot:we(!1),disabled:we(!1),showClose:we(!1),modelValue:we(!1),mode:xe("normal")}),ka=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-popover",options:{virtualHost:!0,addGlobalClass:!0,styleIsolation:"shared"}}),{props:ba,emits:["update:modelValue","menuclick","change","open","close"],setup(t,{expose:a,emit:n}){const o=t,r=n,l=e.inject("__QUEUE_KEY__",null),s="popover",{proxy:i}=e.getCurrentInstance(),c=function(t=!0){const{proxy:a}=e.getCurrentInstance(),n=e.ref(""),o=e.ref(""),r=e.ref(""),l=e.ref(""),s=e.ref(0),i=e.ref(0),c=e.ref(0),u=e.ref(0),d=e.ref(0),p=e.ref(0),m=e.ref(0);return{popStyle:n,arrowStyle:o,showStyle:r,arrowClass:l,init:function(e,t,n){if(t){const t=[`wd-${n}__arrow`,"bottom"===e||"bottom-start"===e||"bottom-end"===e?`wd-${n}__arrow-up`:"","left"===e||"left-start"===e||"left-end"===e?`wd-${n}__arrow-right`:"","right"===e||"right-start"===e||"right-end"===e?`wd-${n}__arrow-left`:"","top"===e||"top-start"===e||"top-end"===e?`wd-${n}__arrow-down`:""];l.value=t.join(" ")}oe("#target",!1,a).then((e=>{e&&(c.value=e.left,u.value=e.bottom,d.value=e.width,p.value=e.height,m.value=e.top)})),oe("#pos",!1,a).then((e=>{e&&(s.value=e.width,i.value=e.height)}))},control:function(e,a){const r=t?9:0,l=d.value/2,c=r+p.value+5,u=d.value+r+5,m=p.value/2;let v=0,f=0;Array.isArray(a)?(v=(l-17>0?0:l-25)+a[0],f=(m-17>0?0:m-25)+(a[1]?a[1]:a[0])):X(a)?(v=(l-17>0?0:l-25)+a.x,f=(m-17>0?0:m-25)+a.y):(v=(l-17>0?0:l-25)+a,f=(m-17>0?0:m-25)+a);const h=new Map([["top",[`left: ${l}px; bottom: ${c}px; transform: translateX(-50%);`,"left: 50%;"]],["top-start",[`left: ${v}px; bottom: ${c}px;`,`left: ${(s.value>=d.value?d.value/2:s.value-25)-v}px;`]],["top-end",[`right: ${v}px; bottom: ${c}px;`,`right: ${(s.value>=d.value?d.value/2:s.value-25)-v}px; transform: translateX(50%);`]],["bottom",[`left: ${l}px; top: ${c}px; transform: translateX(-50%);`,"left: 50%;"]],["bottom-start",[`left: ${v}px; top: ${c}px;`,`left: ${(s.value>=d.value?d.value/2:s.value-25)-v}px;`]],["bottom-end",[`right: ${v}px; top: ${c}px;`,`right: ${(s.value>=d.value?d.value/2:s.value-25)-v}px; transform: translateX(50%);`]],["left",[`right: ${u}px; top: ${m}px; transform: translateY(-50%);`,"top: 50%"]],["left-start",[`right: ${u}px; top: ${f}px;`,`top: ${(i.value>=p.value?p.value/2:i.value-20)-f}px;`]],["left-end",[`right: ${u}px; bottom: ${f}px;`,`bottom: ${(i.value>=p.value?p.value/2:i.value-20)-f}px; transform: translateY(50%);`]],["right",[`left: ${u}px; top: ${m}px; transform: translateY(-50%);`,"top: 50%"]],["right-start",[`left: ${u}px; top: ${f}px;`,`top: ${(i.value>=p.value?p.value/2:i.value-20)-f}px;`]],["right-end",[`left: ${u}px; bottom: ${f}px;`,`bottom: ${(i.value>=p.value?p.value/2:i.value-20)-f}px; transform: translateY(50%);`]]]);n.value=h.get(e)[0],o.value=h.get(e)[1]},noop:function(){}}}(o.visibleArrow),u=e.ref(!1);function d(e){m(!1),r("menuclick",{item:o.content[e],index:e})}function p(){o.disabled||m(!u.value)}function m(e){u.value=e,r("update:modelValue",e)}return e.watch((()=>o.content),(e=>{const{mode:t}=o;"normal"===t&&"string"!=typeof e||"menu"===t&&re(e)})),e.watch((()=>o.placement),(()=>{c.init(o.placement,o.visibleArrow,s)})),e.watch((()=>o.modelValue),(e=>{u.value=e})),e.watch((()=>u.value),(e=>{var t;e&&(c.control(o.placement,o.offset),l&&l.closeOther?l.closeOther(i):(t=i,wa.forEach((e=>{e.$.uid!==t.$.uid&&e.$.exposed.close()})))),c.showStyle.value=e?"display: inline-block;":"display: none;",r("change",{show:e}),r(""+(e?"open":"close"))})),e.onMounted((()=>{c.init(o.placement,o.visibleArrow,s)})),e.onBeforeMount((()=>{var e;l&&l.pushToQueue?l.pushToQueue(i):(e=i,wa.push(e)),c.showStyle.value=u.value?"opacity: 1;":"opacity: 0;"})),e.onBeforeUnmount((()=>{var e;l&&l.removeFromQueue?l.removeFromQueue(i):(e=i,wa=wa.filter((t=>t.$.uid!==e.$.uid)))})),a({open:function(){m(!0)},close:function(){m(!1)}}),(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(`wd-popover ${t.customClass}`),style:e.normalizeStyle(t.customStyle),id:"popover",onClick:a[0]||(a[0]=e.withModifiers(((...t)=>e.unref(c).noop&&e.unref(c).noop(...t)),["stop"]))},[e.createElementVNode("view",{class:"wd-popover__pos wd-popover__hidden",id:"pos"},[e.createElementVNode("view",{class:e.normalizeClass(`wd-popover__container ${t.customPop}`)},[t.useContentSlot||"normal"!==t.mode?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:"wd-popover__inner"},e.toDisplayString(t.content),1)),t.useContentSlot||"menu"!==t.mode||"object"!=typeof t.content?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:1,class:"wd-popover__menu"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.content,((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:a,class:"wd-popover__menu-inner",onClick:e=>d(a)},[t.iconClass?(e.openBlock(),e.createBlock(Ne,{key:0,name:t.iconClass,"custom-class":"wd-popover__icon"},null,8,["name"])):e.createCommentVNode("",!0),e.createElementVNode("text",null,e.toDisplayString(t.content),1)],8,["onClick"])))),128))]))],2)]),e.createVNode(Pt,{"custom-class":"wd-popover__pos","custom-style":e.unref(c).popStyle.value,show:u.value,name:"fade",duration:200},{default:e.withCtx((()=>[e.createElementVNode("view",{class:e.normalizeClass(`wd-popover__container ${t.customPop}`)},[o.visibleArrow?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(`wd-popover__arrow ${e.unref(c).arrowClass.value} ${t.customArrow}`),style:e.normalizeStyle(e.unref(c).arrowStyle.value)},null,6)):e.createCommentVNode("",!0),t.useContentSlot||"normal"!==t.mode?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:1,class:"wd-popover__inner"},e.toDisplayString(t.content),1)),t.useContentSlot||"menu"!==t.mode?e.renderSlot(t.$slots,"content",{key:3},void 0,!0):(e.openBlock(),e.createElementBlock("view",{key:2,class:"wd-popover__menu"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.content,((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:a,class:"wd-popover__menu-inner",onClick:e=>d(a),style:e.normalizeStyle(0===a?"border-top: none":"")},["object"==typeof t&&t.iconClass?(e.openBlock(),e.createBlock(Ne,{key:0,name:t.iconClass,"custom-class":"wd-popover__icon"},null,8,["name"])):e.createCommentVNode("",!0),e.createElementVNode("view",{style:{display:"inline-block"}},e.toDisplayString("object"==typeof t&&t.content?t.content:""),1)],12,["onClick"])))),128))]))],2),t.showClose?(e.openBlock(),e.createBlock(Ne,{key:0,name:"close","custom-class":"wd-popover__close-icon",onClick:p})):e.createCommentVNode("",!0)])),_:3},8,["custom-style","show"]),e.createElementVNode("view",{onClick:p,class:"wd-popover__target",id:"target"},[e.renderSlot(t.$slots,"default",{},void 0,!0)])],6))}})),[["__scopeId","data-v-4f291d86"]]),xa=__spreadProps(__spreadValues({},Ce),{color:String,contentPosition:xe("center"),dashed:Boolean,vertical:we(!1),hairline:we(!0)}),Ca=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-divider",options:{virtualHost:!0,addGlobalClass:!0,styleIsolation:"shared"}}),{props:xa,setup(t){const a=t,n=e.useSlots(),o=e.computed((()=>{const{color:e,customStyle:t}=a,n={};return e&&(n.color=e),`${de(n)}${t}`})),r=e.computed((()=>{const e="wd-divider";return{[e]:!0,"is-dashed":a.dashed,"is-hairline":a.hairline,[`${e}--vertical`]:a.vertical,[`${e}--center`]:!a.vertical&&"center"===a.contentPosition&&!!n.default,[`${e}--left`]:!a.vertical&&"left"===a.contentPosition,[`${e}--right`]:!a.vertical&&"right"===a.contentPosition,[a.customClass]:!!a.customClass}}));return(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(r.value),style:e.normalizeStyle(o.value)},[t.vertical?e.createCommentVNode("",!0):e.renderSlot(t.$slots,"default",{key:0},void 0,!0)],6))}})),[["__scopeId","data-v-386d00c9"]]),Va=Ee(e.defineComponent({__name:"search",setup(t){const{safeAreaInsets:a}=uni.getSystemInfoSync(),n=uni.getSystemInfoSync(),o=["mp-weixin","mp-alipay","mp-baidu","mp-toutiao","mp-qq"].includes(uni.getSystemInfoSync().uniPlatform),r=e.ref({}),l=e.ref(""),s=e.ref(!1),i=e.ref(!1),c=e.ref(),u=e.ref("商品");e.ref(["商品","店铺"]);const d=e.ref([{content:"商品"},{content:"店铺"}]),p=e.computed((()=>{if(o&&r.value.width){return`margin-right: ${n.windowWidth-r.value.left-5}px`}return""})),m=e.ref([]),v=e.ref(["苹果","牛奶","面包","鸡蛋"]),f=e.ref("goods"),h=e.ref([{title:"新鲜苹果",desc:"搜索热度 99.8万",trend:"up"},{title:"有机牛奶",desc:"搜索热度 88.6万",trend:"up"},{title:"进口零食",desc:"搜索热度 76.3万",trend:"down"},{title:"优质大米",desc:"搜索热度 65.2万",trend:"up"},{title:"新鲜蔬菜",desc:"搜索热度 58.9万",trend:"same"},{title:"健康饮品",desc:"搜索热度 52.1万",trend:"down"},{title:"特价商品",desc:"搜索热度 48.7万",trend:"up"},{title:"本地特产",desc:"搜索热度 42.3万",trend:"same"}]),g=e.ref([{title:"新鲜果园旗舰店",desc:"月销量 12.8万",trend:"up"},{title:"优选生鲜超市",desc:"月销量 10.6万",trend:"up"},{title:"有机生活馆",desc:"月销量 9.2万",trend:"down"},{title:"品质食材店",desc:"月销量 8.5万",trend:"up"},{title:"健康美食坊",desc:"月销量 7.8万",trend:"same"},{title:"进口食品专营",desc:"月销量 6.9万",trend:"down"},{title:"本地农产品",desc:"月销量 6.2万",trend:"up"},{title:"精品零食铺",desc:"月销量 5.7万",trend:"same"}]),y=e.computed((()=>"goods"===f.value?h.value:g.value)),_=e.ref([]),w=e.ref("grid"),b=e.ref("comprehensive"),k=e.ref("desc"),x=e.ref(!1),C=e.ref(!0),V=e.ref(1),E=[{label:"综合",value:"comprehensive"},{label:"销量",value:"sales"},{label:"价格",value:"price"},{label:"新上架",value:"newest"}],N=e.ref([]),S=e.computed((()=>N.value.map((e=>({id:e.id,title:e.title,price:e.price,originalPrice:e.originalPrice,sales:e.sales,isLiked:e.isLiked,image:e.image||""}))))),B=["苹果","苹果汁","苹果派","红苹果","青苹果","牛奶","纯牛奶","酸奶","面包","全麦面包","吐司面包"],z=e.ref(!1),$={distance:[{label:"5km以内",value:"5km"},{label:"10km以内",value:"10km"},{label:"20km以内",value:"20km"},{label:"50km以内",value:"50km"},{label:"不限距离",value:"unlimited"}],freshness:[{label:"不限",value:"all"},{label:"当日新鲜",value:"today"},{label:"3日内",value:"three_days"},{label:"一周内",value:"week"}]},P=e.ref({priceMin:"",priceMax:"",distance:"5km",freshness:"all"}),I=()=>{getCurrentPages().length>1?uni.navigateBack():uni.reLaunch({url:"/pages/user/index"})},T=({item:e,index:t})=>{u.value=e.content},A=()=>{i.value=!1,l.value.trim()&&M(l.value)},D=({value:e})=>{l.value=e,e.trim()?(M(e),s.value=!0):s.value=!1,i.value=!1},M=e=>{m.value=B.filter((t=>t.toLowerCase().includes(e.toLowerCase()))).slice(0,5)},O=()=>{l.value.trim()?(j(l.value.trim()),s.value=!1,i.value=!0,H(l.value.trim())):uni.showToast({title:"请输入搜索关键词",icon:"none"})},F=e=>{f.value=e},j=e=>{const t=v.value.indexOf(e);t>-1&&v.value.splice(t,1),v.value.unshift(e),v.value.length>10&&(v.value=v.value.slice(0,10))},R=()=>{uni.showModal({title:"提示",content:"确定要清空搜索历史吗？",success:e=>{e.confirm&&(v.value=[])}})},H=e=>{x.value=!0,V.value=1,setTimeout((()=>{const t=q(e,1,20);_.value=t,N.value=Q(t),x.value=!1,C.value=t.length>=20}),300)},q=(e,t,a)=>{const n=[`新鲜${e} - 优质产品`,`精选${e} - 品质保证`,`有机${e} - 健康之选`,`进口${e} - 高端品质`,`特价${e} - 超值优惠`,`本地${e} - 新鲜直供`,`无公害${e} - 安全可靠`,`绿色${e} - 天然健康`],o=["新鲜果园旗舰店","优选商城","有机生活馆","品质食材店","进口食品专营","本地农产品","健康美食坊","精品零食铺"];return Array.from({length:a},((e,r)=>{const l=(t-1)*a+r;return{id:`search_${l}`,title:n[l%n.length],image:`https://picsum.photos/300/300?random=${l}`,price:(50*Math.random()+10).toFixed(2),originalPrice:Math.random()>.5?(20*Math.random()+60).toFixed(2):null,sales:Math.floor(1e3*Math.random()+50),shopName:o[l%o.length],isLiked:Math.random()>.7}}))},Q=e=>e.map((e=>({id:e.id,title:e.title,price:parseFloat(e.price),originalPrice:e.originalPrice?parseFloat(e.originalPrice):null,sales:e.sales,isLiked:e.isLiked,imageHeight:Math.floor(100*Math.random())+150,image:e.image}))),U=()=>{let e=[..._.value];switch(b.value){case"sales":e.sort(((e,t)=>t.sales-e.sales));break;case"price":"asc"===k.value?e.sort(((e,t)=>parseFloat(e.price)-parseFloat(t.price))):e.sort(((e,t)=>parseFloat(t.price)-parseFloat(e.price)));break;case"newest":e.sort(((e,t)=>{const a=parseInt(e.id.toString().replace("search_",""));return parseInt(t.id.toString().replace("search_",""))-a}));break;default:e.sort(((e,t)=>{const a=.3*e.sales+.2*parseFloat(e.price)+(e.isLiked?100:0);return.3*t.sales+.2*parseFloat(t.price)+(t.isLiked?100:0)-a}))}_.value=e,N.value=Q(e)},W=e=>{G("log","at pages/user/search.vue:655","瀑布流商品点击:",e),uni.navigateTo({url:`/pages/user/goods-detail?id=${e.id}&title=${encodeURIComponent(e.title)}`})},K=e=>{const t=N.value.findIndex((t=>t.id===e.id));if(-1!==t){N.value[t].isLiked=!N.value[t].isLiked;const a=_.value.findIndex((t=>t.id===e.id));-1!==a&&(_.value[a].isLiked=N.value[t].isLiked),uni.showToast({title:N.value[t].isLiked?"已收藏":"已取消收藏",icon:"success",duration:1e3})}},Y=e=>{G("log","at pages/user/search.vue:682","列表商品点击:",e),uni.navigateTo({url:`/pages/user/goods-detail?id=${e.id}&title=${encodeURIComponent(e.title)}`})},X=e=>{const t=N.value.findIndex((t=>t.id===e.id));if(-1!==t){N.value[t].isLiked=!N.value[t].isLiked;const a=_.value.findIndex((t=>t.id===e.id));-1!==a&&(_.value[a].isLiked=N.value[t].isLiked),uni.showToast({title:N.value[t].isLiked?"已收藏":"已取消收藏",icon:"success",duration:1e3})}},J=()=>{!x.value&&C.value&&Z()},Z=()=>{x.value=!0,V.value+=1,setTimeout((()=>{const e=q(l.value,V.value,10);_.value.push(...e),N.value.push(...Q(e)),x.value=!1,C.value=e.length>=10}),500)},ee=()=>{z.value=!0},te=e=>{G("log","at pages/user/search.vue:734","应用筛选条件:",e),z.value=!1,l.value.trim()&&H(l.value.trim())},ae=()=>{P.value={priceMin:"",priceMax:"",distance:"5km",freshness:"all"}};e.onMounted((()=>{}));const ne=()=>{w.value="grid"===w.value?"list":"grid"};return(t,n)=>{const o=L(e.resolveDynamicComponent("wd-icon"),Ne),r=L(e.resolveDynamicComponent("wd-popover"),ka),h=L(e.resolveDynamicComponent("wd-divider"),Ca),g=L(e.resolveDynamicComponent("wd-search"),Zt),V=e.resolveComponent("layout-default-uni");return e.openBlock(),e.createBlock(V,null,{default:e.withCtx((()=>{var t;return[e.createElementVNode("view",{class:"search-container"},[e.createElementVNode("view",{class:"top-background",style:e.normalizeStyle({paddingTop:(null==(t=e.unref(a))?void 0:t.top)+"px"})},[e.createElementVNode("view",{class:"header"},[e.createVNode(o,{name:"arrow-left",size:"25px",color:"#fff",onClick:I}),e.createVNode(g,{modelValue:l.value,"onUpdate:modelValue":n[0]||(n[0]=e=>l.value=e),placeholder:`搜索${u.value}`,"cancel-txt":"搜索",focus:"",focusWhenClear:"","placeholder-left":"",onSearch:O,onFocus:A,onChange:D,onCancel:O,"custom-class":"search-component","custom-style":p.value,ref_key:"searchRef",ref:c},{prefix:e.withCtx((()=>[e.createVNode(r,{mode:"menu",content:d.value,onMenuclick:T},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"search-type"},[e.createElementVNode("text",null,e.toDisplayString(u.value),1),e.createVNode(o,{"custom-class":"icon-arrow",name:"fill-arrow-down"})])])),_:1},8,["content"]),e.createVNode(h,{vertical:"",hairline:!1,"content-position":"left","custom-class":"search-divider"})])),_:1},8,["modelValue","placeholder","custom-style"])])],4),e.createElementVNode("view",{class:"main-content"},[s.value&&m.value.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"suggestions-section"},[e.createElementVNode("view",{class:"suggestions-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(m.value,((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:a,class:"suggestion-item",onClick:e=>(e=>{l.value=e,O()})(t)},[e.createVNode(o,{name:"search",size:"16px",color:"#999"}),e.createElementVNode("text",{class:"suggestion-text"},e.toDisplayString(t),1)],8,["onClick"])))),128))])])):e.createCommentVNode("",!0),i.value||s.value?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:1,class:"search-content"},[v.value.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"history-section"},[e.createElementVNode("view",{class:"section-header"},[e.createElementVNode("text",{class:"section-title"},"搜索历史"),e.createVNode(o,{name:"delete",size:"16px",color:"#999",onClick:R})]),e.createElementVNode("view",{class:"history-tags"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(v.value,((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:a,class:"history-tag",onClick:e=>(e=>{l.value=e,O()})(t)},[e.createElementVNode("text",{class:"tag-text"},e.toDisplayString(t),1)],8,["onClick"])))),128))])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"hot-section"},[e.createElementVNode("view",{class:"hot-tabs"},[e.createElementVNode("view",{class:e.normalizeClass(["hot-tab",{active:"goods"===f.value}]),onClick:n[1]||(n[1]=e=>F("goods"))},[e.createElementVNode("text",{class:"tab-text"},"商品榜")],2),e.createElementVNode("view",{class:e.normalizeClass(["hot-tab",{active:"merchant"===f.value}]),onClick:n[2]||(n[2]=e=>F("merchant"))},[e.createElementVNode("text",{class:"tab-text"},"商户榜")],2)]),e.createElementVNode("view",{class:"hot-ranking"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(y.value,((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:a,class:"ranking-item",onClick:e=>(e=>{l.value=e.title,O()})(t)},[e.createElementVNode("view",{class:"ranking-number"},[e.createElementVNode("text",{class:"number-text"},e.toDisplayString(a+1),1)]),e.createElementVNode("view",{class:"ranking-content"},[e.createElementVNode("text",{class:"ranking-title"},e.toDisplayString(t.title),1),e.createElementVNode("text",{class:"ranking-desc"},e.toDisplayString(t.desc),1)]),e.createElementVNode("view",{class:"ranking-trend"},[e.createVNode(o,{name:"up"===t.trend?"arrow-up":"down"===t.trend?"arrow-down":"minus",color:"up"===t.trend?"#ff4d4f":"down"===t.trend?"#52c41a":"#999",size:"14px"},null,8,["name","color"])])],8,["onClick"])))),128))])])])),i.value?(e.openBlock(),e.createElementBlock("view",{key:2,class:"results-section"},[e.createElementVNode("view",{class:"sort-filter-bar"},[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(E,(t=>e.createElementVNode("view",{key:t.value,class:e.normalizeClass(["sort-item",{active:b.value===t.value}]),onClick:e=>{return"price"===(a=t.value)&&"price"===b.value?k.value="asc"===k.value?"desc":"asc":(b.value=a,"price"===a&&(k.value="desc")),void U();var a}},[e.createElementVNode("text",{class:"sort-text"},e.toDisplayString(t.label),1),"price"===t.value?(e.openBlock(),e.createBlock(o,{key:0,name:"asc"===k.value?"arrow-up":"arrow-down",size:"12px",color:"price"===b.value?"#f8293a":"#999"},null,8,["name","color"])):e.createCommentVNode("",!0)],10,["onClick"]))),64)),e.createElementVNode("view",{class:"right-actions"},[e.createElementVNode("view",{class:"filter-button",onClick:ee},[e.createElementVNode("text",{class:"iconfont-sys iconsys-shaixuan"})]),e.createElementVNode("view",{class:"view-toggle-btn",onClick:ne},[e.createElementVNode("text",{class:e.normalizeClass(["iconfont-sys","grid"===w.value?"iconsys-liebiao":"iconsys-more-grid-big"])},null,2)])])]),e.createElementVNode("scroll-view",{class:"results-scroll-container","scroll-y":"",onScrolltolower:J,"lower-threshold":200},[e.createElementVNode("view",{class:"results-content"},["grid"===w.value?(e.openBlock(),e.createBlock(Vt,{key:0,"goods-list":N.value,loading:x.value,"has-more":C.value,"column-count":2,"show-price":!0,"loading-text":"加载中...","no-more-text":"没有更多商品了",onGoodsClick:W,onLikeClick:K},null,8,["goods-list","loading","has-more"])):"list"===w.value?(e.openBlock(),e.createBlock(Et,{key:1,"goods-list":S.value,loading:x.value,"has-more":C.value,"show-price":!0,"loading-text":"加载中...","no-more-text":"没有更多商品了",onGoodsClick:Y,onLikeClick:X},null,8,["goods-list","loading","has-more"])):e.createCommentVNode("",!0)])],32),0!==_.value.length||x.value?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-state"},[e.createVNode(o,{name:"search",size:"60px",color:"#ccc"}),e.createElementVNode("text",{class:"empty-text"},"没有找到相关商品"),e.createElementVNode("text",{class:"empty-tip"},"试试其他关键词吧")]))])):e.createCommentVNode("",!0)]),e.createVNode(Mt,{visible:z.value,"view-mode":w.value,"filter-options":$,"current-filters":P.value,"show-view-mode-switch":!1,"onUpdate:visible":n[3]||(n[3]=e=>z.value=e),"onUpdate:viewMode":n[4]||(n[4]=e=>w.value=e),"onUpdate:currentFilters":n[5]||(n[5]=e=>P.value=e),onApply:te,onReset:ae},null,8,["visible","view-mode","current-filters"])])]})),_:1})}}}),[["__scopeId","data-v-5b662c9e"]]);__definePage("pages/user/index",Ot),__definePage("pages/index/index",Ft),__definePage("pages/merchants/goods",jt),__definePage("pages/merchants/index",Gt),__definePage("pages/merchants/mine",Ht),__definePage("pages/merchants/orders",qt),__definePage("pages/merchants/statistics",Qt),__definePage("pages/sys/dashboard",Ut),__definePage("pages/sys/goods",Wt),__definePage("pages/sys/merchants",Kt),__definePage("pages/sys/mine",Yt),__definePage("pages/sys/system",Xt),__definePage("pages/user/category",ca),__definePage("pages/user/goods-detail",va),__definePage("pages/user/mine",_a),__definePage("pages/user/search",Va);var Ea=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Na="undefined"==typeof window||"Deno"in globalThis;function Sa(){}function Ba(e,t){return"function"==typeof e?e(t):e}function za(e,t){const{type:a="all",exact:n,fetchStatus:o,predicate:r,queryKey:l,stale:s}=e;if(l)if(n){if(t.queryHash!==Pa(l,t.options))return!1}else if(!Ta(t.queryKey,l))return!1;if("all"!==a){const e=t.isActive();if("active"===a&&!e)return!1;if("inactive"===a&&e)return!1}return("boolean"!=typeof s||t.isStale()===s)&&((!o||o===t.state.fetchStatus)&&!(r&&!r(t)))}function $a(e,t){const{exact:a,status:n,predicate:o,mutationKey:r}=e;if(r){if(!t.options.mutationKey)return!1;if(a){if(Ia(t.options.mutationKey)!==Ia(r))return!1}else if(!Ta(t.options.mutationKey,r))return!1}return(!n||t.state.status===n)&&!(o&&!o(t))}function Pa(e,t){return((null==t?void 0:t.queryKeyHashFn)||Ia)(e)}function Ia(e){return JSON.stringify(e,((e,t)=>Ma(t)?Object.keys(t).sort().reduce(((e,a)=>(e[a]=t[a],e)),{}):t))}function Ta(e,t){return e===t||typeof e==typeof t&&(!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&!Object.keys(t).some((a=>!Ta(e[a],t[a]))))}function Aa(e,t){if(e===t)return e;const a=Da(e)&&Da(t);if(a||Ma(e)&&Ma(t)){const n=a?e:Object.keys(e),o=n.length,r=a?t:Object.keys(t),l=r.length,s=a?[]:{};let i=0;for(let c=0;c<l;c++){const o=a?c:r[c];(!a&&n.includes(o)||a)&&void 0===e[o]&&void 0===t[o]?(s[o]=void 0,i++):(s[o]=Aa(e[o],t[o]),s[o]===e[o]&&void 0!==e[o]&&i++)}return o===l&&i===o?e:s}return t}function Da(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Ma(e){if(!Oa(e))return!1;const t=e.constructor;if(void 0===t)return!0;const a=t.prototype;return!!Oa(a)&&(!!a.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype)}function Oa(e){return"[object Object]"===Object.prototype.toString.call(e)}function Fa(e,t,a){return"function"==typeof a.structuralSharing?a.structuralSharing(e,t):!1!==a.structuralSharing?Aa(e,t):t}function ja(e,t,a=0){const n=[...e,t];return a&&n.length>a?n.slice(1):n}function Ga(e,t,a=0){const n=[t,...e];return a&&n.length>a?n.slice(0,-1):n}var La=Symbol();function Ra(e,t){return!e.queryFn&&(null==t?void 0:t.initialPromise)?()=>t.initialPromise:e.queryFn&&e.queryFn!==La?e.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`))}var Ha=new(o=class extends Ea{constructor(){super(),__privateAdd(this,t),__privateAdd(this,a),__privateAdd(this,n),__privateSet(this,n,(e=>{if(!Na&&window.addEventListener){const t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}))}onSubscribe(){__privateGet(this,a)||this.setEventListener(__privateGet(this,n))}onUnsubscribe(){var e;this.hasListeners()||(null==(e=__privateGet(this,a))||e.call(this),__privateSet(this,a,void 0))}setEventListener(e){var t;__privateSet(this,n,e),null==(t=__privateGet(this,a))||t.call(this),__privateSet(this,a,e((e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})))}setFocused(e){__privateGet(this,t)!==e&&(__privateSet(this,t,e),this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach((t=>{t(e)}))}isFocused(){var e;return"boolean"==typeof __privateGet(this,t)?__privateGet(this,t):"hidden"!==(null==(e=globalThis.document)?void 0:e.visibilityState)}},t=new WeakMap,a=new WeakMap,n=new WeakMap,o),qa=new(i=class extends Ea{constructor(){super(),__privateAdd(this,r,!0),__privateAdd(this,l),__privateAdd(this,s),__privateSet(this,s,(e=>{if(!Na&&window.addEventListener){const t=()=>e(!0),a=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",a,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",a)}}}))}onSubscribe(){__privateGet(this,l)||this.setEventListener(__privateGet(this,s))}onUnsubscribe(){var e;this.hasListeners()||(null==(e=__privateGet(this,l))||e.call(this),__privateSet(this,l,void 0))}setEventListener(e){var t;__privateSet(this,s,e),null==(t=__privateGet(this,l))||t.call(this),__privateSet(this,l,e(this.setOnline.bind(this)))}setOnline(e){__privateGet(this,r)!==e&&(__privateSet(this,r,e),this.listeners.forEach((t=>{t(e)})))}isOnline(){return __privateGet(this,r)}},r=new WeakMap,l=new WeakMap,s=new WeakMap,i);function Qa(e){return Math.min(1e3*__pow(2,e),3e4)}function Ua(e){return"online"!==(null!=e?e:"online")||qa.isOnline()}var Wa=class extends Error{constructor(e){super("CancelledError"),this.revert=null==e?void 0:e.revert,this.silent=null==e?void 0:e.silent}};function Ka(e){return e instanceof Wa}function Ya(e){let t,a=!1,n=0,o=!1;const r=function(){let e,t;const a=new Promise(((a,n)=>{e=a,t=n}));function n(e){Object.assign(a,e),delete a.resolve,delete a.reject}return a.status="pending",a.catch((()=>{})),a.resolve=t=>{n({status:"fulfilled",value:t}),e(t)},a.reject=e=>{n({status:"rejected",reason:e}),t(e)},a}(),l=()=>Ha.isFocused()&&("always"===e.networkMode||qa.isOnline())&&e.canRun(),s=()=>Ua(e.networkMode)&&e.canRun(),i=a=>{var n;o||(o=!0,null==(n=e.onSuccess)||n.call(e,a),null==t||t(),r.resolve(a))},c=a=>{var n;o||(o=!0,null==(n=e.onError)||n.call(e,a),null==t||t(),r.reject(a))},u=()=>new Promise((a=>{var n;t=e=>{(o||l())&&a(e)},null==(n=e.onPause)||n.call(e)})).then((()=>{var a;t=void 0,o||null==(a=e.onContinue)||a.call(e)})),d=()=>{if(o)return;let t;const r=0===n?e.initialPromise:void 0;try{t=null!=r?r:e.fn()}catch(s){t=Promise.reject(s)}Promise.resolve(t).then(i).catch((t=>{var r,s,i;if(o)return;const p=null!=(r=e.retry)?r:Na?0:3,m=null!=(s=e.retryDelay)?s:Qa,v="function"==typeof m?m(n,t):m,f=!0===p||"number"==typeof p&&n<p||"function"==typeof p&&p(n,t);var h;!a&&f?(n++,null==(i=e.onFail)||i.call(e,n,t),(h=v,new Promise((e=>{setTimeout(e,h)}))).then((()=>l()?void 0:u())).then((()=>{a?c(t):d()}))):c(t)}))};return{promise:r,cancel:t=>{var a;o||(c(new Wa(t)),null==(a=e.abort)||a.call(e))},continue:()=>(null==t||t(),r),cancelRetry:()=>{a=!0},continueRetry:()=>{a=!1},canStart:s,start:()=>(s()?d():u().then(d),r)}}var Xa=function(){let e=[],t=0,a=e=>{e()},n=e=>{e()},o=e=>setTimeout(e,0);const r=n=>{t?e.push(n):o((()=>{a(n)}))};return{batch:r=>{let l;t++;try{l=r()}finally{t--,t||(()=>{const t=e;e=[],t.length&&o((()=>{n((()=>{t.forEach((e=>{a(e)}))}))}))})()}return l},batchCalls:e=>(...t)=>{r((()=>{e(...t)}))},schedule:r,setNotifyFunction:e=>{a=e},setBatchNotifyFunction:e=>{n=e},setScheduler:e=>{o=e}}}(),Ja=(u=class{constructor(){__privateAdd(this,c)}destroy(){this.clearGcTimeout()}scheduleGc(){var e;this.clearGcTimeout(),"number"==typeof(e=this.gcTime)&&e>=0&&e!==1/0&&__privateSet(this,c,setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,null!=e?e:Na?1/0:3e5)}clearGcTimeout(){__privateGet(this,c)&&(clearTimeout(__privateGet(this,c)),__privateSet(this,c,void 0))}},c=new WeakMap,u),Za=(_=class extends Ja{constructor(e){var t;super(),__privateAdd(this,g),__privateAdd(this,d),__privateAdd(this,p),__privateAdd(this,m),__privateAdd(this,v),__privateAdd(this,f),__privateAdd(this,h),__privateSet(this,h,!1),__privateSet(this,f,e.defaultOptions),this.setOptions(e.options),this.observers=[],__privateSet(this,m,e.cache),this.queryKey=e.queryKey,this.queryHash=e.queryHash,__privateSet(this,d,function(e){const t="function"==typeof e.initialData?e.initialData():e.initialData,a=void 0!==t,n=a?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:a?null!=n?n:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:a?"success":"pending",fetchStatus:"idle"}}(this.options)),this.state=null!=(t=e.state)?t:__privateGet(this,d),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var e;return null==(e=__privateGet(this,v))?void 0:e.promise}setOptions(e){this.options=__spreadValues(__spreadValues({},__privateGet(this,f)),e),this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||__privateGet(this,m).remove(this)}setData(e,t){const a=Fa(this.state.data,e,this.options);return __privateMethod(this,g,y).call(this,{data:a,type:"success",dataUpdatedAt:null==t?void 0:t.updatedAt,manual:null==t?void 0:t.manual}),a}setState(e,t){__privateMethod(this,g,y).call(this,{type:"setState",state:e,setStateOptions:t})}cancel(e){var t,a;const n=null==(t=__privateGet(this,v))?void 0:t.promise;return null==(a=__privateGet(this,v))||a.cancel(e),n?n.then(Sa).catch(Sa):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(__privateGet(this,d))}isActive(){return this.observers.some((e=>{return!1!==(t=e.options.enabled,a=this,"function"==typeof t?t(a):t);var t,a}))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===La||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((e=>e.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!function(e,t){return Math.max(e+(t||0)-Date.now(),0)}(this.state.dataUpdatedAt,e)}onFocus(){var e;const t=this.observers.find((e=>e.shouldFetchOnWindowFocus()));null==t||t.refetch({cancelRefetch:!1}),null==(e=__privateGet(this,v))||e.continue()}onOnline(){var e;const t=this.observers.find((e=>e.shouldFetchOnReconnect()));null==t||t.refetch({cancelRefetch:!1}),null==(e=__privateGet(this,v))||e.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),__privateGet(this,m).notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter((t=>t!==e)),this.observers.length||(__privateGet(this,v)&&(__privateGet(this,h)?__privateGet(this,v).cancel({revert:!0}):__privateGet(this,v).cancelRetry()),this.scheduleGc()),__privateGet(this,m).notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||__privateMethod(this,g,y).call(this,{type:"invalidate"})}fetch(e,t){var a,n,o;if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&(null==t?void 0:t.cancelRefetch))this.cancel({silent:!0});else if(__privateGet(this,v))return __privateGet(this,v).continueRetry(),__privateGet(this,v).promise;if(e&&this.setOptions(e),!this.options.queryFn){const e=this.observers.find((e=>e.options.queryFn));e&&this.setOptions(e.options)}const r=new AbortController,l=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(__privateSet(this,h,!0),r.signal)})},s={fetchOptions:t,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>{const e=Ra(this.options,t),a={queryKey:this.queryKey,meta:this.meta};return l(a),__privateSet(this,h,!1),this.options.persister?this.options.persister(e,a,this):e(a)}};l(s),null==(a=this.options.behavior)||a.onFetch(s,this),__privateSet(this,p,this.state),"idle"!==this.state.fetchStatus&&this.state.fetchMeta===(null==(n=s.fetchOptions)?void 0:n.meta)||__privateMethod(this,g,y).call(this,{type:"fetch",meta:null==(o=s.fetchOptions)?void 0:o.meta});const i=e=>{var t,a,n,o;Ka(e)&&e.silent||__privateMethod(this,g,y).call(this,{type:"error",error:e}),Ka(e)||(null==(a=(t=__privateGet(this,m).config).onError)||a.call(t,e,this),null==(o=(n=__privateGet(this,m).config).onSettled)||o.call(n,this.state.data,e,this)),this.scheduleGc()};return __privateSet(this,v,Ya({initialPromise:null==t?void 0:t.initialPromise,fn:s.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{var t,a,n,o;if(void 0!==e){try{this.setData(e)}catch(r){return void i(r)}null==(a=(t=__privateGet(this,m).config).onSuccess)||a.call(t,e,this),null==(o=(n=__privateGet(this,m).config).onSettled)||o.call(n,e,this.state.error,this),this.scheduleGc()}else i(new Error(`${this.queryHash} data is undefined`))},onError:i,onFail:(e,t)=>{__privateMethod(this,g,y).call(this,{type:"failed",failureCount:e,error:t})},onPause:()=>{__privateMethod(this,g,y).call(this,{type:"pause"})},onContinue:()=>{__privateMethod(this,g,y).call(this,{type:"continue"})},retry:s.options.retry,retryDelay:s.options.retryDelay,networkMode:s.options.networkMode,canRun:()=>!0})),__privateGet(this,v).start()}},d=new WeakMap,p=new WeakMap,m=new WeakMap,v=new WeakMap,f=new WeakMap,h=new WeakMap,g=new WeakSet,y=function(e){this.state=(t=>{var a,n,o,r;switch(e.type){case"failed":return __spreadProps(__spreadValues({},t),{fetchFailureCount:e.failureCount,fetchFailureReason:e.error});case"pause":return __spreadProps(__spreadValues({},t),{fetchStatus:"paused"});case"continue":return __spreadProps(__spreadValues({},t),{fetchStatus:"fetching"});case"fetch":return __spreadProps(__spreadValues(__spreadValues({},t),(o=t.data,r=this.options,__spreadValues({fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Ua(r.networkMode)?"fetching":"paused"},void 0===o&&{error:null,status:"pending"}))),{fetchMeta:null!=(a=e.meta)?a:null});case"success":return __spreadValues(__spreadProps(__spreadValues({},t),{data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:null!=(n=e.dataUpdatedAt)?n:Date.now(),error:null,isInvalidated:!1,status:"success"}),!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null});case"error":const l=e.error;return Ka(l)&&l.revert&&__privateGet(this,p)?__spreadProps(__spreadValues({},__privateGet(this,p)),{fetchStatus:"idle"}):__spreadProps(__spreadValues({},t),{error:l,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:l,fetchStatus:"idle",status:"error"});case"invalidate":return __spreadProps(__spreadValues({},t),{isInvalidated:!0});case"setState":return __spreadValues(__spreadValues({},t),e.state)}})(this.state),Xa.batch((()=>{this.observers.forEach((e=>{e.onQueryUpdate()})),__privateGet(this,m).notify({query:this,type:"updated",action:e})}))},_);var en=(b=class extends Ea{constructor(e={}){super(),__privateAdd(this,w),this.config=e,__privateSet(this,w,new Map)}build(e,t,a){var n;const o=t.queryKey,r=null!=(n=t.queryHash)?n:Pa(o,t);let l=this.get(r);return l||(l=new Za({cache:this,queryKey:o,queryHash:r,options:e.defaultQueryOptions(t),state:a,defaultOptions:e.getQueryDefaults(o)}),this.add(l)),l}add(e){__privateGet(this,w).has(e.queryHash)||(__privateGet(this,w).set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const t=__privateGet(this,w).get(e.queryHash);t&&(e.destroy(),t===e&&__privateGet(this,w).delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){Xa.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}get(e){return __privateGet(this,w).get(e)}getAll(){return[...__privateGet(this,w).values()]}find(e){const t=__spreadValues({exact:!0},e);return this.getAll().find((e=>za(t,e)))}findAll(e={}){const t=this.getAll();return Object.keys(e).length>0?t.filter((t=>za(e,t))):t}notify(e){Xa.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}onFocus(){Xa.batch((()=>{this.getAll().forEach((e=>{e.onFocus()}))}))}onOnline(){Xa.batch((()=>{this.getAll().forEach((e=>{e.onOnline()}))}))}},w=new WeakMap,b),tn=(N=class extends Ja{constructor(e){super(),__privateAdd(this,V),__privateAdd(this,k),__privateAdd(this,x),__privateAdd(this,C),this.mutationId=e.mutationId,__privateSet(this,x,e.mutationCache),__privateSet(this,k,[]),this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){__privateGet(this,k).includes(e)||(__privateGet(this,k).push(e),this.clearGcTimeout(),__privateGet(this,x).notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){__privateSet(this,k,__privateGet(this,k).filter((t=>t!==e))),this.scheduleGc(),__privateGet(this,x).notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){__privateGet(this,k).length||("pending"===this.state.status?this.scheduleGc():__privateGet(this,x).remove(this))}continue(){var e,t;return null!=(t=null==(e=__privateGet(this,C))?void 0:e.continue())?t:this.execute(this.state.variables)}execute(e){return __async(this,null,(function*(){var t,a,n,o,r,l,s,i,c,u,d,p,m,v,f,h,g,y,_,w,b;__privateSet(this,C,Ya({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(e,t)=>{__privateMethod(this,V,E).call(this,{type:"failed",failureCount:e,error:t})},onPause:()=>{__privateMethod(this,V,E).call(this,{type:"pause"})},onContinue:()=>{__privateMethod(this,V,E).call(this,{type:"continue"})},retry:null!=(t=this.options.retry)?t:0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>__privateGet(this,x).canRun(this)}));const k="pending"===this.state.status,N=!__privateGet(this,C).canStart();try{if(!k){__privateMethod(this,V,E).call(this,{type:"pending",variables:e,isPaused:N}),yield null==(n=(a=__privateGet(this,x).config).onMutate)?void 0:n.call(a,e,this);const t=yield null==(r=(o=this.options).onMutate)?void 0:r.call(o,e);t!==this.state.context&&__privateMethod(this,V,E).call(this,{type:"pending",context:t,variables:e,isPaused:N})}const t=yield __privateGet(this,C).start();return yield null==(s=(l=__privateGet(this,x).config).onSuccess)?void 0:s.call(l,t,e,this.state.context,this),yield null==(c=(i=this.options).onSuccess)?void 0:c.call(i,t,e,this.state.context),yield null==(d=(u=__privateGet(this,x).config).onSettled)?void 0:d.call(u,t,null,this.state.variables,this.state.context,this),yield null==(m=(p=this.options).onSettled)?void 0:m.call(p,t,null,e,this.state.context),__privateMethod(this,V,E).call(this,{type:"success",data:t}),t}catch(S){try{throw yield null==(f=(v=__privateGet(this,x).config).onError)?void 0:f.call(v,S,e,this.state.context,this),yield null==(g=(h=this.options).onError)?void 0:g.call(h,S,e,this.state.context),yield null==(_=(y=__privateGet(this,x).config).onSettled)?void 0:_.call(y,void 0,S,this.state.variables,this.state.context,this),yield null==(b=(w=this.options).onSettled)?void 0:b.call(w,void 0,S,e,this.state.context),S}finally{__privateMethod(this,V,E).call(this,{type:"error",error:S})}}finally{__privateGet(this,x).runNext(this)}}))}},k=new WeakMap,x=new WeakMap,C=new WeakMap,V=new WeakSet,E=function(e){this.state=(t=>{switch(e.type){case"failed":return __spreadProps(__spreadValues({},t),{failureCount:e.failureCount,failureReason:e.error});case"pause":return __spreadProps(__spreadValues({},t),{isPaused:!0});case"continue":return __spreadProps(__spreadValues({},t),{isPaused:!1});case"pending":return __spreadProps(__spreadValues({},t),{context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()});case"success":return __spreadProps(__spreadValues({},t),{data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1});case"error":return __spreadProps(__spreadValues({},t),{data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"})}})(this.state),Xa.batch((()=>{__privateGet(this,k).forEach((t=>{t.onMutationUpdate(e)})),__privateGet(this,x).notify({mutation:this,type:"updated",action:e})}))},N);var an=($=class extends Ea{constructor(e={}){super(),__privateAdd(this,S),__privateAdd(this,B),__privateAdd(this,z),this.config=e,__privateSet(this,S,new Set),__privateSet(this,B,new Map),__privateSet(this,z,0)}build(e,t,a){const n=new tn({mutationCache:this,mutationId:++__privateWrapper(this,z)._,options:e.defaultMutationOptions(t),state:a});return this.add(n),n}add(e){__privateGet(this,S).add(e);const t=nn(e);if("string"==typeof t){const a=__privateGet(this,B).get(t);a?a.push(e):__privateGet(this,B).set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(__privateGet(this,S).delete(e)){const t=nn(e);if("string"==typeof t){const a=__privateGet(this,B).get(t);if(a)if(a.length>1){const t=a.indexOf(e);-1!==t&&a.splice(t,1)}else a[0]===e&&__privateGet(this,B).delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){const t=nn(e);if("string"==typeof t){const a=__privateGet(this,B).get(t),n=null==a?void 0:a.find((e=>"pending"===e.state.status));return!n||n===e}return!0}runNext(e){var t,a;const n=nn(e);if("string"==typeof n){const o=null==(t=__privateGet(this,B).get(n))?void 0:t.find((t=>t!==e&&t.state.isPaused));return null!=(a=null==o?void 0:o.continue())?a:Promise.resolve()}return Promise.resolve()}clear(){Xa.batch((()=>{__privateGet(this,S).forEach((e=>{this.notify({type:"removed",mutation:e})})),__privateGet(this,S).clear(),__privateGet(this,B).clear()}))}getAll(){return Array.from(__privateGet(this,S))}find(e){const t=__spreadValues({exact:!0},e);return this.getAll().find((e=>$a(t,e)))}findAll(e={}){return this.getAll().filter((t=>$a(e,t)))}notify(e){Xa.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}resumePausedMutations(){const e=this.getAll().filter((e=>e.state.isPaused));return Xa.batch((()=>Promise.all(e.map((e=>e.continue().catch(Sa))))))}},S=new WeakMap,B=new WeakMap,z=new WeakMap,$);function nn(e){var t;return null==(t=e.options.scope)?void 0:t.id}function on(e){return{onFetch:(t,a)=>{var n,o,r,l,s;const i=t.options,c=null==(r=null==(o=null==(n=t.fetchOptions)?void 0:n.meta)?void 0:o.fetchMore)?void 0:r.direction,u=(null==(l=t.state.data)?void 0:l.pages)||[],d=(null==(s=t.state.data)?void 0:s.pageParams)||[];let p={pages:[],pageParams:[]},m=0;const v=()=>__async(null,null,(function*(){var a;let n=!1;const o=Ra(t.options,t.fetchOptions),r=(e,a,r)=>__async(null,null,(function*(){if(n)return Promise.reject();if(null==a&&e.pages.length)return Promise.resolve(e);const l={queryKey:t.queryKey,pageParam:a,direction:r?"backward":"forward",meta:t.options.meta};var s;s=l,Object.defineProperty(s,"signal",{enumerable:!0,get:()=>(t.signal.aborted?n=!0:t.signal.addEventListener("abort",(()=>{n=!0})),t.signal)});const i=yield o(l),{maxPages:c}=t.options,u=r?Ga:ja;return{pages:u(e.pages,i,c),pageParams:u(e.pageParams,a,c)}}));if(c&&u.length){const e="backward"===c,t={pages:u,pageParams:d},a=(e?ln:rn)(i,t);p=yield r(t,a,e)}else{const t=null!=e?e:u.length;do{const e=0===m?null!=(a=d[0])?a:i.initialPageParam:rn(i,p);if(m>0&&null==e)break;p=yield r(p,e),m++}while(m<t)}return p}));t.options.persister?t.fetchFn=()=>{var e,n;return null==(n=(e=t.options).persister)?void 0:n.call(e,v,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},a)}:t.fetchFn=v}}}function rn(e,{pages:t,pageParams:a}){const n=t.length-1;return t.length>0?e.getNextPageParam(t[n],t,a[n],a):void 0}function ln(e,{pages:t,pageParams:a}){var n;return t.length>0?null==(n=e.getPreviousPageParam)?void 0:n.call(e,t[0],t,a[0],a):void 0}var sn=(j=class{constructor(e={}){__privateAdd(this,P),__privateAdd(this,I),__privateAdd(this,T),__privateAdd(this,A),__privateAdd(this,D),__privateAdd(this,M),__privateAdd(this,O),__privateAdd(this,F),__privateSet(this,P,e.queryCache||new en),__privateSet(this,I,e.mutationCache||new an),__privateSet(this,T,e.defaultOptions||{}),__privateSet(this,A,new Map),__privateSet(this,D,new Map),__privateSet(this,M,0)}mount(){__privateWrapper(this,M)._++,1===__privateGet(this,M)&&(__privateSet(this,O,Ha.subscribe((e=>__async(this,null,(function*(){e&&(yield this.resumePausedMutations(),__privateGet(this,P).onFocus())}))))),__privateSet(this,F,qa.subscribe((e=>__async(this,null,(function*(){e&&(yield this.resumePausedMutations(),__privateGet(this,P).onOnline())}))))))}unmount(){var e,t;__privateWrapper(this,M)._--,0===__privateGet(this,M)&&(null==(e=__privateGet(this,O))||e.call(this),__privateSet(this,O,void 0),null==(t=__privateGet(this,F))||t.call(this),__privateSet(this,F,void 0))}isFetching(e){return __privateGet(this,P).findAll(__spreadProps(__spreadValues({},e),{fetchStatus:"fetching"})).length}isMutating(e){return __privateGet(this,I).findAll(__spreadProps(__spreadValues({},e),{status:"pending"})).length}getQueryData(e){var t;const a=this.defaultQueryOptions({queryKey:e});return null==(t=__privateGet(this,P).get(a.queryHash))?void 0:t.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),a=__privateGet(this,P).build(this,t),n=a.state.data;return void 0===n?this.fetchQuery(e):(e.revalidateIfStale&&a.isStaleByTime(Ba(t.staleTime,a))&&this.prefetchQuery(t),Promise.resolve(n))}getQueriesData(e){return __privateGet(this,P).findAll(e).map((({queryKey:e,state:t})=>[e,t.data]))}setQueryData(e,t,a){const n=this.defaultQueryOptions({queryKey:e}),o=__privateGet(this,P).get(n.queryHash),r=function(e,t){return"function"==typeof e?e(t):e}(t,null==o?void 0:o.state.data);if(void 0!==r)return __privateGet(this,P).build(this,n).setData(r,__spreadProps(__spreadValues({},a),{manual:!0}))}setQueriesData(e,t,a){return Xa.batch((()=>__privateGet(this,P).findAll(e).map((({queryKey:e})=>[e,this.setQueryData(e,t,a)]))))}getQueryState(e){var t;const a=this.defaultQueryOptions({queryKey:e});return null==(t=__privateGet(this,P).get(a.queryHash))?void 0:t.state}removeQueries(e){const t=__privateGet(this,P);Xa.batch((()=>{t.findAll(e).forEach((e=>{t.remove(e)}))}))}resetQueries(e,t){const a=__privateGet(this,P),n=__spreadValues({type:"active"},e);return Xa.batch((()=>(a.findAll(e).forEach((e=>{e.reset()})),this.refetchQueries(n,t))))}cancelQueries(e,t={}){const a=__spreadValues({revert:!0},t),n=Xa.batch((()=>__privateGet(this,P).findAll(e).map((e=>e.cancel(a)))));return Promise.all(n).then(Sa).catch(Sa)}invalidateQueries(e,t={}){return Xa.batch((()=>{var a,n;if(__privateGet(this,P).findAll(e).forEach((e=>{e.invalidate()})),"none"===(null==e?void 0:e.refetchType))return Promise.resolve();const o=__spreadProps(__spreadValues({},e),{type:null!=(n=null!=(a=null==e?void 0:e.refetchType)?a:null==e?void 0:e.type)?n:"active"});return this.refetchQueries(o,t)}))}refetchQueries(e,t={}){var a;const n=__spreadProps(__spreadValues({},t),{cancelRefetch:null==(a=t.cancelRefetch)||a}),o=Xa.batch((()=>__privateGet(this,P).findAll(e).filter((e=>!e.isDisabled())).map((e=>{let t=e.fetch(void 0,n);return n.throwOnError||(t=t.catch(Sa)),"paused"===e.state.fetchStatus?Promise.resolve():t}))));return Promise.all(o).then(Sa)}fetchQuery(e){const t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);const a=__privateGet(this,P).build(this,t);return a.isStaleByTime(Ba(t.staleTime,a))?a.fetch(t):Promise.resolve(a.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(Sa).catch(Sa)}fetchInfiniteQuery(e){return e.behavior=on(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(Sa).catch(Sa)}ensureInfiniteQueryData(e){return e.behavior=on(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return qa.isOnline()?__privateGet(this,I).resumePausedMutations():Promise.resolve()}getQueryCache(){return __privateGet(this,P)}getMutationCache(){return __privateGet(this,I)}getDefaultOptions(){return __privateGet(this,T)}setDefaultOptions(e){__privateSet(this,T,e)}setQueryDefaults(e,t){__privateGet(this,A).set(Ia(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...__privateGet(this,A).values()],a={};return t.forEach((t=>{Ta(e,t.queryKey)&&Object.assign(a,t.defaultOptions)})),a}setMutationDefaults(e,t){__privateGet(this,D).set(Ia(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...__privateGet(this,D).values()];let a={};return t.forEach((t=>{Ta(e,t.mutationKey)&&(a=__spreadValues(__spreadValues({},a),t.defaultOptions))})),a}defaultQueryOptions(e){if(e._defaulted)return e;const t=__spreadProps(__spreadValues(__spreadValues(__spreadValues({},__privateGet(this,T).queries),this.getQueryDefaults(e.queryKey)),e),{_defaulted:!0});return t.queryHash||(t.queryHash=Pa(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===La&&(t.enabled=!1),t}defaultMutationOptions(e){return(null==e?void 0:e._defaulted)?e:__spreadProps(__spreadValues(__spreadValues(__spreadValues({},__privateGet(this,T).mutations),(null==e?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey)),e),{_defaulted:!0})}clear(){__privateGet(this,P).clear(),__privateGet(this,I).clear()}},P=new WeakMap,I=new WeakMap,T=new WeakMap,A=new WeakMap,D=new WeakMap,M=new WeakMap,O=new WeakMap,F=new WeakMap,j);function cn(t,a,n="",o=0){if(a){const r=a(t,n,o);if(void 0===r&&e.isRef(t))return r;if(void 0!==r)return r}if(Array.isArray(t))return t.map(((e,t)=>cn(e,a,String(t),o+1)));if("object"==typeof t&&function(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}(t)){const e=Object.entries(t).map((([e,t])=>[e,cn(t,a,e,o+1)]));return Object.fromEntries(e)}return t}function un(t,a=!1){return cn(t,((t,n,o)=>1===o&&"queryKey"===n?un(t,!0):a&&"function"==typeof t?un(t(),a):e.isRef(t)?un(e.unref(t),a):void 0))}var dn=class extends en{find(e){return super.find(un(e))}findAll(e={}){return super.findAll(un(e))}},pn=class extends an{find(e){return super.find(un(e))}findAll(e={}){return super.findAll(un(e))}},mn=class extends sn{constructor(t={}){super({defaultOptions:t.defaultOptions,queryCache:t.queryCache||new dn,mutationCache:t.mutationCache||new pn}),this.isRestoring=e.ref(!1)}isFetching(e={}){return super.isFetching(un(e))}isMutating(e={}){return super.isMutating(un(e))}getQueryData(e){return super.getQueryData(un(e))}ensureQueryData(e){return super.ensureQueryData(un(e))}getQueriesData(e){return super.getQueriesData(un(e))}setQueryData(e,t,a={}){return super.setQueryData(un(e),t,un(a))}setQueriesData(e,t,a={}){return super.setQueriesData(un(e),t,un(a))}getQueryState(e){return super.getQueryState(un(e))}removeQueries(e={}){return super.removeQueries(un(e))}resetQueries(e={},t={}){return super.resetQueries(un(e),un(t))}cancelQueries(e={},t={}){return super.cancelQueries(un(e),un(t))}invalidateQueries(t={},a={}){var n,o;const r=un(t),l=un(a);if(super.invalidateQueries(__spreadProps(__spreadValues({},r),{refetchType:"none"}),l),"none"===r.refetchType)return Promise.resolve();const s=__spreadProps(__spreadValues({},r),{type:null!=(o=null!=(n=r.refetchType)?n:r.type)?o:"active"});return e.nextTick().then((()=>super.refetchQueries(s,l)))}refetchQueries(e={},t={}){return super.refetchQueries(un(e),un(t))}fetchQuery(e){return super.fetchQuery(un(e))}prefetchQuery(e){return super.prefetchQuery(un(e))}fetchInfiniteQuery(e){return super.fetchInfiniteQuery(un(e))}prefetchInfiniteQuery(e){return super.prefetchInfiniteQuery(un(e))}setDefaultOptions(e){super.setDefaultOptions(un(e))}setQueryDefaults(e,t){super.setQueryDefaults(un(e),un(t))}getQueryDefaults(e){return super.getQueryDefaults(un(e))}setMutationDefaults(e,t){super.setMutationDefaults(un(e),un(t))}getMutationDefaults(e){return super.getMutationDefaults(un(e))}},vn={install:(e,t={})=>{const a="VUE_QUERY_CLIENT"+((n=t.queryClientKey)?`:${n}`:"");var n;let o;if("queryClient"in t&&t.queryClient)o=t.queryClient;else{const e="queryClientConfig"in t?t.queryClientConfig:void 0;o=new mn(e)}Na||o.mount();let r=()=>{};if(t.clientPersister){o.isRestoring.value=!0;const[e,a]=t.clientPersister(o);r=e,a.then((()=>{var e;o.isRestoring.value=!1,null==(e=t.clientPersisterOnSuccess)||e.call(t,o)}))}const l=()=>{o.unmount(),r()};if(e.onUnmount)e.onUnmount(l);else{const t=e.unmount;e.unmount=function(){l(),t()}}e.provide(a,o)}},fn="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function hn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var gn;gn||(gn=1,function(){function e(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=Array(t);a<t;a++)n[a]=e[a];return n}function t(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function a(e,t,a){return t=i(t),d(e,u()?Reflect.construct(t,[],i(e).constructor):t.apply(e,a))}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var a=0;a<t.length;a++){var n=t[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,h(n.key),n)}}function r(e,t,a){return t&&o(e.prototype,t),a&&o(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e}function l(e,t){var a="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!a){if(Array.isArray(e)||(a=g(e))||t){a&&(e=a);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,l=!0,s=!1;return{s:function(){a=a.call(e)},n:function(){var e=a.next();return l=e.done,e},e:function(e){s=!0,r=e},f:function(){try{l||null==a.return||a.return()}finally{if(s)throw r}}}}function s(){return s="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,a){var n=m(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(arguments.length<3?e:a):o.value}},s.apply(null,arguments)}function i(e){return(i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function c(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&p(e,t)}function u(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(u=function(){return!!e})()}function d(e,a){if(a&&("object"==typeof a||"function"==typeof a))return a;if(void 0!==a)throw new TypeError("Derived constructors may only return object or undefined");return t(e)}function p(e,t){return(p=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function m(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=i(e)););return e}function v(e,t,a,n){var o=s(i(e.prototype),t,a);return"function"==typeof o?function(e){return o.apply(a,e)}:o}function f(e,t){if("object"!=typeof e||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var n=a.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function h(e){var t=f(e,"string");return"symbol"==typeof t?t:t+""}function g(t,a){if(t){if("string"==typeof t)return e(t,a);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?e(t,a):void 0}}function y(e){var t;try{t=new Event("abort")}catch(a){"undefined"!=typeof document?document.createEvent?(t=document.createEvent("Event")).initEvent("abort",!1,!1):(t=document.createEventObject()).type="abort":t={type:"abort",bubbles:!1,cancelable:!1}}return t.reason=e,t}function _(e){if(void 0===e)if("undefined"==typeof document)(e=new Error("This operation was aborted")).name="AbortError";else try{e=new DOMException("signal is aborted without reason"),Object.defineProperty(e,"name",{value:"AbortError"})}catch(t){(e=new Error("This operation was aborted")).name="AbortError"}return e}var w,b=function(){function e(){n(this,e),Object.defineProperty(this,"listeners",{value:{},writable:!0,configurable:!0})}return r(e,[{key:"addEventListener",value:function(e,t,a){e in this.listeners||(this.listeners[e]=[]),this.listeners[e].push({callback:t,options:a})}},{key:"removeEventListener",value:function(e,t){if(e in this.listeners)for(var a=this.listeners[e],n=0,o=a.length;n<o;n++)if(a[n].callback===t)return void a.splice(n,1)}},{key:"dispatchEvent",value:function(e){var t=this;if(e.type in this.listeners){for(var a=this.listeners[e.type].slice(),n=function(){var n=a[o];try{n.callback.call(t,e)}catch(r){Promise.resolve().then((function(){throw r}))}n.options&&n.options.once&&t.removeEventListener(e.type,n.callback)},o=0,r=a.length;o<r;o++)n();return!e.defaultPrevented}}}])}(),k=function(e){function t(){var e;return n(this,t),(e=a(this,t)).listeners||b.call(e),Object.defineProperty(e,"aborted",{value:!1,writable:!0,configurable:!0}),Object.defineProperty(e,"onabort",{value:null,writable:!0,configurable:!0}),Object.defineProperty(e,"reason",{value:void 0,writable:!0,configurable:!0}),e}return c(t,e),r(t,[{key:"toString",value:function(){return"[object AbortSignal]"}},{key:"dispatchEvent",value:function(e){"abort"===e.type&&(this.aborted=!0,"function"==typeof this.onabort&&this.onabort.call(this,e)),v(t,"dispatchEvent",this)([e])}},{key:"throwIfAborted",value:function(){var e=this.aborted,t=this.reason;if(e)throw void 0===t?"Aborted":t}}],[{key:"timeout",value:function(e){var t=new x;return setTimeout((function(){return t.abort(new DOMException("This signal is timeout in ".concat(e,"ms"),"TimeoutError"))}),e),t.signal}},{key:"any",value:function(e){var t=new x;function a(){t.abort(this.reason),n()}function n(){var t,n=l(e);try{for(n.s();!(t=n.n()).done;)t.value.removeEventListener("abort",a)}catch(o){n.e(o)}finally{n.f()}}var o,r=l(e);try{for(r.s();!(o=r.n()).done;){var s=o.value;if(s.aborted){t.abort(s.reason);break}s.addEventListener("abort",a)}}catch(i){r.e(i)}finally{r.f()}return t.signal}}])}(b),x=function(){function e(){n(this,e),Object.defineProperty(this,"signal",{value:new k,writable:!0,configurable:!0})}return r(e,[{key:"abort",value:function(e){var t=_(e),a=y(t);this.signal.reason=t,this.signal.dispatchEvent(a)}},{key:"toString",value:function(){return"[object AbortController]"}}])}();function C(e){return!!e.__FORCE_INSTALL_ABORTCONTROLLER_POLYFILL||"function"==typeof e.Request&&!e.Request.prototype.hasOwnProperty("signal")||!e.AbortController}"undefined"!=typeof Symbol&&Symbol.toStringTag&&(x.prototype[Symbol.toStringTag]="AbortController",k.prototype[Symbol.toStringTag]="AbortSignal"),C(w="undefined"!=typeof self?self:fn)&&(w.AbortController=x,w.AbortSignal=k)}());const yn=e.defineComponent({__name:"App",setup:e=>(Q((()=>{G("log","at App.vue:6","App Launch"),setTimeout((()=>{uni.hideTabBar({animation:!1,fail:()=>{}})}),0)})),H((()=>{G("log","at App.vue:19","App Show"),setTimeout((()=>{uni.hideTabBar({animation:!1,fail:()=>{}})}),0);const e=setInterval((()=>{uni.hideTabBar({animation:!1,fail:()=>{}})}),100);setTimeout((()=>{clearInterval(e)}),3e3)})),q((()=>{G("log","at App.vue:46","App Hide")})),()=>{})});function _n(e,t){var a;return e="object"==typeof(a=e)&&null!==a?e:Object.create(null),new Proxy(e,{get:(e,a,n)=>"key"===a?Reflect.get(e,a,n):Reflect.get(e,a,n)||Reflect.get(t,a,n)})}function wn(e,{storage:t,serializer:a,key:n,debug:o}){try{const o=null==t?void 0:t.getItem(n);o&&e.$patch(null==a?void 0:a.deserialize(o))}catch(r){}}function bn(e,{storage:t,serializer:a,key:n,paths:o,debug:r}){try{const r=Array.isArray(o)?function(e,t){return t.reduce(((t,a)=>{const n=a.split(".");return function(e,t,a){return t.slice(0,-1).reduce(((e,t)=>/^(__proto__)$/.test(t)?{}:e[t]=e[t]||{}),e)[t[t.length-1]]=a,e}(t,n,function(e,t){return t.reduce(((e,t)=>null==e?void 0:e[t]),e)}(e,n))}),{})}(e,o):e;t.setItem(n,a.serialize(r))}catch(l){}}const kn=e=>new Promise(((t,a)=>{uni.request(__spreadProps(__spreadValues({},e),{dataType:"json",responseType:"json",success(n){n.statusCode>=200&&n.statusCode<300?t(n.data):(401===n.statusCode||!e.hideErrorToast&&uni.showToast({icon:"none",title:n.data.msg||"请求错误"}),a(n))},fail(e){uni.showToast({icon:"none",title:"网络错误，换个网络试试"}),a(e)}}))}));kn.get=(e,t,a)=>kn({url:e,query:t,method:"GET",header:a}),kn.post=(e,t,a,n)=>kn({url:e,query:a,data:t,method:"POST",header:n}),kn.put=(e,t,a,n)=>kn({url:e,data:t,query:a,method:"PUT",header:n}),kn.delete=(e,t,a)=>kn({url:e,query:t,method:"DELETE",header:a});function xn(e){const t={type:"info",duration:2e3,position:"middle",message:""},a="string"==typeof e?__spreadProps(__spreadValues({},t),{message:e}):__spreadValues(__spreadValues({},t),e);uni.showToast({title:a.message,duration:a.duration,position:{top:"top",middle:"center",bottom:"bottom"}[a.position],icon:a.icon||{success:"success",error:"error",warning:"fail",info:"none"}[a.type],mask:!0})}const Cn=(e,t)=>xn(__spreadProps(__spreadValues({},t),{type:"success",message:e})),Vn={id:0,username:"",avatar:"/static/images/default-avatar.png",token:""},En=Je("user",(()=>{const t=e.ref(__spreadValues({},Vn)),a=()=>__async(null,null,(function*(){const e=yield kn.get("/user/info"),a=e.data;var n;return G("log","at store/user.ts:28","设置用户信息",n=a),n.avatar?n.avatar="https://oss.laf.run/ukw0y1-site/avatar.jpg?feige":n.avatar=Vn.avatar,t.value=n,uni.setStorageSync("userInfo",a),uni.setStorageSync("token",a.token),e}));return{userInfo:t,login:e=>__async(null,null,(function*(){const t=yield(n=e,kn.post("/user/login",n));var n;return G("log","at store/user.ts:55","登录信息",t),Cn("登录成功"),a(),t})),wxLogin:()=>__async(null,null,(function*(){const e=yield new Promise(((e,t)=>{uni.login({provider:"weixin",success:t=>e(t),fail:e=>t(new Error(e))})}));G("log","at store/user.ts:85","微信登录code",e);const t=yield(e=>kn.post("/user/wxLogin",e))(e);return a(),t})),getUserInfo:a,logout:()=>__async(null,null,(function*(){kn.get("/user/logout"),t.value=__spreadValues({},Vn),uni.removeStorageSync("userInfo"),uni.removeStorageSync("token")}))}}),{persist:!0}),Nn=function(){const t=e.effectScope(!0),a=t.run((()=>e.ref({})));let n=[],o=[];const r=e.markRaw({install(e){je(r),r._a=e,e.provide(Ge,r),e.config.globalProperties.$pinia=r,o.forEach((e=>n.push(e))),o=[]},use(e){return this._a?n.push(e):o.push(e),this},_p:n,_a:null,_e:t,_s:new Map,state:a});return r}();Nn.use(function(e={}){return t=>{const{auto:a=!1}=e,{options:{persist:n=a},store:o,pinia:r}=t;if(!n)return;if(!(o.$id in r.state.value)){const e=r._s.get(o.$id.replace("__hot:",""));return void(e&&Promise.resolve().then((()=>e.$persist())))}const l=(Array.isArray(n)?n.map((t=>_n(t,e))):[_n(n,e)]).map(function(e,t){return a=>{var n;try{const{storage:o=localStorage,beforeRestore:r,afterRestore:l,serializer:s={serialize:JSON.stringify,deserialize:JSON.parse},key:i=t.$id,paths:c=null,debug:u=!1}=a;return{storage:o,beforeRestore:r,afterRestore:l,serializer:s,key:(null!=(n=e.key)?n:e=>e)("string"==typeof i?i:i(t.$id)),paths:c,debug:u}}catch(o){return a.debug,null}}}(e,o)).filter(Boolean);o.$persist=()=>{l.forEach((e=>{bn(o.$state,e)}))},o.$hydrate=({runHooks:e=!0}={})=>{l.forEach((a=>{const{beforeRestore:n,afterRestore:r}=a;e&&(null==n||n(t)),wn(o,a),e&&(null==r||r(t))}))},l.forEach((e=>{const{beforeRestore:a,afterRestore:n}=e;null==a||a(t),wn(o,e),null==n||n(t),o.$subscribe(((t,a)=>{bn(a,e)}),{detached:!0})}))}}({storage:{getItem:uni.getStorageSync,setItem:uni.setStorageSync}}));const Sn={globalStyle:{navigationStyle:"default",navigationBarTitleText:"invoice-book-uniapp",navigationBarBackgroundColor:"#f8f8f8",navigationBarTextStyle:"black",backgroundColor:"#FFFFFF"},easycom:{autoscan:!0,custom:{"^fg-(.*)":"@/components/fg-$1/fg-$1.vue","^wd-(.*)":"wot-design-uni/components/wd-$1/wd-$1.vue","^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)":"z-paging/components/z-paging$1/z-paging$1.vue"}},pages:[{path:"pages/user/index",type:"home",style:{navigationStyle:"custom",navigationBarTitleText:"主页"}},{path:"pages/index/index",type:"page",style:{navigationStyle:"custom",navigationBarTitleText:"主页"}},{path:"pages/merchants/goods",type:"page",style:{navigationStyle:"custom",navigationBarTitleText:"商品管理"}},{path:"pages/merchants/index",type:"page",style:{navigationStyle:"custom",navigationBarTitleText:"主页"}},{path:"pages/merchants/mine",type:"page",style:{navigationStyle:"custom",navigationBarTitleText:"我的"}},{path:"pages/merchants/orders",type:"page",style:{navigationStyle:"custom",navigationBarTitleText:"订单"}},{path:"pages/merchants/statistics",type:"page",style:{navigationStyle:"custom",navigationBarTitleText:"统计"}},{path:"pages/sys/dashboard",type:"page",style:{navigationStyle:"custom",navigationBarTitleText:"仪表盘"}},{path:"pages/sys/goods",type:"page",style:{navigationStyle:"custom",navigationBarTitleText:"商品管理"}},{path:"pages/sys/merchants",type:"page",style:{navigationStyle:"custom",navigationBarTitleText:"商户管理"}},{path:"pages/sys/mine",type:"page",style:{navigationStyle:"custom",navigationBarTitleText:"我的"}},{path:"pages/sys/system",type:"page",style:{navigationStyle:"custom",navigationBarTitleText:"系统管理"}},{path:"pages/user/category",type:"page",style:{navigationStyle:"custom",navigationBarTitleText:"分类"}},{path:"pages/user/goods-detail",type:"page",style:{navigationStyle:"custom",navigationBarTitleText:"商品详情"}},{path:"pages/user/mine",type:"page",style:{navigationStyle:"custom",navigationBarTitleText:"我的"}},{path:"pages/user/search",type:"page",style:{navigationStyle:"custom",navigationBarTitleText:"搜索"}}],subPackages:[]},{pages:Bn,subPackages:zn}=__spreadValues({},Sn),$n=((e="needLogin")=>{const t=[...Bn.filter((t=>!e||t[e])).map((e=>__spreadProps(__spreadValues({},e),{path:`/${e.path}`})))],a=[];zn.forEach((t=>{const{root:n}=t;t.pages.filter((t=>!e||t[e])).forEach((e=>{a.push(__spreadProps(__spreadValues({},e),{path:`/${n}/${e.path}`}))}))}));return[...t,...a]})("needLogin").map((e=>e.path)),Pn={invoke({url:e}){let t=e.split("?")[0];if(!t.startsWith("/")){const e=(()=>{const e=getCurrentPages();return e[e.length-1]})().route,a=e.startsWith("/")?e:`/${e}`;t=`${a.substring(0,a.lastIndexOf("/"))}/${t}`}let a=[];a=$n;if(!a.includes(t))return!0;if(!!En().userInfo.username)return!0;const n=`/pages/login/index?redirect=${encodeURIComponent(e)}`;return uni.navigateTo({url:n}),!1}},In={install(){uni.addInterceptor("navigateTo",Pn),uni.addInterceptor("reLaunch",Pn),uni.addInterceptor("redirectTo",Pn),uni.addInterceptor("switchTab",Pn)}};var Tn,An,Dn,Mn,On,Fn,jn,Gn,Ln,Rn;function Hn(){if(An)return Tn;An=1;var e=Object.prototype.hasOwnProperty,t=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),a=function(e,t){for(var a=t&&t.plainObjects?Object.create(null):{},n=0;n<e.length;++n)void 0!==e[n]&&(a[n]=e[n]);return a};return Tn={arrayToObject:a,assign:function(e,t){return Object.keys(t).reduce((function(e,a){return e[a]=t[a],e}),e)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],a=[],n=0;n<t.length;++n)for(var o=t[n],r=o.obj[o.prop],l=Object.keys(r),s=0;s<l.length;++s){var i=l[s],c=r[i];"object"==typeof c&&null!==c&&-1===a.indexOf(c)&&(t.push({obj:r,prop:i}),a.push(c))}return function(e){for(var t;e.length;){var a=e.pop();if(t=a.obj[a.prop],Array.isArray(t)){for(var n=[],o=0;o<t.length;++o)void 0!==t[o]&&n.push(t[o]);a.obj[a.prop]=n}}return t}(t)},decode:function(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch(t){return e}},encode:function(e){if(0===e.length)return e;for(var a="string"==typeof e?e:String(e),n="",o=0;o<a.length;++o){var r=a.charCodeAt(o);45===r||46===r||95===r||126===r||r>=48&&r<=57||r>=65&&r<=90||r>=97&&r<=122?n+=a.charAt(o):r<128?n+=t[r]:r<2048?n+=t[192|r>>6]+t[128|63&r]:r<55296||r>=57344?n+=t[224|r>>12]+t[128|r>>6&63]+t[128|63&r]:(o+=1,r=65536+((1023&r)<<10|1023&a.charCodeAt(o)),n+=t[240|r>>18]+t[128|r>>12&63]+t[128|r>>6&63]+t[128|63&r])}return n},isBuffer:function(e){return null!=e&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},merge:function t(n,o,r){if(!o)return n;if("object"!=typeof o){if(Array.isArray(n))n.push(o);else{if(!n||"object"!=typeof n)return[n,o];(r&&(r.plainObjects||r.allowPrototypes)||!e.call(Object.prototype,o))&&(n[o]=!0)}return n}if(!n||"object"!=typeof n)return[n].concat(o);var l=n;return Array.isArray(n)&&!Array.isArray(o)&&(l=a(n,r)),Array.isArray(n)&&Array.isArray(o)?(o.forEach((function(a,o){if(e.call(n,o)){var l=n[o];l&&"object"==typeof l&&a&&"object"==typeof a?n[o]=t(l,a,r):n.push(a)}else n[o]=a})),n):Object.keys(o).reduce((function(a,n){var l=o[n];return e.call(a,n)?a[n]=t(a[n],l,r):a[n]=l,a}),l)}}}function qn(){if(Mn)return Dn;Mn=1;var e=String.prototype.replace,t=/%20/g;return Dn={default:"RFC3986",formatters:{RFC1738:function(a){return e.call(a,t,"+")},RFC3986:function(e){return String(e)}},RFC1738:"RFC1738",RFC3986:"RFC3986"}}function Qn(){if(Gn)return jn;Gn=1;var e=Hn(),t=Object.prototype.hasOwnProperty,a={allowDots:!1,allowPrototypes:!1,arrayLimit:20,decoder:e.decode,delimiter:"&",depth:5,parameterLimit:1e3,plainObjects:!1,strictNullHandling:!1},n=function(e,a,n){if(e){var o=n.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,r=/(\[[^[\]]*])/g,l=/(\[[^[\]]*])/.exec(o),s=l?o.slice(0,l.index):o,i=[];if(s){if(!n.plainObjects&&t.call(Object.prototype,s)&&!n.allowPrototypes)return;i.push(s)}for(var c=0;null!==(l=r.exec(o))&&c<n.depth;){if(c+=1,!n.plainObjects&&t.call(Object.prototype,l[1].slice(1,-1))&&!n.allowPrototypes)return;i.push(l[1])}return l&&i.push("["+o.slice(l.index)+"]"),function(e,t,a){for(var n=t,o=e.length-1;o>=0;--o){var r,l=e[o];if("[]"===l&&a.parseArrays)r=[].concat(n);else{r=a.plainObjects?Object.create(null):{};var s="["===l.charAt(0)&&"]"===l.charAt(l.length-1)?l.slice(1,-1):l,i=parseInt(s,10);a.parseArrays||""!==s?!isNaN(i)&&l!==s&&String(i)===s&&i>=0&&a.parseArrays&&i<=a.arrayLimit?(r=[])[i]=n:"__proto__"!==s&&(r[s]=n):r={0:n}}n=r}return n}(i,a,n)}};return jn=function(o,r){var l=r?e.assign({},r):{};if(null!==l.decoder&&void 0!==l.decoder&&"function"!=typeof l.decoder)throw new TypeError("Decoder has to be a function.");if(l.ignoreQueryPrefix=!0===l.ignoreQueryPrefix,l.delimiter="string"==typeof l.delimiter||e.isRegExp(l.delimiter)?l.delimiter:a.delimiter,l.depth="number"==typeof l.depth?l.depth:a.depth,l.arrayLimit="number"==typeof l.arrayLimit?l.arrayLimit:a.arrayLimit,l.parseArrays=!1!==l.parseArrays,l.decoder="function"==typeof l.decoder?l.decoder:a.decoder,l.allowDots="boolean"==typeof l.allowDots?l.allowDots:a.allowDots,l.plainObjects="boolean"==typeof l.plainObjects?l.plainObjects:a.plainObjects,l.allowPrototypes="boolean"==typeof l.allowPrototypes?l.allowPrototypes:a.allowPrototypes,l.parameterLimit="number"==typeof l.parameterLimit?l.parameterLimit:a.parameterLimit,l.strictNullHandling="boolean"==typeof l.strictNullHandling?l.strictNullHandling:a.strictNullHandling,""===o||null==o)return l.plainObjects?Object.create(null):{};for(var s="string"==typeof o?function(e,n){for(var o={},r=n.ignoreQueryPrefix?e.replace(/^\?/,""):e,l=n.parameterLimit===1/0?void 0:n.parameterLimit,s=r.split(n.delimiter,l),i=0;i<s.length;++i){var c,u,d=s[i],p=d.indexOf("]="),m=-1===p?d.indexOf("="):p+1;-1===m?(c=n.decoder(d,a.decoder),u=n.strictNullHandling?null:""):(c=n.decoder(d.slice(0,m),a.decoder),u=n.decoder(d.slice(m+1),a.decoder)),t.call(o,c)?o[c]=[].concat(o[c]).concat(u):o[c]=u}return o}(o,l):o,i=l.plainObjects?Object.create(null):{},c=Object.keys(s),u=0;u<c.length;++u){var d=c[u],p=n(d,s[d],l);i=e.merge(i,p,l)}return e.compact(i)}}const Un=hn(function(){if(Rn)return Ln;Rn=1;var e=function(){if(Fn)return On;Fn=1;var e=Hn(),t=qn(),a={brackets:function(e){return e+"[]"},indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},n=Array.isArray,o=Array.prototype.push,r=function(e,t){o.apply(e,n(t)?t:[t])},l=Date.prototype.toISOString,s={delimiter:"&",encode:!0,encoder:e.encode,encodeValuesOnly:!1,serializeDate:function(e){return l.call(e)},skipNulls:!1,strictNullHandling:!1},i=function t(a,o,l,i,c,u,d,p,m,v,f,h){var g=a;if("function"==typeof d?g=d(o,g):g instanceof Date&&(g=v(g)),null===g){if(i)return u&&!h?u(o,s.encoder):o;g=""}if("string"==typeof g||"number"==typeof g||"boolean"==typeof g||e.isBuffer(g))return u?[f(h?o:u(o,s.encoder))+"="+f(u(g,s.encoder))]:[f(o)+"="+f(String(g))];var y,_=[];if(void 0===g)return _;if(n(d))y=d;else{var w=Object.keys(g);y=p?w.sort(p):w}for(var b=0;b<y.length;++b){var k=y[b];c&&null===g[k]||(n(g)?r(_,t(g[k],l(o,k),l,i,c,u,d,p,m,v,f,h)):r(_,t(g[k],o+(m?"."+k:"["+k+"]"),l,i,c,u,d,p,m,v,f,h)))}return _};return On=function(o,l){var c=o,u=l?e.assign({},l):{};if(null!==u.encoder&&void 0!==u.encoder&&"function"!=typeof u.encoder)throw new TypeError("Encoder has to be a function.");var d=void 0===u.delimiter?s.delimiter:u.delimiter,p="boolean"==typeof u.strictNullHandling?u.strictNullHandling:s.strictNullHandling,m="boolean"==typeof u.skipNulls?u.skipNulls:s.skipNulls,v="boolean"==typeof u.encode?u.encode:s.encode,f="function"==typeof u.encoder?u.encoder:s.encoder,h="function"==typeof u.sort?u.sort:null,g=void 0!==u.allowDots&&u.allowDots,y="function"==typeof u.serializeDate?u.serializeDate:s.serializeDate,_="boolean"==typeof u.encodeValuesOnly?u.encodeValuesOnly:s.encodeValuesOnly;if(void 0===u.format)u.format=t.default;else if(!Object.prototype.hasOwnProperty.call(t.formatters,u.format))throw new TypeError("Unknown format option provided.");var w,b,k=t.formatters[u.format];"function"==typeof u.filter?c=(b=u.filter)("",c):n(u.filter)&&(w=b=u.filter);var x,C=[];if("object"!=typeof c||null===c)return"";x=u.arrayFormat in a?u.arrayFormat:"indices"in u?u.indices?"indices":"repeat":"indices";var V=a[x];w||(w=Object.keys(c)),h&&w.sort(h);for(var E=0;E<w.length;++E){var N=w[E];m&&null===c[N]||r(C,i(c[N],N,V,p,m,v?f:null,b,h,g,y,k,_))}var S=C.join(d),B=!0===u.addQueryPrefix?"?":"";return S.length>0?B+S:""}}(),t=Qn(),a=qn();return Ln={formats:a,parse:t,stringify:e}}()),Wn=(()=>{let e="https://localhost:9002";if(ya){const{miniProgram:{envVersion:t}}=uni.getAccountInfoSync();switch(t){case"develop":case"trial":case"release":e="https://ukw0y1.laf.run"}}return e})(),Kn={invoke(e){if(e.query){const t=Un.stringify(e.query);e.url.includes("?")?e.url+=`&${t}`:e.url+=`?${t}`}e.url.startsWith("http")||(e.url=Wn+e.url),e.timeout=1e4,e.header=__spreadValues({platform:"app"},e.header);const t=En(),{token:a}=t.userInfo;a&&(e.header.Authorization=`Bearer ${a}`)}},Yn={install(){uni.addInterceptor("request",Kn),uni.addInterceptor("uploadFile",Kn)}},Xn={install(){"function"!=typeof Array.prototype.at&&(Array.prototype.at=function(e){return e<0?this[this.length+e]:e>=this.length?void 0:this[e]})}},Jn="__TOAST_OPTION__",Zn={duration:2e3,show:!1},eo={success:()=>'<svg width="42px" height="42px" viewBox="0 0 42 42" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>成功</title><desc>Created with Sketch.</desc><defs><filter x="-63.2%" y="-80.0%" width="226.3%" height="260.0%" filterUnits="objectBoundingBox" id="filter-1"><feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset><feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur><feColorMatrix values="0 0 0 0 0.122733141   0 0 0 0 0.710852582   0 0 0 0 0.514812768  0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix><feMerge><feMergeNode in="shadowMatrixOuter1"></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter><rect id="path-2" x="3.4176226" y="5.81442199" width="3" height="8.5" rx="1.5"></rect><linearGradient x1="50%" y1="0.126649064%" x2="50%" y2="100%" id="linearGradient-4"><stop stop-color="#ACFFBD" stop-opacity="0.208123907" offset="0%"></stop><stop stop-color="#10B87C" offset="100%"></stop></linearGradient></defs><g id="规范" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="反馈-轻提示" transform="translate(-388.000000, -538.000000)"><g id="成功" transform="translate(388.000000, 538.000000)"><circle id="Oval" fill="#34D19D" opacity="0.400000006" cx="21" cy="21" r="20"></circle><circle id="Oval" fill="#34D19D" cx="21" cy="21" r="16"></circle><g id="Group-6" filter="url(#filter-1)" transform="translate(11.500000, 14.000000)"><mask id="mask-3" fill="white"><use xlink:href="#path-2"></use></mask><use id="Rectangle-Copy-24" fill="#C4FFEB" transform="translate(4.917623, 10.064422) rotate(-45.000000) translate(-4.917623, -10.064422) " xlink:href="#path-2"></use><rect id="Rectangle" fill="url(#linearGradient-4)" mask="url(#mask-3)" transform="translate(6.215869, 11.372277) rotate(-45.000000) translate(-6.215869, -11.372277) " x="4.71586891" y="9.52269089" width="3" height="3.69917136"></rect><rect id="Rectangle" fill="#FFFFFF" transform="translate(11.636236, 7.232744) scale(1, -1) rotate(-45.000000) translate(-11.636236, -7.232744) " x="10.1362361" y="-1.02185365" width="3" height="16.5091951" rx="1.5"></rect></g></g></g></g></svg>',warning:()=>'<svg width="42px" height="42px" viewBox="0 0 42 42" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>警告</title><desc>Created with Sketch.</desc> <defs> <filter x="-240.0%" y="-60.0%" width="580.0%" height="220.0%" filterUnits="objectBoundingBox" id="filter-1"><feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset><feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur><feColorMatrix values="0 0 0 0 0.824756567   0 0 0 0 0.450356612   0 0 0 0 0.168550194  0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix><feMerge><feMergeNode in="shadowMatrixOuter1"></feMergeNode> <feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs><g id="规范" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="反馈-轻提示" transform="translate(-580.000000, -538.000000)"> <g id="警告" transform="translate(580.000000, 538.000000)"><circle id="Oval" fill="#F0883A" opacity="0.400000006" cx="21" cy="21" r="20"></circle><circle id="Oval" fill="#F0883A" cx="21" cy="21" r="16"></circle><g id="Group-6" filter="url(#filter-1)" transform="translate(18.500000, 10.800000)"><rect id="Rectangle" fill="#FFFFFF" transform="translate(2.492935, 7.171583) scale(1, -1) rotate(-360.000000) translate(-2.492935, -7.171583) " x="0.992934699" y="0.955464537" width="3" height="12.4322365" rx="1.5"></rect><rect id="Rectangle-Copy-25" fill="#FFDEC5" transform="translate(2.508751, 17.202636) scale(1, -1) rotate(-360.000000) translate(-2.508751, -17.202636) " x="1.00875134" y="15.200563" width="3" height="4.00414639" rx="1.5"></rect></g></g></g></g></svg>',info:()=>'<svg width="42px" height="42px" viewBox="0 0 42 42" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>常规</title><desc>Created with Sketch.</desc><defs><filter x="-300.0%" y="-57.1%" width="700.0%" height="214.3%" filterUnits="objectBoundingBox" id="filter-1"><feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset><feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur><feColorMatrix values="0 0 0 0 0.362700096   0 0 0 0 0.409035039   0 0 0 0 0.520238904  0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix><feMerge><feMergeNode in="shadowMatrixOuter1"></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs><g id="规范" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="反馈-轻提示" transform="translate(-772.000000, -538.000000)"><g id="常规" transform="translate(772.000000, 538.000000)"><circle id="Oval" fill="#909CB7" opacity="0.4" cx="21" cy="21" r="20"></circle><circle id="Oval" fill="#909CB7" cx="21" cy="21" r="16"></circle><g id="Group-6" filter="url(#filter-1)" transform="translate(18.500000, 9.800000)"><g id="编组-2" transform="translate(2.492935, 10.204709) rotate(-180.000000) translate(-2.492935, -10.204709) translate(0.992935, 0.204709)"><rect id="Rectangle" fill="#FFFFFF" transform="translate(1.500000, 7.000000) scale(1, -1) rotate(-360.000000) translate(-1.500000, -7.000000) " x="0" y="0" width="3" height="14" rx="1.5"></rect><rect id="Rectangle-Copy-25" fill="#EEEEEE" transform="translate(1.500000, 18.000000) scale(1, -1) rotate(-360.000000) translate(-1.500000, -18.000000) " x="0" y="16" width="3" height="4" rx="1.5"></rect></g></g></g></g></g></svg>',error:()=>'<svg width="42px" height="42px" viewBox="0 0 42 42" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>toast</title><desc>Created with Sketch.</desc><defs><linearGradient x1="99.6229896%" y1="50.3770104%" x2="0.377010363%" y2="50.3770104%" id="linearGradient-1"><stop stop-color="#FFDFDF" offset="0%"></stop><stop stop-color="#F9BEBE" offset="100%"></stop></linearGradient><linearGradient x1="0.377010363%" y1="50.3770104%" x2="99.6229896%" y2="50.3770104%" id="linearGradient-2"><stop stop-color="#FFDFDF" offset="0%"></stop><stop stop-color="#F9BEBE" offset="100%"></stop></linearGradient></defs><g id="规范" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="反馈-轻提示" transform="translate(-196.000000, -538.000000)"> <g id="toast" transform="translate(196.000000, 538.000000)"><circle id="Oval" fill="#FA4350" opacity="0.400000006" cx="21" cy="21" r="20"></circle><circle id="Oval" fill="#FA4350" opacity="0.900000036" cx="21" cy="21" r="16"></circle><rect id="矩形" fill="#FFDFDF" transform="translate(21.071068, 21.071068) rotate(-225.000000) translate(-21.071068, -21.071068) " x="12.5710678" y="19.5710678" width="17" height="3" rx="1.5"></rect><rect id="矩形" fill="url(#linearGradient-1)" transform="translate(19.303301, 22.838835) rotate(-225.000000) translate(-19.303301, -22.838835) " x="17.3033009" y="21.3388348" width="4" height="3"></rect><rect id="矩形" fill="url(#linearGradient-2)" transform="translate(22.838835, 19.303301) rotate(-225.000000) translate(-22.838835, -19.303301) " x="20.8388348" y="17.8033009" width="4" height="3"></rect><rect id="矩形" fill="#FFFFFF" transform="translate(21.071068, 21.071068) rotate(-315.000000) translate(-21.071068, -21.071068) " x="12.5710678" y="19.5710678" width="17" height="3" rx="1.5"></rect></g></g></g></svg>'},to=__spreadProps(__spreadValues({},Ce),{selector:xe(""),msg:{type:String,default:""},direction:xe("horizontal"),iconName:{type:String,default:""},iconSize:Number,loadingType:xe("outline"),loadingColor:{type:String,default:"#4D80F0"},loadingSize:Number,iconColor:String,position:xe("middle-top"),zIndex:{type:Number,default:100},cover:{type:Boolean,default:!1},iconClass:{type:String,default:""},classPrefix:{type:String,default:"wd-icon"},opened:Function,closed:Function}),ao=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-toast",options:{addGlobalClass:!0,virtualHost:!0,styleIsolation:"shared"}}),{props:to,setup(t){const a=t,n=e.ref(""),o=e.ref(""),r=e.ref("middle"),l=e.ref(!1),s=e.ref(100),i=e.ref("outline"),c=e.ref("#4D80F0"),u=e.ref(),d=e.ref(),p=e.ref(""),m=e.ref(!1),v=e.ref("wd-icon"),f=e.ref(""),h=e.ref("horizontal");let g=null,y=null;const _=(w=a.selector)?`${Jn}${w}`:Jn;var w;const b=e.inject(_,e.ref(Zn));e.watch((()=>b.value),(e=>{var t;t=e,l.value=!!Z(t.show)&&t.show,l.value&&function(e,t){n.value=Z(e.iconName)?e.iconName:t.iconName,f.value=Z(e.iconClass)?e.iconClass:t.iconClass,o.value=Z(e.msg)?e.msg:t.msg,r.value=Z(e.position)?e.position:t.position,s.value=Z(e.zIndex)?e.zIndex:t.zIndex,i.value=Z(e.loadingType)?e.loadingType:t.loadingType,c.value=Z(e.loadingColor)?e.loadingColor:t.loadingColor,u.value=Z(e.iconSize)?Y(e.iconSize):Z(t.iconSize)?Y(t.iconSize):void 0,d.value=Z(e.loadingSize)?Y(e.loadingSize):Z(t.loadingSize)?Y(t.loadingSize):void 0,m.value=Z(e.cover)?e.cover:t.cover,v.value=Z(e.classPrefix)?e.classPrefix:t.classPrefix,h.value=Z(e.direction)?e.direction:t.direction,y=le(e.closed)?e.closed:le(t.closed)?t.closed:null,g=le(e.opened)?e.opened:le(t.opened)?t.opened:null}(t,a)}),{deep:!0,immediate:!0}),e.watch((()=>n.value),(()=>{N()}),{deep:!0,immediate:!0});const k=e.computed((()=>de({"z-index":s.value,position:"fixed",top:"50%",left:0,width:"100%",transform:"translate(0, -50%)","text-align":"center","pointer-events":"none"}))),x=e.computed((()=>`wd-toast ${a.customClass} wd-toast--${r.value} ${"loading"===n.value&&!o.value||!n.value&&!f.value?"":"wd-toast--with-icon"} ${"loading"!==n.value||o.value?"":"wd-toast--loading"} ${"vertical"===h.value?"is-vertical":""}`)),C=e.computed((()=>{const e={backgroundImage:`url(${p.value})`};return Z(u.value)&&(e.width=u.value,e.height=u.value),de(e)}));function V(){le(g)&&g()}function E(){le(y)&&y()}function N(){if("success"!==n.value&&"warning"!==n.value&&"info"!==n.value&&"error"!==n.value)return;const e=`"data:image/svg+xml;base64,${kt(eo[n.value]())}"`;p.value=e}return e.onBeforeMount((()=>{N()})),(t,a)=>(e.openBlock(),e.createElementBlock(e.Fragment,null,[m.value?(e.openBlock(),e.createBlock(Tt,{key:0,"z-index":s.value,"lock-scroll":"",show:l.value,"custom-style":"background-color: transparent;pointer-events: auto;"},null,8,["z-index","show"])):e.createCommentVNode("",!0),e.createVNode(Pt,{name:"fade",show:l.value,"custom-style":k.value,onAfterEnter:V,onAfterLeave:E},{default:e.withCtx((()=>[e.createElementVNode("view",{class:e.normalizeClass(x.value)},["loading"===n.value?(e.openBlock(),e.createBlock(Ct,{key:0,type:i.value,color:c.value,size:d.value,"custom-class":"wd-toast__icon "+("vertical"===h.value?"is-vertical":"")},null,8,["type","color","size","custom-class"])):"success"===n.value||"warning"===n.value||"info"===n.value||"error"===n.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:e.normalizeClass("wd-toast__iconWrap wd-toast__icon "+("vertical"===h.value?"is-vertical":""))},[e.createElementVNode("view",{class:"wd-toast__iconBox"},[e.createElementVNode("view",{class:"wd-toast__iconSvg",style:e.normalizeStyle(C.value)},null,4)])],2)):f.value?(e.openBlock(),e.createBlock(Ne,{key:2,"custom-class":"wd-toast__icon "+("vertical"===h.value?"is-vertical":""),size:u.value,"class-prefix":v.value,name:f.value},null,8,["custom-class","size","class-prefix","name"])):e.createCommentVNode("",!0),o.value?(e.openBlock(),e.createElementBlock("view",{key:3,class:"wd-toast__msg"},e.toDisplayString(o.value),1)):e.createCommentVNode("",!0)],2)])),_:1},8,["show","custom-style"])],64))}})),[["__scopeId","data-v-d24ac773"]]),no=__spreadProps(__spreadValues({},Ce),{selector:xe("")}),oo="__MESSAGE_OPTION__",ro={title:"",showCancelButton:!1,show:!1,closeOnClickModal:!0,msg:"",type:"alert",inputType:"text",inputValue:"",showErr:!1,zIndex:99,lazyRender:!0,inputError:""},lo=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-message-box",options:{virtualHost:!0,addGlobalClass:!0,styleIsolation:"shared"}}),{props:no,setup(t){const a=t,{translate:n}=ft("message-box"),o=e.computed((()=>`wd-message-box__container ${a.customClass}`)),r=e.computed((()=>`wd-message-box__body ${c.title?"":"is-no-title"} ${"prompt"===c.type?"is-prompt":""}`)),l=(s=a.selector)?`${oo}${s}`:oo;var s;const i=e.inject(l,e.ref(ro)),c=e.reactive({msg:"",show:!1,title:"",showCancelButton:!1,closeOnClickModal:!0,confirmButtonText:"",cancelButtonText:"",type:"alert",inputType:"text",inputValue:"",inputPlaceholder:"",inputError:"",showErr:!1,zIndex:99,lazyRender:!0}),u=e.computed((()=>{const e=ve({block:!0},Z(c.confirmButtonProps)?ge(c.confirmButtonProps,ue):{});return e.customClass=`${e.customClass||""} wd-message-box__actions-btn`,e})),d=e.computed((()=>{const e=ve({block:!0,type:"info"},Z(c.cancelButtonProps)?ge(c.cancelButtonProps,ue):{});return e.customClass=`${e.customClass||""} wd-message-box__actions-btn`,e}));function p(e){if(("modal"!==e||c.closeOnClickModal)&&("prompt"!==c.type||"confirm"!==e||function(){if(c.inputPattern&&!c.inputPattern.test(String(c.inputValue)))return c.showErr=!0,!1;if("function"==typeof c.inputValidate){if(!c.inputValidate(c.inputValue))return c.showErr=!0,!1}return c.showErr=!1,!0}()))switch(e){case"confirm":c.beforeConfirm?c.beforeConfirm({resolve:t=>{t&&m({action:e,value:c.inputValue})}}):m({action:e,value:c.inputValue});break;case"cancel":v({action:e});break;default:v({action:"modal"})}}function m(e){c.show=!1,le(c.success)&&c.success(e)}function v(e){c.show=!1,le(c.fail)&&c.fail(e)}function f({value:e}){""!==e?c.inputValue=e:c.showErr=!1}return e.watch((()=>i.value),(e=>{var t;(t=e)&&(c.title=Z(t.title)?t.title:"",c.showCancelButton=!!Z(t.showCancelButton)&&t.showCancelButton,c.show=t.show,c.closeOnClickModal=t.closeOnClickModal,c.confirmButtonText=t.confirmButtonText,c.cancelButtonText=t.cancelButtonText,c.msg=t.msg,c.type=t.type,c.inputType=t.inputType,c.inputSize=t.inputSize,c.inputValue=t.inputValue,c.inputPlaceholder=t.inputPlaceholder,c.inputPattern=t.inputPattern,c.inputValidate=t.inputValidate,c.success=t.success,c.fail=t.fail,c.beforeConfirm=t.beforeConfirm,c.inputError=t.inputError,c.showErr=t.showErr,c.zIndex=t.zIndex,c.lazyRender=t.lazyRender,c.confirmButtonProps=t.confirmButtonProps,c.cancelButtonProps=t.cancelButtonProps)}),{deep:!0,immediate:!0}),e.watch((()=>c.show),(e=>{!1===!!e&&(c.showErr=!1)}),{deep:!0,immediate:!0}),(t,a)=>(e.openBlock(),e.createElementBlock("view",null,[e.createVNode(Dt,{transition:"zoom-in",modelValue:c.show,"onUpdate:modelValue":a[3]||(a[3]=e=>c.show=e),"close-on-click-modal":c.closeOnClickModal,"lazy-render":c.lazyRender,"custom-class":"wd-message-box",onClickModal:a[4]||(a[4]=e=>p("modal")),"z-index":c.zIndex,duration:200},{default:e.withCtx((()=>[e.createElementVNode("view",{class:e.normalizeClass(o.value)},[e.createElementVNode("view",{class:e.normalizeClass(r.value)},[c.title?(e.openBlock(),e.createElementBlock("view",{key:0,class:"wd-message-box__title"},e.toDisplayString(c.title),1)):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"wd-message-box__content"},["prompt"===c.type?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createVNode(zt,{modelValue:c.inputValue,"onUpdate:modelValue":a[0]||(a[0]=e=>c.inputValue=e),type:c.inputType,size:c.inputSize,placeholder:c.inputPlaceholder,onInput:f},null,8,["modelValue","type","size","placeholder"]),c.showErr?(e.openBlock(),e.createElementBlock("view",{key:0,class:"wd-message-box__input-error"},e.toDisplayString(c.inputError||e.unref(n)("inputNoValidate")),1)):e.createCommentVNode("",!0)],64)):e.createCommentVNode("",!0),e.renderSlot(t.$slots,"default",{},(()=>[e.createTextVNode(e.toDisplayString(c.msg),1)]),!0)])],2),e.createElementVNode("view",{class:e.normalizeClass("wd-message-box__actions "+(c.showCancelButton?"wd-message-box__flex":"wd-message-box__block"))},[c.showCancelButton?(e.openBlock(),e.createBlock(Rt,e.mergeProps({key:0},d.value,{onClick:a[1]||(a[1]=e=>p("cancel"))}),{default:e.withCtx((()=>[e.createTextVNode(e.toDisplayString(c.cancelButtonText||e.unref(n)("cancel")),1)])),_:1},16)):e.createCommentVNode("",!0),e.createVNode(Rt,e.mergeProps(u.value,{onClick:a[2]||(a[2]=e=>p("confirm"))}),{default:e.withCtx((()=>[e.createTextVNode(e.toDisplayString(c.confirmButtonText||e.unref(n)("confirm")),1)])),_:1},16)],2)],2)])),_:3},8,["modelValue","close-on-click-modal","lazy-render","z-index"])]))}})),[["__scopeId","data-v-05f973d9"]]),so=Ee(e.defineComponent(__spreadProps(__spreadValues({},{name:"privacy-popup",options:{virtualHost:!0,addGlobalClass:!0,styleIsolation:"shared"}}),{props:{title:{default:"用户隐私保护提示"},desc:{default:"感谢您使用本应用，您使用本应用的服务之前请仔细阅读并同意"},subDesc:{default:"。当您点击同意并开始时用产品服务时，即表示你已理解并同息该条款内容，该条款将对您产生法律约束力。如您拒绝，将无法使用相应服务。"},protocol:{default:"《用户隐私保护指引》"}},emits:["agree","disagree"],setup(t,{emit:a}){const n=e.ref(!1),o=e.ref(new Set),r=e=>{n.value=!0,o.value.add(e)},l=a;function s(){n.value=!1,o.value.forEach((e=>{e({event:"agree",buttonId:"agree-btn"})})),o.value.clear(),l("agree")}function i(){n.value=!1,o.value.forEach((e=>{e({event:"disagree"})})),o.value.clear()}function c(){wx.openPrivacyContract({success:e=>{G("log","at components/privacy-popup/privacy-popup.vue:125","openPrivacyContract success")},fail:e=>{G("error","at components/privacy-popup/privacy-popup.vue:128","openPrivacyContract fail",e)}})}function u(){o.value.clear()}return e.onBeforeMount((()=>{wx.onNeedPrivacyAuthorization&&wx.onNeedPrivacyAuthorization((e=>{r(e)}))})),(t,a)=>{const o=L(e.resolveDynamicComponent("wd-popup"),Dt);return e.openBlock(),e.createElementBlock("view",null,[e.createVNode(o,{modelValue:e.unref(n),"onUpdate:modelValue":a[0]||(a[0]=t=>e.isRef(n)?n.value=t:null),"close-on-click-modal":!1,"custom-class":"wd-privacy-popup",onClose:u},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"wd-privacy-popup__header"},[e.createElementVNode("view",{class:"wd-picker__title"},e.toDisplayString(t.title),1)]),e.createElementVNode("view",{class:"wd-privacy-popup__container"},[e.createElementVNode("text",null,e.toDisplayString(t.desc),1),e.createElementVNode("text",{class:"wd-privacy-popup__container-protocol",onClick:c},e.toDisplayString(t.protocol),1),e.createElementVNode("text",null,e.toDisplayString(t.subDesc),1)]),e.createElementVNode("view",{class:"wd-privacy-popup__footer"},[e.createElementVNode("button",{class:"wd-privacy-popup__footer-disagree wd-button is-block is-round is-medium is-plain",id:"disagree-btn",onClick:i}," 拒绝 "),e.createElementVNode("button",{class:"wd-privacy-popup__footer-agree wd-button is-primary is-block is-round is-medium",id:"agree-btn","open-type":"agreePrivacyAuthorization",onAgreeprivacyauthorization:s}," 同意 ",32)])])),_:1},8,["modelValue"])])}}})),[["__scopeId","data-v-60f150da"]]),io=__spreadProps(__spreadValues({},Ce),{theme:xe("light"),themeVars:{type:Object,default:()=>({})}}),co=e.defineComponent(__spreadProps(__spreadValues({},{name:"wd-config-provider",options:{virtualHost:!0,addGlobalClass:!0,styleIsolation:"shared"}}),{props:io,setup(t){const a=t,n=e.computed((()=>`wot-theme-${a.theme} ${a.customClass}`)),o=e.computed((()=>{const e=r(a.themeVars);return e?`${de(e)}${a.customStyle}`:a.customStyle})),r=e=>{if(!e)return;const t={};return Object.keys(e).forEach((a=>{var n;t[`--wot-${n=a,(n=n.replace(n.charAt(0),n.charAt(0).toLocaleLowerCase())).replace(/([a-z])([A-Z])/g,((e,t,a)=>t+"-"+a.toLowerCase()))}`]=e[a]})),t};return(t,a)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(n.value),style:e.normalizeStyle(o.value)},[e.renderSlot(t.$slots,"default")],6))}})),uo=Ee(e.defineComponent({__name:"default",setup(t){e.ref("开发中！！");const a={},n=yt();e.provide("safeArea",n);const o=e.computed((()=>({})));return(t,n)=>{const r=L(e.resolveDynamicComponent("wd-toast"),ao),l=L(e.resolveDynamicComponent("wd-message-box"),lo),s=L(e.resolveDynamicComponent("privacy-popup"),so),i=L(e.resolveDynamicComponent("wd-config-provider"),co);return e.openBlock(),e.createBlock(i,{themeVars:a},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"global-layout",style:e.normalizeStyle(e.unref(o))},[e.renderSlot(t.$slots,"default",{},void 0,!0)],4),e.createVNode(r),e.createVNode(l),e.createVNode(s)])),_:3})}}}),[["__scopeId","data-v-84b6ca8f"]]);const{app:po,Vuex:mo,Pinia:vo}=function(){const t=e.createVueApp(yn);return t.use(Nn),t.use(In),t.use(Yn),t.use(Xn),t.use(vn),t.component("layout-default-uni",uo),{app:t}}();uni.Vuex=mo,uni.Pinia=vo,po.provide("__globalStyles",__uniConfig.styles),po._component.mpType="app",po._component.render=()=>{},po.mount("#app")}(Vue);
