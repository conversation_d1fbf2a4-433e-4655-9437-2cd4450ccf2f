
  ;(function(){
  let u=void 0,isReady=false,onReadyCallbacks=[],isServiceReady=false,onServiceReadyCallbacks=[];
  const __uniConfig = {"pages":[],"globalStyle":{"backgroundColor":"#FFFFFF","navigationBar":{"backgroundColor":"#f8f8f8","titleText":"invoice-book-uniapp","style":"default","type":"default","titleColor":"#000000"},"isNVue":false},"nvue":{"compiler":"uni-app","styleCompiler":"uni-app","flex-direction":"column"},"renderer":"auto","appname":"invoice-book-uniapp","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":true},"compilerVersion":"4.66","entryPagePath":"pages/user/index","entryPageQuery":"","realEntryPagePath":"","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"locales":{},"darkmode":false,"themeConfig":{}};
  const __uniRoutes = [{"path":"pages/user/index","meta":{"isQuit":true,"isEntry":true,"navigationBar":{"titleText":"主页","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/index/index","meta":{"navigationBar":{"titleText":"主页","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/merchants/goods","meta":{"navigationBar":{"titleText":"商品管理","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/merchants/index","meta":{"navigationBar":{"titleText":"主页","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/merchants/mine","meta":{"navigationBar":{"titleText":"我的","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/merchants/orders","meta":{"navigationBar":{"titleText":"订单","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/merchants/statistics","meta":{"navigationBar":{"titleText":"统计","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/sys/dashboard","meta":{"navigationBar":{"titleText":"仪表盘","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/sys/goods","meta":{"navigationBar":{"titleText":"商品管理","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/sys/merchants","meta":{"navigationBar":{"titleText":"商户管理","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/sys/mine","meta":{"navigationBar":{"titleText":"我的","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/sys/system","meta":{"navigationBar":{"titleText":"系统管理","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/user/category","meta":{"navigationBar":{"titleText":"分类","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/user/goods-detail","meta":{"navigationBar":{"titleText":"商品详情","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/user/mine","meta":{"navigationBar":{"titleText":"我的","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/user/search","meta":{"navigationBar":{"titleText":"搜索","style":"custom","type":"default"},"isNVue":false}}].map(uniRoute=>(uniRoute.meta.route=uniRoute.path,__uniConfig.pages.push(uniRoute.path),uniRoute.path='/'+uniRoute.path,uniRoute));
  __uniConfig.styles=[];//styles
  __uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  __uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:16})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:u,window:u,document:u,frames:u,self:u,location:u,navigator:u,localStorage:u,history:u,Caches:u,screen:u,alert:u,confirm:u,prompt:u,fetch:u,XMLHttpRequest:u,WebSocket:u,webkit:u,print:u}}}}); 
  })();
  