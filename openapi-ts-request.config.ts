import type { GenerateServiceProps } from 'openapi-ts-request'

export default [
  {
    // schemaPath: 'https://amd2g7rffe.apifox.cn/303351428e0',
    schemaPath: './openapi.json',
    serversPath: './src/service/app/merchants',
    requestLibPath: `import request from '@/utils/request';\n import { CustomRequestOptions } from '@/interceptors/request';`,
    requestOptionsType: 'CustomRequestOptions',
    isGenReactQuery: true,
    reactQueryMode: 'vue',
    isGenJavaScript: false,
    isTranslateToEnglishTag: true,
    isOnlyGenTypeScriptType: true,
  },
] as GenerateServiceProps[]
